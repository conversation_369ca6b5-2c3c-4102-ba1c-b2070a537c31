<template>
  <div class="dynamic-form-item">
    <label class="param-label">
      {{ paramConfig.label || paramName }}
      <span v-if="paramConfig.unit" class="param-unit">({{ paramConfig.unit }})</span>
      <el-tooltip v-if="paramConfig.tooltip" :content="paramConfig.tooltip" placement="top">
        <el-icon class="tooltip-icon"><QuestionFilled /></el-icon>
      </el-tooltip>
    </label>
    
    <!-- 数值类型 -->
    <div v-if="paramConfig.type === 'number'" class="number-input">
      <el-slider
        v-if="paramConfig.range"
        :model-value="displayValue"
        @update:model-value="onNumberChange"
        :min="getSliderMin"
        :max="getSliderMax"
        :step="getSliderStep"
        :show-input="true"
        :show-input-controls="false"
      />
      <el-input-number
        v-else
        :model-value="modelValue"
        @update:model-value="$emit('update:modelValue', $event)"
        :min="paramConfig.range?.min"
        :max="paramConfig.range?.max"
        :step="paramConfig.range?.step || 1"
        style="width: 100%"
      />
    </div>
    
    <!-- 文本类型 -->
    <el-input
      v-else-if="paramConfig.type === 'text'"
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
      :placeholder="paramConfig.placeholder || `请输入${paramConfig.label || paramName}`"
      :maxlength="paramConfig.maxLength"
      :show-word-limit="!!paramConfig.maxLength"
    />
    
    <!-- 选择类型 -->
    <el-select
      v-else-if="paramConfig.type === 'select'"
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
      :placeholder="paramConfig.placeholder || `请选择${paramConfig.label || paramName}`"
      style="width: 100%"
    >
      <el-option
        v-for="option in paramConfig.options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
    
    <!-- 开关类型 -->
    <el-switch
      v-else-if="paramConfig.type === 'switch'"
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 多行文本 -->
    <el-input
      v-else-if="paramConfig.type === 'textarea'"
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
      type="textarea"
      :rows="paramConfig.rows || 3"
      :placeholder="paramConfig.placeholder || `请输入${paramConfig.label || paramName}`"
      :maxlength="paramConfig.maxLength"
      :show-word-limit="!!paramConfig.maxLength"
    />
    
    <!-- 默认文本输入 -->
    <el-input
      v-else
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
      :placeholder="paramConfig.placeholder || `请输入${paramConfig.label || paramName}`"
    />
    
    <!-- 参数说明 -->
    <p v-if="paramConfig.tooltip" class="param-description">
      {{ paramConfig.tooltip }}
    </p>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  paramName: {
    type: String,
    required: true
  },
  paramConfig: {
    type: Object,
    required: true
  },
  modelValue: {
    type: [String, Number, Boolean, Array],
    default: undefined
  }
})

const emit = defineEmits(['update:modelValue'])

// 滑动条范围和步长的计算属性
const getSliderMin = computed(() => {
  if (props.paramConfig.range?.max === 1 && props.paramConfig.range?.min === 0) {
    return 0 // 百分比显示时最小值为0
  }
  return props.paramConfig.range?.min || 0
})

const getSliderMax = computed(() => {
  if (props.paramConfig.range?.max === 1 && props.paramConfig.range?.min === 0) {
    return 100 // 百分比显示时最大值为100
  }
  return props.paramConfig.range?.max || 100
})

const getSliderStep = computed(() => {
  if (props.paramConfig.range?.max === 1 && props.paramConfig.range?.min === 0) {
    // 百分比显示时，如果原始步长是0.01，则显示步长为1
    return (props.paramConfig.range?.step || 0.01) * 100
  }
  return props.paramConfig.range?.step || 1
})

// 对于滑块显示值的处理
const displayValue = computed(() => {
  if (props.paramConfig.type === 'number' && props.paramConfig.range) {
    // 如果是百分比显示（0-1的值转换为0-100）
    if (props.paramConfig.range.max === 1 && props.paramConfig.range.min === 0) {
      // 保持原始精度，不进行四舍五入
      return (props.modelValue || 0) * 100
    }
  }
  return props.modelValue
})

// 数值变化处理
const onNumberChange = (value) => {
  if (props.paramConfig.range) {
    // 如果是百分比显示（0-100转换为0-1）
    if (props.paramConfig.range.max === 1 && props.paramConfig.range.min === 0) {
      // 保持精度，使用parseFloat确保数值正确性
      const newValue = parseFloat((value / 100).toFixed(3))
      emit('update:modelValue', newValue)
      return
    }
  }
  emit('update:modelValue', value)
}
</script>

<style scoped>
.dynamic-form-item {
  margin-bottom: 16px;
}

.param-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.param-unit {
  font-weight: normal;
  color: #909399;
}

.tooltip-icon {
  margin-left: 4px;
  color: #909399;
  cursor: help;
  font-size: 14px;
}

.number-input {
  width: 100%;
}

.param-description {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}
</style>
