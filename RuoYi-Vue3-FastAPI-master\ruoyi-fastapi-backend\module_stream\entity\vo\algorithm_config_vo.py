from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, validator
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional, Dict, Any
from module_admin.annotation.pydantic_annotation import as_query


class SaveAlgorithmConfigModel(BaseModel):
    """
    保存算法配置模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    stream_id: int = Field(..., description='视频流ID')
    algorithm_id: str = Field(..., description='算法ID')
    algorithm_name: Optional[str] = Field(default='', description='算法名称')
    algorithm_version: Optional[str] = Field(default='1.0.0', description='算法版本')
    task_name: Optional[str] = Field(default='', description='任务名称')
    user_config: Dict[str, Any] = Field(..., description='用户配置参数')
    remark: Optional[str] = Field(default='', description='备注')

    @NotBlank(field_name='stream_id', message='视频流ID不能为空')
    def get_stream_id(self):
        return self.stream_id

    @NotBlank(field_name='algorithm_id', message='算法ID不能为空')
    def get_algorithm_id(self):
        return self.algorithm_id

    @validator('user_config')
    def validate_user_config(cls, v):
        """验证用户配置参数"""
        if not v:
            raise ValueError('用户配置参数不能为空')
        
        # 检查必要的配置字段
        required_fields = ['basicParams']
        missing_fields = []
        
        for field in required_fields:
            if field not in v:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f'配置缺少必要字段: {", ".join(missing_fields)}')
        
        return v

    @validator('task_name')
    def validate_task_name(cls, v, values):
        """验证任务名称，如果为空则生成默认名称"""
        if not v or v.strip() == '':
            # 使用算法名称和时间戳生成默认任务名称
            algorithm_name = values.get('algorithm_name', '未知算法')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return f'{algorithm_name}_{timestamp}'
        return v.strip()

    def validate_fields(self):
        """验证所有字段"""
        self.get_stream_id()
        self.get_algorithm_id()


class AlgorithmConfigQueryModel(BaseModel):
    """
    算法配置查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    stream_id: Optional[int] = Field(default=None, description='视频流ID')
    algorithm_id: Optional[str] = Field(default=None, description='算法ID')
    task_name: Optional[str] = Field(default=None, description='任务名称')
    status: Optional[str] = Field(default=None, description='状态')


@as_query
class AlgorithmConfigPageQueryModel(AlgorithmConfigQueryModel):
    """
    算法配置分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteAlgorithmConfigModel(BaseModel):
    """
    删除算法配置模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    config_ids: str = Field(description='需要删除的配置主键')
