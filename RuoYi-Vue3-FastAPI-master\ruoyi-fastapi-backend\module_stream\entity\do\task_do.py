from sqlalchemy import String, BigInteger, Column, JSON, DateTime, CHAR, Integer
from config.database import Base


class SurveillanceTask(Base):
    """
    检测任务表
    """

    __tablename__ = 'surveillance_task'

    task_id = Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment='任务ID')
    task_name = Column(String(100), nullable=False, comment='任务名称')
    stream_id = Column(BigInteger, nullable=False, comment='视频流ID')

    # 算法信息（从算法配置表合并过来）
    algorithm_id = Column(String(100), nullable=False, comment='算法ID')
    algorithm_name = Column(String(200), nullable=True, comment='算法名称')
    algorithm_version = Column(String(50), nullable=True, comment='算法版本')
    algorithm_type = Column(String(50), nullable=True, comment='算法类型（保留字段，与algorithm_id相同）')

    # 算法配置参数（标准化JSON格式）
    user_config = Column(JSON, nullable=True, comment='用户配置参数JSON')

    # 保留字段（兼容性）
    model_id = Column(BigInteger, nullable=True, comment='模型ID(保留字段)')
    config_id = Column(BigInteger, nullable=True, comment='配置ID(保留字段)')
    algorithm_config = Column(JSON, nullable=True, comment='算法配置参数(保留字段)')
    bbox_config = Column(JSON, nullable=True, comment='区域/线段配置(保留字段)')
    alert_config = Column(JSON, nullable=True, comment='告警配置(保留字段)')
    schedule_config = Column(JSON, nullable=True, comment='调度配置(保留字段)')
    status = Column(CHAR(1), nullable=True, comment='状态(0停止 1运行 2暂停)')
    last_run_time = Column(DateTime, nullable=True, comment='最后运行时间')
    next_run_time = Column(DateTime, nullable=True, comment='下次运行时间(保留字段)')
    run_count = Column(Integer, nullable=True, comment='运行次数')
    alert_count = Column(Integer, nullable=True, comment='告警次数')
    error_count = Column(Integer, nullable=True, comment='错误次数')
    del_flag = Column(CHAR(1), nullable=True, comment='删除标志（0代表存在 2代表删除）')
    create_by = Column(String(64), nullable=True, comment='创建者')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(64), nullable=True, comment='更新者')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
    remark = Column(String(500), nullable=True, comment='备注')
