/**
 * 算法配置验证工具
 * 提供通用的算法配置验证功能，可在所有算法配置组件中复用
 */

/**
 * 验证基本配置信息
 * @param {Object} config 配置对象
 * @param {number} streamId 视频流ID
 * @param {string} selectedAlgorithm 选中的算法ID
 * @param {string} taskName 任务名称
 * @returns {Array} 错误信息数组
 */
export function validateBasicConfig(config) {
  const errors = []
  
  // 验证视频流ID
  if (!config.streamId) {
    errors.push('请选择视频流')
  }
  
  // 验证算法选择
  if (!config.selectedAlgorithm) {
    errors.push('请选择算法')
  }
  
  // 验证任务名称
  if (!config.taskName || config.taskName.trim() === '') {
    errors.push('请输入任务名称')
  } else {
    const taskNameValidation = validateTaskName(config.taskName)
    if (!taskNameValidation.isValid) {
      errors.push(taskNameValidation.error)
    }
  }
  
  return errors
}

/**
 * 验证任务名称
 * @param {string} taskName 任务名称
 * @returns {Object} 验证结果 { isValid: boolean, error: string }
 */
export function validateTaskName(taskName) {
  if (!taskName || taskName.trim() === '') {
    return { isValid: false, error: '任务名称不能为空' }
  }
  
  const trimmedName = taskName.trim()
  
  if (trimmedName.length < 2) {
    return { isValid: false, error: '任务名称至少需要2个字符' }
  }
  
  if (trimmedName.length > 50) {
    return { isValid: false, error: '任务名称不能超过50个字符' }
  }
  
  // 检查特殊字符
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(trimmedName)) {
    return { isValid: false, error: '任务名称不能包含特殊字符 < > : " / \\ | ? *' }
  }
  
  return { isValid: true, error: '' }
}

/**
 * 验证模型参数
 * @param {Object} modelParams 模型参数对象
 * @returns {Array} 错误信息数组
 */
export function validateModelParameters(modelParams) {
  const errors = []
  
  if (!modelParams) {
    return errors
  }
  
  // 验证置信度阈值
  if (typeof modelParams.confidence_threshold === 'number') {
    if (modelParams.confidence_threshold < 0 || modelParams.confidence_threshold > 1) {
      errors.push('置信度阈值必须在0-1之间')
    }
  }
  
  // 验证NMS阈值
  if (typeof modelParams.nms_threshold === 'number') {
    if (modelParams.nms_threshold < 0 || modelParams.nms_threshold > 1) {
      errors.push('NMS阈值必须在0-1之间')
    }
  }
  
  // 验证输入尺寸
  if (typeof modelParams.input_size === 'number') {
    if (modelParams.input_size <= 0 || modelParams.input_size > 2048) {
      errors.push('输入图像尺寸必须在1-2048之间')
    }
  }
  
  // 验证最大检测数量
  if (typeof modelParams.max_detections === 'number') {
    if (modelParams.max_detections <= 0 || modelParams.max_detections > 1000) {
      errors.push('最大检测数量必须在1-1000之间')
    }
  }
  
  return errors
}

/**
 * 验证告警参数
 * @param {Object} alertParams 告警参数对象
 * @returns {Array} 错误信息数组
 */
export function validateAlertParameters(alertParams) {
  const errors = []
  
  if (!alertParams) {
    return errors
  }
  
  // 验证告警间隔
  if (typeof alertParams.alert_interval === 'number') {
    if (alertParams.alert_interval < 1 || alertParams.alert_interval > 3600) {
      errors.push('告警间隔必须在1-3600秒之间')
    }
  }
  
  // 验证告警阈值
  if (typeof alertParams.alert_threshold === 'number') {
    if (alertParams.alert_threshold < 1 || alertParams.alert_threshold > 100) {
      errors.push('告警阈值必须在1-100之间')
    }
  }
  
  // 验证告警消息
  if (alertParams.alert_message && alertParams.alert_message.length > 200) {
    errors.push('告警消息不能超过200个字符')
  }
  
  return errors
}

/**
 * 验证几何配置（多边形、线段等）
 * @param {Object} config 配置对象
 * @param {Object} algorithmCapability 算法能力对象
 * @returns {Array} 错误信息数组
 */
export function validateGeometryConfig(config, algorithmCapability) {
  const errors = []
  
  if (!algorithmCapability) {
    return errors
  }
  
  // 验证多边形检测区域
  if (config.detection_areas && config.detection_areas.length > 0) {
    if (!algorithmCapability.supports_polygons) {
      errors.push('该算法不支持多边形检测区域')
    } else {
      // 验证每个多边形
      config.detection_areas.forEach((area, index) => {
        if (!area.points || area.points.length < 3) {
          errors.push(`检测区域${index + 1}至少需要3个点`)
        }
      })
    }
  }
  
  // 验证检测线段
  if (config.detection_lines && config.detection_lines.length > 0) {
    if (!algorithmCapability.supports_lines) {
      errors.push('该算法不支持线段检测')
    } else {
      // 验证每个线段
      config.detection_lines.forEach((line, index) => {
        if (!line.points || line.points.length !== 2) {
          errors.push(`检测线段${index + 1}必须有2个点`)
        }
      })
    }
  }
  
  // 验证排除区域
  if (config.exclusion_areas && config.exclusion_areas.length > 0) {
    if (!algorithmCapability.supports_exclusion) {
      errors.push('该算法不支持排除区域')
    }
  }
  
  return errors
}

/**
 * 完整的算法配置验证
 * @param {Object} config 完整的配置对象
 * @param {Object} algorithmCapability 算法能力对象
 * @returns {Object} 验证结果 { isValid: boolean, errors: Array }
 */
export function validateAlgorithmConfig(config, algorithmCapability) {
  const allErrors = []
  
  // 基本配置验证
  const basicErrors = validateBasicConfig(config)
  allErrors.push(...basicErrors)
  
  // 模型参数验证
  if (config.model_params) {
    const modelErrors = validateModelParameters(config.model_params)
    allErrors.push(...modelErrors)
  }
  
  // 告警参数验证
  if (config.alert_params) {
    const alertErrors = validateAlertParameters(config.alert_params)
    allErrors.push(...alertErrors)
  }
  
  // 几何配置验证
  const geometryErrors = validateGeometryConfig(config, algorithmCapability)
  allErrors.push(...geometryErrors)
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  }
}

/**
 * 生成默认任务名称
 * @param {string} algorithmName 算法名称
 * @param {string} algorithmId 算法ID
 * @returns {string} 默认任务名称
 */
export function generateDefaultTaskName(algorithmName, algorithmId) {
  const name = algorithmName || algorithmId || '未知算法'
  const now = new Date()

  // 生成更精确的时间戳，包含秒数和随机数
  const timestamp = now.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/[\/\s:]/g, '')

  // 添加随机数确保唯一性
  const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0')

  return `${name}检测任务_${timestamp}_${randomSuffix}`
}

/**
 * 格式化验证错误信息
 * @param {Array} errors 错误数组
 * @returns {string} 格式化后的错误信息
 */
export function formatValidationErrors(errors) {
  if (!errors || errors.length === 0) {
    return ''
  }
  
  return errors.join('；')
}
