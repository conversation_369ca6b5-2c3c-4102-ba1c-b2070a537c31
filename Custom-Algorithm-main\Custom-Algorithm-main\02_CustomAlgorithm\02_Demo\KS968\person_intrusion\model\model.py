"""
通用PyTorch适配的RknnModel基类
适用于所有智驱力算法，完全替代RKNN，保持原有逻辑不变
"""

import cv2
import numpy as np

# 导入必要的库
import os

import torch
import sys
from pathlib import Path

# 导入正确的Logger
from logger import LOGGER

# 添加YOLOv5路径 - 使用yolov5-master
yolov5_absolute_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master"
if os.path.exists(yolov5_absolute_path):
    sys.path.insert(0, yolov5_absolute_path)
    LOGGER.info(f"添加YOLOv5路径: {yolov5_absolute_path}")
else:
    LOGGER.warning(f"YOLOv5路径不存在: {yolov5_absolute_path}")

class RknnModel:
    """
    通用PyTorch适配的RknnModel基类
    完全替代RKNN，保持智驱力原有接口和逻辑不变
    """
    def __init__(self, acc_id, name, conf, model_names):
        self.acc_id = acc_id
        self.name = name
        self.conf = conf
        self.model_names = model_names
        self.models = {}
        self.status = False
        # 自动选择设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self._load_models()
        
        # 调用子类的参数加载方法
        if hasattr(self, '_load_args'):
            success = self._load_args(conf)
            if not success:
                LOGGER.error("参数加载失败")
                self.status = False

    def _load_models(self):
        """使用torch.hub本地模式加载YOLOv5模型 - 生产环境真实模型加载"""
        try:
            for model_name in self.model_names:
                LOGGER.info(f"加载生产环境YOLOv5模型: {model_name}")

                # 根据算法类型选择合适的权重文件
                if 'person' in self.name.lower():
                    weights_path = os.path.join(yolov5_absolute_path, "yolov5s.pt")
                elif 'car' in self.name.lower() or 'vehicle' in self.name.lower():
                    weights_path = os.path.join(yolov5_absolute_path, "yolov5m.pt")
                    # 如果yolov5m不存在，回退到yolov5s
                    if not os.path.exists(weights_path):
                        weights_path = os.path.join(yolov5_absolute_path, "yolov5s.pt")
                else:
                    weights_path = os.path.join(yolov5_absolute_path, "yolov5s.pt")

                LOGGER.info(f"使用权重文件: {weights_path}")

                # 检查权重文件是否存在
                if not os.path.exists(weights_path):
                    raise FileNotFoundError(f"生产环境权重文件不存在: {weights_path}")

                model_loaded = False

                # 方法1: 直接使用torch.load（最稳定的方法，优先使用）
                try:
                    LOGGER.info("尝试使用YOLOv5原生方式加载模型...")
                    LOGGER.info(f"当前工作目录: {os.getcwd()}")
                    LOGGER.info(f"当前sys.path前3项: {sys.path[:3]}")

                    # 尝试使用YOLOv5的原生加载方式
                    try:
                        # 方法1: 使用YOLOv5的models.common.DetectMultiBackend
                        from models.common import DetectMultiBackend
                        model = DetectMultiBackend(weights_path, device=self.device)
                        LOGGER.info("✅ 成功使用DetectMultiBackend加载模型")
                    except Exception as e_backend:
                        LOGGER.warning(f"DetectMultiBackend加载失败: {e_backend}")

                        # 方法2: 使用torch.hub加载
                        try:
                            import torch
                            model = torch.hub.load('ultralytics/yolov5', 'custom', weights_path, device=self.device)
                            LOGGER.info("✅ 成功使用torch.hub加载模型")
                        except Exception as e_hub:
                            LOGGER.warning(f"torch.hub加载失败: {e_hub}")

                            # 方法3: 直接加载checkpoint并手动构建
                            import torch as torch_module
                            checkpoint = torch_module.load(weights_path, map_location=self.device)

                            # 尝试从checkpoint中获取模型
                            if isinstance(checkpoint, dict):
                                if 'model' in checkpoint:
                                    model = checkpoint['model']
                                elif 'ema' in checkpoint:
                                    model = checkpoint['ema']
                                else:
                                    # 如果checkpoint包含state_dict，需要构建模型结构
                                    raise Exception("需要模型结构定义")
                            else:
                                model = checkpoint

                            # 确保模型处于评估模式
                            model = model.float().eval()
                            LOGGER.info("✅ 成功使用checkpoint直接加载模型")

                    # 设置基本属性
                    if not hasattr(model, 'conf'):
                        model.conf = getattr(self, 'conf_thres', 0.25)
                    if not hasattr(model, 'iou'):
                        model.iou = getattr(self, 'nms_thres', 0.45)

                    # 使用自动选择的设备
                    model.to(self.device)
                    self.models[model_name] = model
                    LOGGER.info("✅ 成功加载YOLOv5生产环境模型")
                    model_loaded = True

                except Exception as e1:
                    LOGGER.warning(f"YOLOv5原生方式加载失败: {e1}")

                # 方法2: 使用ultralytics包（如果可用）
                if not model_loaded:
                    try:
                        LOGGER.info("尝试使用ultralytics包加载模型...")
                        from ultralytics import YOLO
                        model = YOLO(weights_path)
                        self.models[model_name] = model
                        LOGGER.info("✅ 成功使用ultralytics包加载生产环境模型")
                        model_loaded = True
                    except Exception as e2:
                        LOGGER.warning(f"ultralytics包加载失败: {e2}")

                # 方法3: 使用torch.hub本地模式（最后尝试，因为有依赖问题）
                if not model_loaded:
                    try:
                        LOGGER.info("尝试使用torch.hub本地模式加载模型...")
                        import torch.hub
                        # 设置YOLOv5目录
                        torch.hub.set_dir(yolov5_absolute_path)
                        model = torch.hub.load(yolov5_absolute_path, 'custom',
                                             path=weights_path, source='local',
                                             force_reload=False, trust_repo=True)

                        # 设置模型参数
                        model.conf = getattr(self, 'conf_thres', 0.25)
                        model.iou = getattr(self, 'nms_thres', 0.45)
                        model.eval()

                        # 从配置文件读取目标类别
                        target_classes = self._get_target_classes_from_config()
                        if target_classes:
                            model.classes = target_classes
                            LOGGER.info(f"设置检测类别: {target_classes}")

                        self.models[model_name] = model
                        LOGGER.info("✅ 成功使用torch.hub本地模式加载生产环境YOLOv5模型")
                        model_loaded = True

                    except Exception as e3:
                        LOGGER.warning(f"torch.hub本地模式加载失败: {e3}")

                if not model_loaded:
                    raise RuntimeError(f"❌ 生产环境模型加载失败: 所有加载方法都失败了 - {model_name}")

            self.status = True
            LOGGER.info("🎉 所有生产环境模型加载成功")

        except Exception as e:
            LOGGER.error(f"❌ 生产环境模型加载失败: {e}")
            self.status = False
            raise RuntimeError(f"生产环境模型加载失败: {e}")



    def _get_target_classes_from_config(self):
        """从配置文件读取目标检测类别"""
        try:
            import yaml
            # 读取后处理器配置文件
            config_path = os.path.join(os.path.dirname(__file__), '..', 'postprocessor', 'postprocessor.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                # 提取class2label配置中的类别ID
                target_classes = []
                if 'model' in config:
                    for model_name, model_config in config['model'].items():
                        if 'label' in model_config and 'class2label' in model_config['label']:
                            class2label = model_config['label']['class2label']
                            # 获取所有配置的类别ID
                            target_classes.extend([int(class_id) for class_id in class2label.keys()])

                if target_classes:
                    LOGGER.info(f"从配置文件读取到检测类别: {target_classes}")
                    return list(set(target_classes))  # 去重
                else:
                    LOGGER.warning("配置文件中未找到class2label配置")
            else:
                LOGGER.warning(f"配置文件不存在: {config_path}")
        except Exception as e:
            LOGGER.error(f"读取配置文件失败: {e}")

        return None

    def _create_missing_modules(self):
        """创建缺失的模块以避免导入错误"""
        try:
            import sys
            import types
            import logging

            # 首先创建基础的utils模块
            if 'utils' not in sys.modules:
                utils_module = types.ModuleType('utils')
                sys.modules['utils'] = utils_module
            else:
                utils_module = sys.modules['utils']

            # 创建TryExcept类
            class TryExcept:
                def __init__(self, msg=""):
                    self.msg = msg
                def __enter__(self):
                    pass
                def __exit__(self, exc_type, value, traceback):
                    if value and self.msg:
                        LOGGER.warning(f"{self.msg}: {value}")
                    return True

            # 创建基础函数
            def colorstr(*args):
                return str(args[0]) if args else ""

            def check_version(*args, **kwargs):
                return True

            def emojis(str=""):
                return str

            # 直接添加到utils模块（models/common.py需要从utils导入）
            utils_module.TryExcept = TryExcept
            utils_module.colorstr = colorstr
            utils_module.check_version = check_version
            utils_module.emojis = emojis

            # 创建utils.general模块
            if 'utils.general' not in sys.modules:
                general_module = types.ModuleType('utils.general')
                sys.modules['utils.general'] = general_module

                # 添加LOGGER
                general_module.LOGGER = logging.getLogger("yolov5")

                # 添加ROOT路径
                import os
                general_module.ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

                # 添加Profile类
                class Profile:
                    def __init__(self):
                        pass
                    def __enter__(self):
                        return self
                    def __exit__(self, *args):
                        pass

                # 添加其他必要的函数
                def check_requirements(*args, **kwargs):
                    pass

                def check_suffix(*args, **kwargs):
                    return True

                def increment_path(path, exist_ok=False, sep='', mkdir=False):
                    """增量路径函数"""
                    import os
                    from pathlib import Path
                    path = Path(path)
                    if path.exists() and not exist_ok:
                        path, suffix = (path.with_suffix(''), path.suffix) if path.is_file() else (path, '')
                        for n in range(2, 9999):
                            p = f'{path}{sep}{n}{suffix}'
                            if not os.path.exists(p):
                                break
                        path = Path(p)
                    if mkdir:
                        path.mkdir(parents=True, exist_ok=True)
                    return path

                def is_jupyter():
                    """检查是否在Jupyter环境中运行"""
                    try:
                        from IPython import get_ipython
                        return get_ipython() is not None
                    except ImportError:
                        return False

                # 添加所有函数到general模块
                general_module.colorstr = colorstr
                general_module.check_version = check_version
                general_module.emojis = emojis
                general_module.TryExcept = TryExcept
                general_module.Profile = Profile
                general_module.check_requirements = check_requirements
                general_module.check_suffix = check_suffix
                general_module.increment_path = increment_path
                general_module.is_jupyter = is_jupyter

                # 添加其他必要的函数
                def make_divisible(x, divisor):
                    import math
                    return math.ceil(x / divisor) * divisor

                def check_img_size(imgsz, s=32, floor=0):
                    if isinstance(imgsz, int):
                        new_size = max(make_divisible(imgsz, int(s)), floor)
                    else:
                        imgsz = list(imgsz)
                        new_size = [max(make_divisible(x, int(s)), floor) for x in imgsz]
                    return new_size

                general_module.make_divisible = make_divisible
                general_module.check_img_size = check_img_size

                LOGGER.info("成功创建虚拟utils和utils.general模块")

            # 创建虚拟dataloaders模块
            if 'utils.dataloaders' not in sys.modules:
                dataloaders_module = types.ModuleType('utils.dataloaders')
                sys.modules['utils.dataloaders'] = dataloaders_module

                # 添加必要的函数
                def exif_transpose(image):
                    return image

                def letterbox(im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
                    # 简化版letterbox函数
                    import cv2
                    import numpy as np

                    shape = im.shape[:2]  # current shape [height, width]
                    if isinstance(new_shape, int):
                        new_shape = (new_shape, new_shape)

                    # Scale ratio (new / old)
                    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
                    if not scaleup:  # only scale down, do not scale up (for better val mAP)
                        r = min(r, 1.0)

                    # Compute padding
                    ratio = r, r  # width, height ratios
                    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
                    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding

                    if auto:  # minimum rectangle
                        dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
                    elif scaleFill:  # stretch
                        dw, dh = 0.0, 0.0
                        new_unpad = (new_shape[1], new_shape[0])
                        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

                    dw /= 2  # divide padding into 2 sides
                    dh /= 2

                    if shape[::-1] != new_unpad:  # resize
                        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
                    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
                    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
                    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
                    return im, ratio, (dw, dh)

                dataloaders_module.exif_transpose = exif_transpose
                dataloaders_module.letterbox = letterbox
                LOGGER.info("成功创建虚拟dataloaders模块")

            LOGGER.info("成功创建虚拟utils模块")

        except Exception as e:
            LOGGER.warning(f"创建utils模块失败: {e}")
            # 继续执行，可能YOLOv5有自己的utils



    def _rknn_infer(self, model_name, inputs):
        """
        PyTorch推理接口 - 专门处理本地YOLOv5模型
        """
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 未加载")

        model = self.models[model_name]

        with torch.no_grad():
            input_data = inputs[0]

            LOGGER.info("使用本地YOLOv5模型推理")

            # 准备输入数据 - 转换为torch.Tensor
            if isinstance(input_data, np.ndarray):
                LOGGER.info(f"原始输入形状: {input_data.shape}")

                # 确保输入是正确的格式 (H, W, C) uint8
                if input_data.dtype != np.uint8:
                    if input_data.max() <= 1.0:
                        input_data = (input_data * 255).astype(np.uint8)
                    else:
                        input_data = input_data.astype(np.uint8)

                # 转换为torch.Tensor
                input_data = torch.from_numpy(input_data).float()
                LOGGER.info(f"转换为tensor后形状: {input_data.shape}")

                # 调整维度：处理不同的输入格式
                if len(input_data.shape) == 4:  # (B, H, W, C) 或 (B, C, H, W)
                    if input_data.shape[3] == 3:  # (B, H, W, C) -> (B, C, H, W)
                        input_data = input_data.permute(0, 3, 1, 2)  # (B, C, H, W)
                        LOGGER.info(f"调整4D维度后形状: {input_data.shape}")
                elif len(input_data.shape) == 3:  # (H, W, C)
                    if input_data.shape[2] == 3:  # (H, W, C) -> (C, H, W)
                        input_data = input_data.permute(2, 0, 1)  # (C, H, W)
                        LOGGER.info(f"调整3D维度后形状: {input_data.shape}")
                    # 添加batch维度：从 (C, H, W) 到 (1, C, H, W)
                    input_data = input_data.unsqueeze(0)  # (1, C, H, W)
                    LOGGER.info(f"添加batch维度后形状: {input_data.shape}")

                # 归一化到[0,1]
                input_data = input_data / 255.0

                # 移动到正确的设备
                input_data = input_data.to(self.device)
                LOGGER.info(f"最终输入形状: {input_data.shape}, 设备: {input_data.device}")

            # YOLOv5推理 - 直接返回检测结果
            results = model(input_data)

            # 转换为智驱力期望的格式
            return self._convert_yolov5_to_zhiquli_format(results)

    def _convert_yolov5_to_zhiquli_format(self, results):
        """
        将YOLOv5结果转换为智驱力标准的3个尺度输出格式

        Args:
            results: YOLOv5推理结果

        Returns:
            List: 3个尺度的输出 [output1, output2, output3]
        """
        try:
            # 创建3个尺度的空输出 - 智驱力标准格式
            output1 = np.zeros((3, 85, 20, 20))  # 小目标检测
            output2 = np.zeros((3, 85, 40, 40))  # 中等目标检测
            output3 = np.zeros((3, 85, 80, 80))  # 大目标检测

            # 获取检测结果 - 处理不同的输出格式
            detections = None

            # 方法1: 检查是否有xyxy属性（标准YOLOv5输出）
            if hasattr(results, 'xyxy') and len(results.xyxy) > 0:
                detections = results.xyxy[0].cpu().numpy()  # [x1, y1, x2, y2, conf, class]
                LOGGER.info(f"使用xyxy格式，检测到 {len(detections)} 个目标")

            # 方法2: 检查是否是tensor格式（DetectMultiBackend输出）
            elif isinstance(results, torch.Tensor):
                # DetectMultiBackend直接返回tensor
                detections = results.cpu().numpy()
                LOGGER.info(f"使用tensor格式，输出形状: {detections.shape}")

            # 方法3: 检查是否是tuple或list格式
            elif isinstance(results, (tuple, list)) and len(results) > 0:
                detections = results[0]
                if isinstance(detections, torch.Tensor):
                    detections = detections.cpu().numpy()
                LOGGER.info(f"使用tuple/list格式，输出形状: {detections.shape}")

            if detections is not None and len(detections) > 0:

                # 将检测结果编码到合适的尺度中
                for i, det in enumerate(detections):
                    if i >= 100:  # 限制最多处理100个检测
                        break

                    x1, y1, x2, y2, conf, cls = det

                    # 计算目标的中心点和尺寸
                    cx = (x1 + x2) / 2
                    cy = (y1 + y2) / 2
                    w = x2 - x1
                    h = y2 - y1

                    # 根据目标大小选择合适的尺度
                    target_size = max(w, h)

                    if target_size < 50:
                        # 小目标 -> 80x80网格
                        grid_size = 80
                        output = output3
                    elif target_size < 150:
                        # 中等目标 -> 40x40网格
                        grid_size = 40
                        output = output2
                    else:
                        # 大目标 -> 20x20网格
                        grid_size = 20
                        output = output1

                    # 计算网格位置
                    grid_x = int(cx / self.img_size * grid_size)
                    grid_y = int(cy / self.img_size * grid_size)
                    anchor_idx = i % 3  # 简化的anchor分配

                    # 确保索引在有效范围内
                    if 0 <= grid_x < grid_size and 0 <= grid_y < grid_size:
                        # 编码检测结果到网格中
                        output[anchor_idx, 0, grid_y, grid_x] = cx / self.img_size * grid_size - grid_x  # x偏移
                        output[anchor_idx, 1, grid_y, grid_x] = cy / self.img_size * grid_size - grid_y  # y偏移
                        output[anchor_idx, 2, grid_y, grid_x] = w / self.img_size * grid_size  # 宽度
                        output[anchor_idx, 3, grid_y, grid_x] = h / self.img_size * grid_size  # 高度
                        output[anchor_idx, 4, grid_y, grid_x] = conf  # 置信度

                        # 类别概率 (one-hot编码)
                        if 0 <= int(cls) < 80:  # COCO数据集有80个类别
                            output[anchor_idx, 5 + int(cls), grid_y, grid_x] = 1.0

            return [output1, output2, output3]

        except Exception as e:
            LOGGER.error(f"转换YOLOv5结果失败: {e}")
            # 返回空的输出
            return [
                np.zeros((3, 85, 20, 20)),
                np.zeros((3, 85, 40, 40)),
                np.zeros((3, 85, 80, 80))
            ]



    def _convert_pytorch_to_rknn_format(self, pytorch_output):
        """
        将PyTorch YOLOv5输出转换为RKNN期望的格式
        """
        try:
            # 去掉batch维度
            if len(pytorch_output.shape) == 3:
                pytorch_output = pytorch_output[0]  # (num_detections, 85)
            
            total_detections, num_classes = pytorch_output.shape
            
            # YOLOv5标准分割比例
            if total_detections == 25200:  # 640x640输入的标准输出
                split_sizes = [19200, 4800, 1200]
                grid_sizes = [80, 40, 20]
            elif total_detections == 8400:  # 可能是其他输入尺寸
                split_sizes = [6300, 1575, 525]
                grid_sizes = [int(np.sqrt(s/3)) for s in split_sizes]
            else:
                # 通用分割方法：按3:1:0.25比例
                size1 = int(total_detections * 0.76)  # ~76%
                size2 = int(total_detections * 0.19)  # ~19%
                size3 = total_detections - size1 - size2  # 剩余
                split_sizes = [size1, size2, size3]
                grid_sizes = [int(np.sqrt(s/3)) for s in split_sizes]
            
            # 分割并重塑输出
            rknn_outputs = []
            start_idx = 0
            
            for split_size, grid_size in zip(split_sizes, grid_sizes):
                end_idx = start_idx + split_size
                
                # 提取这个尺度的检测结果
                scale_output = pytorch_output[start_idx:end_idx, :]  # (split_size, 85)
                
                # 重塑为RKNN期望的格式: (3, 85, grid_size, grid_size)
                try:
                    grid_cells = grid_size * grid_size
                    expected_total = 3 * grid_cells
                    
                    if split_size >= expected_total:
                        # 截取并重塑
                        truncated = scale_output[:expected_total, :]  # (3*grid_cells, 85)
                        temp = truncated.reshape(3, grid_cells, num_classes)  # (3, grid_cells, 85)
                        temp = temp.transpose(0, 2, 1)  # (3, 85, grid_cells)
                        reshaped = temp.reshape(3, num_classes, grid_size, grid_size)  # (3, 85, grid_size, grid_size)
                    else:
                        # 填充
                        padding_size = expected_total - split_size
                        padding = np.zeros((padding_size, num_classes), dtype=np.float32)
                        padded = np.vstack([scale_output, padding])
                        temp = padded.reshape(3, grid_cells, num_classes)
                        temp = temp.transpose(0, 2, 1)
                        reshaped = temp.reshape(3, num_classes, grid_size, grid_size)
                    
                    rknn_outputs.append(reshaped)
                except ValueError:
                    # 备用方案
                    backup_output = np.zeros((3, num_classes, grid_size, grid_size), dtype=np.float32)
                    rknn_outputs.append(backup_output)
                
                start_idx = end_idx
            
            LOGGER.info(f"PyTorch输出转换成功: {pytorch_output.shape} -> {[out.shape for out in rknn_outputs]}")
            return rknn_outputs
            
        except Exception as e:
            LOGGER.exception(f"PyTorch到RKNN格式转换失败: {e}")
            # 返回备用格式
            return [
                np.zeros((3, 85, 80, 80), dtype=np.float32),
                np.zeros((3, 85, 40, 40), dtype=np.float32), 
                np.zeros((3, 85, 20, 20), dtype=np.float32)
            ]

    def _letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114), 
                   auto=True, scaleFill=False, scaleup=True, stride=32, stretch=False):
        """智驱力原有的letterbox预处理方法"""
        shape = img.shape[:2]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:
            r = min(r, 1.0)

        ratio = r, r
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]

        if auto:
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)
        elif scaleFill:
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]

        if stretch:
            img = cv2.resize(img, new_shape, interpolation=cv2.INTER_LINEAR)
            dw = dh = 0
        else:
            dw /= 2
            dh /= 2

            if shape[::-1] != new_unpad:
                img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
            top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
            left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
            img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)

        return img, dw, dh

    def _xywh2xyxy(self, x):
        """坐标转换"""
        y = np.copy(x)
        y[:, 0] = x[:, 0] - x[:, 2] / 2
        y[:, 1] = x[:, 1] - x[:, 3] / 2
        y[:, 2] = x[:, 0] + x[:, 2] / 2
        y[:, 3] = x[:, 1] + x[:, 3] / 2
        return y

    def _nms_boxes(self, boxes, scores):
        """NMS处理"""
        try:
            indices = cv2.dnn.NMSBoxes(
                boxes.tolist(), 
                scores.tolist(), 
                self.conf_thres,
                self.nms_thres
            )
            if len(indices) > 0:
                return indices.flatten()
            else:
                return np.array([])
        except Exception as e:
            LOGGER.exception(f"NMS失败: {e}")
            return np.array([])

    def __del__(self):
        """释放资源"""
        try:
            for model in self.models.values():
                if hasattr(model, 'cpu'):
                    model.cpu()
            self.models.clear()
        except:
            pass
