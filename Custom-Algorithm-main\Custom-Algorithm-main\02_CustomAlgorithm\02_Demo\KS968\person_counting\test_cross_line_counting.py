#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import numpy as np
import cv2

# 添加路径
sys.path.insert(0, os.path.dirname(__file__))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'postprocessor'))

from cross_line_counter import CrossLineCounter
from logger import LOGGER


def test_cross_line_counting():
    """测试跨线计数功能"""
    print("🎯 跨线计数功能测试")
    print("=" * 60)
    
    # 初始化跨线计数器
    config_path = os.path.join(os.path.dirname(__file__), 'postprocessor', 'person_counting.json')
    counter = CrossLineCounter(config_path)
    
    # 获取可视化数据
    viz_data = counter.get_visualization_data()
    print(f"📏 计数线配置: {viz_data['lines']}")
    print(f"📐 计数区域配置: {viz_data['areas']}")
    print(f"🎯 检测策略: {viz_data['strategy']}")
    
    print("\n🎬 模拟目标移动轨迹测试")
    print("-" * 40)
    
    # 测试场景1：目标从左到右跨越计数线
    print("\n📍 场景1: 目标从左到右移动")
    trajectory1 = [
        {'track_id': 'person_1', 'xyxy': [50, 200, 100, 300]},   # 计数线左侧
        {'track_id': 'person_1', 'xyxy': [150, 200, 200, 300]},  # 跨越计数线
        {'track_id': 'person_1', 'xyxy': [250, 200, 300, 300]},  # 计数线右侧
    ]
    
    for i, detection in enumerate(trajectory1):
        print(f"  帧 {i+1}: 位置 {detection['xyxy']}")
        result = counter.process_detections([detection])
        
        if result['cross_line_events']:
            for event in result['cross_line_events']:
                print(f"    ✅ 跨线事件: {event['line_name']}, 方向: {event['direction']}")
        
        if result['in_area_objects']:
            for obj in result['in_area_objects']:
                print(f"    📍 在区域内: {obj['area_name']}")
    
    print(f"  📊 当前计数统计: {counter.count_stats}")
    
    # 测试场景2：目标从右到左跨越计数线
    print("\n📍 场景2: 目标从右到左移动")
    trajectory2 = [
        {'track_id': 'person_2', 'xyxy': [350, 150, 400, 250]},  # 计数线右侧
        {'track_id': 'person_2', 'xyxy': [250, 150, 300, 250]},  # 跨越计数线
        {'track_id': 'person_2', 'xyxy': [150, 150, 200, 250]},  # 计数线左侧
    ]
    
    for i, detection in enumerate(trajectory2):
        print(f"  帧 {i+1}: 位置 {detection['xyxy']}")
        result = counter.process_detections([detection])
        
        if result['cross_line_events']:
            for event in result['cross_line_events']:
                print(f"    ✅ 跨线事件: {event['line_name']}, 方向: {event['direction']}")
        
        if result['in_area_objects']:
            for obj in result['in_area_objects']:
                print(f"    📍 在区域内: {obj['area_name']}")
    
    print(f"  📊 当前计数统计: {counter.count_stats}")
    
    # 测试场景3：多个目标同时移动
    print("\n📍 场景3: 多个目标同时移动")
    multi_detections = [
        [
            {'track_id': 'person_3', 'xyxy': [80, 180, 120, 280]},   # 左侧
            {'track_id': 'person_4', 'xyxy': [380, 180, 420, 280]},  # 右侧
        ],
        [
            {'track_id': 'person_3', 'xyxy': [180, 180, 220, 280]},  # 跨线
            {'track_id': 'person_4', 'xyxy': [280, 180, 320, 280]},  # 跨线
        ],
        [
            {'track_id': 'person_3', 'xyxy': [280, 180, 320, 280]},  # 右侧
            {'track_id': 'person_4', 'xyxy': [180, 180, 220, 280]},  # 左侧
        ]
    ]
    
    for i, detections in enumerate(multi_detections):
        print(f"  帧 {i+1}: {len(detections)} 个目标")
        result = counter.process_detections(detections)
        
        if result['cross_line_events']:
            for event in result['cross_line_events']:
                print(f"    ✅ 跨线事件: 目标跨越 {event['line_name']}, 方向: {event['direction']}")
        
        print(f"    📍 区域内目标数: {len(result['in_area_objects'])}")
    
    print(f"  📊 最终计数统计: {counter.count_stats}")
    
    return True


def test_different_strategies():
    """测试不同检测策略"""
    print("\n🎯 不同检测策略测试")
    print("=" * 60)
    
    strategies = ['top', 'center', 'bottom', 'left', 'right']
    test_bbox = [180, 150, 220, 250]  # 测试检测框
    
    for strategy in strategies:
        counter = CrossLineCounter()
        counter.strategy = strategy
        
        point = counter.get_detection_point(test_bbox, strategy)
        print(f"📍 策略 '{strategy}': 检测点 {point}")
        
        # 测试该策略下的跨线检测
        detections = [
            {'track_id': 'test_obj', 'xyxy': [150, 150, 190, 250]},  # 左侧
            {'track_id': 'test_obj', 'xyxy': test_bbox},             # 跨线位置
        ]
        
        for detection in detections:
            result = counter.process_detections([detection])
            if result['cross_line_events']:
                print(f"  ✅ 策略 '{strategy}' 检测到跨线事件")
                break
    
    return True


def visualize_counting_setup():
    """可视化计数线和区域设置"""
    print("\n🎨 可视化计数设置")
    print("=" * 60)
    
    # 创建画布
    img = np.zeros((500, 600, 3), dtype=np.uint8)
    img.fill(50)  # 深灰色背景
    
    counter = CrossLineCounter()
    viz_data = counter.get_visualization_data()
    
    # 绘制计数区域
    for area_id, area_info in viz_data['areas'].items():
        points = np.array(area_info['points'], dtype=np.int32)
        cv2.fillPoly(img, [points], (0, 100, 0))  # 绿色半透明
        cv2.polylines(img, [points], True, (0, 255, 0), 2)
        
        # 添加区域标签
        center = np.mean(points, axis=0).astype(int)
        cv2.putText(img, area_info['name'], tuple(center), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 绘制计数线
    for line_id, line_info in viz_data['lines'].items():
        start = tuple(map(int, line_info['start']))
        end = tuple(map(int, line_info['end']))
        cv2.line(img, start, end, (0, 0, 255), 3)  # 红色计数线
        
        # 添加线标签
        mid_point = ((start[0] + end[0]) // 2, (start[1] + end[1]) // 2)
        cv2.putText(img, line_info['name'], mid_point, 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        # 绘制方向箭头
        cv2.arrowedLine(img, start, end, (255, 255, 0), 2, tipLength=0.1)
    
    # 添加说明文字
    cv2.putText(img, f"Strategy: {viz_data['strategy']}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(img, "Green: Counting Area", (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    cv2.putText(img, "Red: Counting Line", (10, 80), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    # 保存图像
    output_path = "counting_setup_visualization.jpg"
    cv2.imwrite(output_path, img)
    print(f"📸 可视化图像已保存: {output_path}")
    
    return True


if __name__ == "__main__":
    print("🎯 跨线计数系统完整测试")
    print("=" * 80)
    
    try:
        # 基础跨线计数测试
        test1_ok = test_cross_line_counting()
        
        # 不同策略测试
        test2_ok = test_different_strategies()
        
        # 可视化测试
        test3_ok = visualize_counting_setup()
        
        print("\n" + "=" * 80)
        print("🎯 测试结果:")
        print(f"   - 跨线计数测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
        print(f"   - 策略测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
        print(f"   - 可视化测试: {'✅ 通过' if test3_ok else '❌ 失败'}")
        
        if test1_ok and test2_ok and test3_ok:
            print("\n🎉 跨线计数系统测试完全成功！")
            print("✅ 支持多种检测策略 (top, center, bottom, left, right)")
            print("✅ 支持双向计数 (进入/离开)")
            print("✅ 支持多目标同时跟踪")
            print("✅ 支持区域检测")
            print("✅ 提供完整的可视化")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
