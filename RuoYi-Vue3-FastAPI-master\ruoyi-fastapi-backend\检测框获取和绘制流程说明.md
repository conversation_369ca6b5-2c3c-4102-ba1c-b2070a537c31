# 检测框获取和绘制流程说明

## 📋 检测框数据来源

### 1. 原始检测结果（AI算法包输出）
```python
# 从AI算法包获得的原始检测结果
detection_result = [
    {
        'bbox': [x1, y1, x2, y2],  # 检测框坐标
        'confidence': 0.85,        # 置信度
        'class_id': 0,            # 类别ID
        'label': 'person'         # 类别名称
    },
    # ... 更多检测目标
]
```

### 2. 后处理判断结果（告警目标）
```python
# 经过后处理算法判断后的告警结果
alert_result = {
    'hit': True,                    # 是否触发告警
    'message': '检测到人员入侵',     # 告警消息
    'data': {
        'bbox': {
            'rectangles': [[x1, y1, x2, y2], ...],  # 告警目标框
            'polygons': {}
        }
    },
    'details': {
        'alert_targets': [          # 经过后处理的告警目标
            {
                'bbox': [x1, y1, x2, y2],
                'confidence': 0.85,
                'alert_type': 'area_intrusion',
                'alert_reason': '进入禁区'
            }
        ],
        'configured_areas': [...],   # 配置的检测区域
        'configured_lines': [...],   # 配置的检测线段
        'in_area_detections': [...], # 区域内检测
        'line_crossing_detections': [...] # 越线检测
    }
}
```

## 🔄 完整的检测和绘制流程

### 第1步：视频帧获取
```python
# 从RTSP流读取视频帧
ret, frame = cap.read()
```

### 第2步：AI算法检测
```python
# 调用AI算法包进行目标检测
detection_result = model.detect(frame)
# 返回所有检测到的目标（包括非告警目标）
```

### 第3步：后处理判断
```python
# 使用后处理算法判断是否为告警目标
postprocessor.process(model_data, result_dict)
# 只有满足告警条件的目标才会被标记为告警
```

### 第4步：绘制逻辑
```python
def _draw_detection_boxes(frame, alert_result):
    """
    绘制检测框的优先级：
    1. 始终绘制配置的区域和线段（监控范围）
    2. 只绘制告警目标的检测框（不绘制所有检测结果）
    3. 根据告警类型使用不同颜色
    """
    
    # 1. 始终绘制配置的区域和线段
    _draw_configured_areas_and_lines(frame, alert_result)
    
    # 2. 只有在有告警时才绘制检测框
    if alert_result.get('hit', False):
        # 优先绘制 alert_targets（最准确的告警目标）
        alert_targets = details.get('alert_targets', [])
        if alert_targets:
            for target in alert_targets:
                _draw_single_alert_target(frame, target)
        else:
            # 备选方案：绘制其他格式的告警目标
            _draw_area_detection(frame, in_area_detections)
            _draw_line_detection(frame, line_crossing_detections)
            _draw_bbox_rectangles(frame, rectangles)
```

## 🎨 绘制内容说明

### 配置区域和线段（始终显示）
- **检测区域**：半透明黄色填充 + 青色边界
- **检测线段**：紫色线段
- **用途**：显示监控范围，让用户知道哪些区域被监控

### 告警目标检测框（仅告警时显示）
- **区域入侵**：红色框 (0, 0, 255)
- **越线检测**：黄色框 (0, 255, 255)
- **车辆计数**：紫色框 (255, 0, 255)
- **默认告警**：红色框 (0, 0, 255)

### 状态信息（始终显示）
- **时间戳**：当前时间
- **监控状态**：有告警时显示告警信息，无告警时显示"Monitoring - No Alert"
- **统计信息**：检测总数、告警数量

## 🔍 关键设计原则

### 1. 只绘制告警目标
```python
# ❌ 错误：绘制所有检测结果
for detection in all_detections:
    draw_box(detection)

# ✅ 正确：只绘制告警目标
if alert_result.get('hit', False):
    for target in alert_targets:
        draw_alert_target(target)
```

### 2. 实时监控始终显示配置信息
```python
# 无论是否有告警，都要显示：
# - 配置的检测区域
# - 配置的检测线段
# - 时间戳
# - 监控状态
```

### 3. 告警截图保存
```python
# 保存告警截图时：
# 1. 复制原始帧
# 2. 绘制告警目标检测框
# 3. 绘制配置区域和线段
# 4. 添加时间戳和告警信息
# 5. 保存到指定目录
```

## 📊 数据流向图

```
RTSP视频流
    ↓
读取视频帧
    ↓
AI算法检测 → 原始检测结果（所有目标）
    ↓
后处理判断 → 告警结果（只有告警目标）
    ↓
绘制逻辑:
├── 配置区域/线段（始终显示）
├── 告警目标检测框（仅告警时）
└── 状态信息（始终显示）
    ↓
实时监控推送 ← 告警截图保存
```

## 🎯 实际应用场景

### 场景1：人员入侵检测
1. **AI检测**：检测到5个人
2. **后处理**：判断其中2个人进入了禁区
3. **绘制结果**：只绘制2个告警目标的红色检测框

### 场景2：车辆计数
1. **AI检测**：检测到10辆车
2. **后处理**：判断其中3辆车越过了计数线
3. **绘制结果**：只绘制3个告警目标的紫色检测框

### 场景3：无告警状态
1. **AI检测**：检测到多个目标
2. **后处理**：判断都不是告警目标
3. **绘制结果**：只显示配置区域/线段，不绘制任何检测框

## 🔧 配置示例

### 检测区域配置
```json
{
  "detection_areas": [
    {
      "id": "area_1",
      "name": "禁区",
      "points": [
        {"x": 100, "y": 100},
        {"x": 300, "y": 100},
        {"x": 300, "y": 300},
        {"x": 100, "y": 300}
      ]
    }
  ]
}
```

### 检测线段配置
```json
{
  "detection_lines": [
    {
      "id": "line_1",
      "name": "计数线",
      "start_point": {"x": 200, "y": 0},
      "end_point": {"x": 200, "y": 480}
    }
  ]
}
```

## ✅ 总结

1. **检测框来源**：从AI算法包获得原始检测结果，经过后处理判断得到告警目标
2. **绘制原则**：只绘制告警目标，不绘制所有检测结果
3. **实时监控**：始终显示配置的区域和线段，有告警时显示告警目标
4. **告警截图**：保存带有告警目标检测框的图片到本地
5. **用户体验**：清晰区分监控范围和实际告警，避免信息过载
