<template>
  <div class="algorithm-config">
    <!-- 基本信息 -->
    <div class="config-section">
      <h4>基本信息</h4>
      <div class="form-item">
        <label class="required">任务名称：</label>
        <el-input
          v-model="taskName"
          placeholder="请输入任务名称（必填）"
          style="width: 100%"
          :class="{ 'error': !taskName || taskName.trim() === '' }"
          @blur="validateTaskNameLocal"
        />
        <div v-if="taskNameError" class="error-message">{{ taskNameError }}</div>
      </div>
    </div>

    <!-- 算法选择 -->
    <div class="config-section">
      <h4>选择算法</h4>
      <el-select
        v-model="selectedAlgorithm"
        placeholder="请选择算法"
        @change="onAlgorithmChange"
        style="width: 100%"
        clearable
      >
        <el-option
          v-for="algorithm in algorithms"
          :key="algorithm.algorithm_id"
          :label="algorithm.algorithm_name"
          :value="algorithm.algorithm_id"
        >
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>{{ algorithm.algorithm_name }}</span>
            <span style="color: #8492a6; font-size: 12px">{{ algorithm.version }}</span>
          </div>
        </el-option>
      </el-select>

      <!-- 算法描述信息 -->
      <div v-if="currentAlgorithm" class="algorithm-info">
        <p class="algorithm-description">{{ currentAlgorithm.algorithm_desc }}</p>
        <div class="algorithm-meta">
          <el-tag size="small" type="info">{{ currentAlgorithm.group_name }}</el-tag>
          <el-tag size="small">{{ currentAlgorithm.version }}</el-tag>
          <el-tag size="small" type="success">处理时间: {{ currentAlgorithm.process_time }}ms</el-tag>
        </div>
      </div>
    </div>

    <!-- 算法配置 -->
    <div v-if="currentAlgorithm" class="config-section">
      <h4>{{ currentAlgorithm.algorithm_name }} - 参数配置</h4>
      <!-- 智驱力动态参数配置 -->
      <div v-if="Object.keys(algorithmRenderConfig).length > 0" class="config-subsection">
        <h5>算法参数配置</h5>
        <DynamicParamRenderer
          v-model="dynamicConfig"
          :render-config="algorithmRenderConfig"
          @edit-geometry="handleEditGeometry"
        />
      </div>



      <!-- 保存按钮 -->
      <div class="config-actions">
        <el-button type="primary" @click="saveConfig" :loading="saving">
          保存配置
        </el-button>
      </div>
    </div>

    <!-- 统一几何编辑器弹窗 -->
    <GeometryEditor
      v-model="showGeometryEditor"
      v-model:polygons="polygons"
      v-model:lines="lines"
      :video-url="videoUrl"
      :supports-polygons="geometrySupports.supportsPolygons"
      :supports-lines="geometrySupports.supportsLines"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import GeometryEditor from './GeometryEditor.vue'
import DynamicParamRenderer from './DynamicParamRenderer.vue'
import {
  saveStandardAlgorithmConfig,
  getAlgorithmsWithInfo
} from '@/api/stream_manage/algorithm'
import {
  validateTaskName,
  validateAlgorithmConfig,
  generateDefaultTaskName,
  formatValidationErrors
} from '@/utils/algorithmValidation'

const props = defineProps({
  streamId: {
    type: Number,
    default: null
  },
  videoUrl: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['config-saved'])

// 数据
const taskName = ref('')
const taskNameError = ref('')
const showGeometryEditor = ref(false)
const geometrySupports = ref({ supportsPolygons: true, supportsLines: true })
const saving = ref(false)
const polygons = ref([])
const lines = ref([])

// 智驱力算法相关
const algorithms = ref([])
const selectedAlgorithm = ref('')
const currentAlgorithmInfo = ref(null)
const userConfig = ref({})

// 兼容性变量（用于模板中的条件判断）
const algorithmRenderConfig = computed(() => {
  return currentAlgorithm.value?.render_params || {}
})

const dynamicConfig = computed({
  get: () => userConfig.value,
  set: (value) => {
    userConfig.value = value
  }
})



// 当前选中的算法
const currentAlgorithm = computed(() => {
  const algorithm = algorithms.value.find(alg => alg.algorithm_id === selectedAlgorithm.value)
  if (!algorithm) return null

  // 从智驱力配置中解析算法能力
  const renderParams = algorithm.render_params || {}
  const bboxConfig = renderParams.bbox || {}

  return {
    ...algorithm,
    // 根据智驱力配置动态确定算法能力
    supports_polygons: 'polygons' in bboxConfig,
    supports_lines: 'lines' in bboxConfig,
    supports_exclusion: bboxConfig.polygons?.exits === 'must' || bboxConfig.polygons?.exits === 'optional'
  }
})



// 配置数据
const config = reactive({
  algorithm_id: '',
  algorithm_name: '',
  algorithm_version: '',
  task_name: '',
  detection_areas: [],
  detection_lines: [],
  model_parameters: {
    confidence_threshold: 0.6,
    nms_threshold: 0.5,
    input_size: 640,
    max_detections: 50
  },
  alert_parameters: {
    enable_alert: true,
    alert_interval: 5,
    alert_threshold: 1,
    alert_message: '检测到异常'
  }
})

// 方法
const loadAlgorithms = async () => {
  try {
    // 加载智驱力算法及其完整信息
    const response = await getAlgorithmsWithInfo()
    if (response.code === 200) {
      algorithms.value = response.data
      console.log('加载的算法列表:', algorithms.value)
    } else {
      ElMessage.error(response.msg || '加载算法列表失败')
    }
  } catch (error) {
    console.error('加载算法列表失败:', error)
    ElMessage.error('加载算法列表失败')
  }
}

const onAlgorithmChange = async () => {
  if (currentAlgorithm.value) {
    // 设置当前算法信息
    currentAlgorithmInfo.value = currentAlgorithm.value

    // 初始化用户配置为默认配置的深拷贝
    userConfig.value = JSON.parse(JSON.stringify(currentAlgorithm.value.default_params || {}))

    // 确保bbox对象存在
    if (!userConfig.value.bbox) {
      userConfig.value.bbox = {
        polygons: [],
        lines: []
      }
    }

    // 重置多边形区域
    polygons.value = []
    userConfig.value.bbox.polygons = []

    // 重置线段
    lines.value = []
    userConfig.value.bbox.lines = []

    console.log('选择算法:', currentAlgorithm.value.algorithm_name)
    console.log('算法能力:', {
      supports_polygons: currentAlgorithm.value.supports_polygons,
      supports_lines: currentAlgorithm.value.supports_lines,
      supports_exclusion: currentAlgorithm.value.supports_exclusion
    })
    console.log('渲染配置:', currentAlgorithm.value.render_params)
    console.log('默认配置:', userConfig.value)
  }
}



const updatePolygonsInConfig = () => {
  console.log('updatePolygonsInConfig 被调用，polygons.value:', polygons.value)

  // 转换多边形数据格式：从 [[{x,y},...]] 转换为 [{id, name, points: [{x,y},...]}]
  config.detection_areas = polygons.value.map((polygon, index) => ({
    id: `area_${Date.now()}_${index}`, // 生成唯一ID
    name: `检测区域${index + 1}`,
    points: polygon,
    type: 'detection'
  }))

  // 同步更新智驱力动态配置中的bbox数据
  if (!userConfig.value.bbox) {
    userConfig.value.bbox = {}
  }

  // 强制触发响应式更新
  const newBbox = { ...userConfig.value.bbox }
  newBbox.polygons = [...polygons.value]
  userConfig.value.bbox = newBbox

  console.log('更新完成，config.detection_areas.length:', config.detection_areas.length)
  console.log('更新完成，userConfig.value.bbox.polygons.length:', userConfig.value.bbox.polygons.length)
}

// 更新线段到配置中
const updateLinesInConfig = () => {
  // 转换线段数据格式：从 [[{x,y},...]] 转换为 [{id, name, points: [{x,y},...]}]
  config.detection_lines = lines.value.map((line, index) => ({
    id: `line_${Date.now()}_${index}`, // 生成唯一ID
    name: `检测线段${index + 1}`,
    points: line,
    type: 'line'
  }))

  // 同步更新智驱力动态配置中的bbox数据
  if (!userConfig.value.bbox) {
    userConfig.value.bbox = {}
  }

  // 强制触发响应式更新
  const newBbox = { ...userConfig.value.bbox }
  newBbox.lines = [...lines.value]
  userConfig.value.bbox = newBbox
}

// 验证任务名称
const validateTaskNameLocal = () => {
  const result = validateTaskName(taskName.value)
  taskNameError.value = result.error
  return result.isValid
}

// 验证配置数据
const validateConfig = () => {
  // 构建验证配置对象
  const validationConfig = {
    streamId: props.streamId,
    selectedAlgorithm: selectedAlgorithm.value,
    taskName: taskName.value,
    model_params: config.model_parameters,
    alert_params: config.alert_parameters,
    detection_areas: config.detection_areas,
    detection_lines: config.detection_lines
  }

  // 获取当前算法能力 - 使用计算属性中已经处理过的算法信息
  const algorithmCapability = currentAlgorithm.value

  // 调试算法能力信息
  console.log('=== 验证配置时的算法能力调试 ===')
  console.log('算法ID:', selectedAlgorithm.value)
  console.log('算法能力:', {
    supports_polygons: algorithmCapability?.supports_polygons,
    supports_lines: algorithmCapability?.supports_lines,
    supports_exclusion: algorithmCapability?.supports_exclusion
  })
  console.log('检测区域数量:', config.detection_areas.length)
  console.log('检测线段数量:', config.detection_lines.length)

  // 使用通用验证工具
  const result = validateAlgorithmConfig(validationConfig, algorithmCapability)

  // 如果算法支持多边形但没有配置，给出提示（不强制要求）
  if (algorithmCapability?.supports_polygons && config.detection_areas.length === 0) {
    console.log('提示：该算法支持多边形检测区域，您可以绘制检测区域以提高检测精度')
  }

  return result.errors
}

const saveConfig = async () => {
  // 验证配置
  const validationErrors = validateConfig()
  if (validationErrors.length > 0) {
    ElMessage.error(formatValidationErrors(validationErrors))
    return
  }

  updatePolygonsInConfig()

  saving.value = true
  try {
    // 获取当前选中算法的信息
    const currentAlgorithm = algorithms.value.find(alg => alg.algorithm_id === selectedAlgorithm.value)

    // 更新配置中的算法信息
    config.algorithm_id = selectedAlgorithm.value
    config.algorithm_name = currentAlgorithm?.algorithm_name || ''
    config.algorithm_version = currentAlgorithm?.version || currentAlgorithm?.algorithm_version || 'unknown'

    // 确保任务名称不为空，如果为空则生成默认名称
    const finalTaskName = taskName.value?.trim() ||
      generateDefaultTaskName(currentAlgorithm?.algorithm_name, selectedAlgorithm.value)

    // 构建智驱力配置数据 - 确保使用前端调整后的完整配置
    const finalZhiquliConfig = {
      ...userConfig.value,
      // 确保bbox数据是最新的
      bbox: {
        polygons: polygons.value,
        lines: lines.value
      }
    }

    // 从智驱力配置中提取模型参数
    const modelParams = finalZhiquliConfig.model_args?.zql_common || {}
    const alertWindow = finalZhiquliConfig.alert_window || {}
    const reservedArgs = finalZhiquliConfig.reserved_args || {}

    console.log('=== 配置数据提取调试 ===')
    console.log('1. finalZhiquliConfig:', finalZhiquliConfig)
    console.log('2. model_args:', finalZhiquliConfig.model_args)
    console.log('3. modelParams (zql_common):', modelParams)
    console.log('4. alertWindow:', alertWindow)
    console.log('5. reservedArgs:', reservedArgs)

    const finalConfig = {
      algorithm_id: selectedAlgorithm.value,
      // 使用中文算法名称
      algorithm_name: currentAlgorithm.value?.algorithm_name || selectedAlgorithm.value,
      algorithm_version: currentAlgorithm.value?.version || '1.0',
      algorithm_type: 'zhiquli',
      zhiquli_config: finalZhiquliConfig,

      // 标准化的模型参数（后端期望的格式）
      model_parameters: (() => {
        // 从所有模型参数中提取置信度阈值
        let confidence_threshold = 0.5
        let nms_threshold = 0.5
        let input_size = 640
        let max_detections = 100

        // 遍历所有模型参数，寻找置信度配置
        const modelArgs = finalZhiquliConfig.model_args || {}
        for (const [, modelConfig] of Object.entries(modelArgs)) {
          if (modelConfig && typeof modelConfig === 'object') {
            if (modelConfig.conf_thres !== undefined) {
              confidence_threshold = modelConfig.conf_thres
            }
            if (modelConfig.nms_thres !== undefined) {
              nms_threshold = modelConfig.nms_thres
            }
            if (modelConfig.input_size !== undefined) {
              input_size = modelConfig.input_size
            }
            if (modelConfig.max_detections !== undefined) {
              max_detections = modelConfig.max_detections
            }
          }
        }

        console.log('6. 提取的模型参数:', {
          confidence_threshold,
          nms_threshold,
          input_size,
          max_detections
        })

        return {
          confidence_threshold,
          nms_threshold,
          input_size,
          max_detections
        }
      })(),

      // 标准化的告警参数（后端期望的格式）
      alert_parameters: {
        enable_alert: true, // 默认启用告警
        alert_interval: alertWindow.interval || 5,
        alert_threshold: alertWindow.threshold || 1,
        alert_message: reservedArgs.sound_text || '检测到异常'
      },

      // 包含多边形区域配置
      detection_areas: polygons.value.map((polygon, index) => ({
        id: `area_${Date.now()}_${index}`, // 添加必需的id字段
        name: `区域${index + 1}`,
        points: polygon,
        type: 'polygon'
      })),
      // 包含线段配置
      detection_lines: lines.value.map((line, index) => ({
        id: `line_${Date.now()}_${index}`, // 添加必需的id字段
        name: `线段${index + 1}`,
        points: line,
        type: 'line'
      }))
    }

    const requestData = {
      stream_id: props.streamId,
      task_name: finalTaskName,
      config: finalConfig,
      remark: ''
    }

    // 详细调试信息
    console.log('=== 前端配置保存调试 ===')
    console.log('1. 智驱力用户配置:', JSON.stringify(userConfig.value, null, 2))
    console.log('2. 最终智驱力配置:', JSON.stringify(finalZhiquliConfig, null, 2))
    console.log('3. 前端多边形数据:', JSON.stringify(polygons.value, null, 2))
    console.log('4. 前端线段数据:', JSON.stringify(lines.value, null, 2))
    console.log('5. 最终配置数据:', JSON.stringify(finalConfig, null, 2))
    console.log('6. 请求数据:', JSON.stringify(requestData, null, 2))
    console.log('7. 检测区域数量:', finalConfig.detection_areas.length)
    console.log('8. 检测线段数量:', finalConfig.detection_lines.length)
    console.log('9. 当前算法信息:', JSON.stringify(currentAlgorithm.value, null, 2))

    const response = await saveStandardAlgorithmConfig(requestData)

    if (response.code === 200) {
      // 更详细的保存成功反馈
      const areaCount = finalConfig.detection_areas.length
      const successMsg = areaCount > 0
        ? `配置保存成功！任务"${finalTaskName}"已创建，包含${areaCount}个检测区域。`
        : `配置保存成功！任务"${finalTaskName}"已创建。`

      ElMessage.success(successMsg)
      emit('config-saved', {
        algorithmId: selectedAlgorithm.value,
        taskName: finalTaskName,
        config: finalConfig
      })
    } else {
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    console.error('请求数据:', requestData)
    console.error('错误详情:', error.response?.data || error.message)

    // 显示更详细的错误信息
    let errorMessage = '保存配置失败'
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    saving.value = false
  }
}

// 处理几何编辑事件
const handleEditGeometry = (supports) => {
  if (supports) {
    geometrySupports.value = supports
  }
  showGeometryEditor.value = true
}

// 监听多边形变化
watch(polygons, (newPolygons) => {
  console.log('多边形数据变化:', newPolygons)
  updatePolygonsInConfig()
  console.log('更新后的config.detection_areas:', config.detection_areas)
  console.log('更新后的userConfig.bbox.polygons:', userConfig.value.bbox?.polygons)
}, { deep: true })

// 监听线段变化
watch(lines, () => {
  updateLinesInConfig()
}, { deep: true })

// 监听userConfig.bbox变化
watch(() => userConfig.value.bbox, (newBbox) => {
  console.log('=== userConfig.bbox 变化 ===')
  console.log('新的bbox数据:', newBbox)
  console.log('多边形数量:', newBbox?.polygons?.length || 0)
  console.log('线段数量:', newBbox?.lines?.length || 0)
}, { deep: true })

// 初始化
onMounted(() => {
  loadAlgorithms()
})

// 监听任务名称变化，清除错误信息
watch(taskName, () => {
  if (taskNameError.value && taskName.value && taskName.value.trim() !== '') {
    taskNameError.value = ''
  }
})
</script>

<style scoped>
.algorithm-config {
  padding: 20px;
  height: 100%;
  max-height: calc(100vh - 200px); /* 根据页面布局调整 */
  overflow-y: auto;
  overflow-x: hidden;
  /* 美化滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

/* Webkit 浏览器滚动条样式 */
.algorithm-config::-webkit-scrollbar {
  width: 6px;
}

.algorithm-config::-webkit-scrollbar-track {
  background: transparent;
}

.algorithm-config::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.algorithm-config::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.config-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.form-item label.required::after {
  content: ' *';
  color: #f56c6c;
  font-weight: bold;
}

.form-item .el-input.error :deep(.el-input__wrapper) {
  border-color: #f56c6c;
  box-shadow: 0 0 0 1px #f56c6c inset;
}

.error-message {
  margin-top: 4px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 1.4;
}

.config-actions {
  margin-top: 24px;
  text-align: center;
}

.polygon-list {
  margin-top: 16px;
}

.polygon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.polygon-item:last-child {
  margin-bottom: 0;
}

.polygon-name {
  color: #409eff;
  font-weight: 500;
}

/* 算法信息样式 */
.algorithm-info {
  margin-top: 12px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.algorithm-description {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.algorithm-capabilities {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

/* 配置子节样式 */
.config-subsection {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
}

.config-subsection h5 {
  margin: 0 0 16px 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

/* 参数描述样式 */
.param-description {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  padding: 20px;
  text-align: center;
}

.empty-state .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.polygon-config, .line-config {
  margin-bottom: 16px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.polygon-preview {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  min-height: 80px;
  background: #fff;
}

.no-polygon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  height: 60px;
}

.param-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.param-group label {
  min-width: 100px;
  font-size: 13px;
}

.unit {
  font-size: 12px;
  color: #909399;
}

.confidence-value {
  font-size: 12px;
  color: #409eff;
  min-width: 40px;
}
</style>
