#!/usr/bin/env python3
"""
简化告警逻辑测试
测试新的简化告警目标提取和绘制逻辑
"""

import cv2
import numpy as np
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class SimplifiedAlertLogicTester:
    """简化告警逻辑测试器"""
    
    def create_test_frame(self, width=800, height=600):
        """创建测试帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(64 + 64 * np.sin(x * 0.005)),
                    int(64 + 64 * np.sin(y * 0.005)),
                    64
                ]
        
        # 添加标题
        cv2.putText(frame, "Simplified Alert Logic Test", (width//4, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
        
        return frame
    
    def create_algorithm_config(self):
        """创建算法配置"""
        return {
            'algorithm_config': {
                'areas': [
                    {
                        'id': 'area_1',
                        'name': '监控区域',
                        'points': [
                            {'x': 100, 'y': 100},
                            {'x': 400, 'y': 100},
                            {'x': 400, 'y': 300},
                            {'x': 100, 'y': 300}
                        ]
                    }
                ]
            }
        }
    
    def test_alert_with_algorithm_color(self):
        """测试告警状态 + 算法包提供颜色"""
        print(f"\n{'='*60}")
        print("测试：告警状态 + 算法包提供颜色")
        print(f"{'='*60}")
        
        try:
            frame = self.create_test_frame()
            algorithm_config = self.create_algorithm_config()
            
            # 算法包输出：告警状态，提供颜色
            postprocess_result = {
                'hit': True,
                'message': '检测到车辆违规',
                'details': {
                    'detections': [
                        {
                            'xyxy': [150, 150, 250, 220],
                            'conf': 0.89,
                            'color': [255, 165, 0],  # 橙色
                            'label': 'car'
                        },
                        {
                            'xyxy': [520, 200, 620, 280],
                            'conf': 0.76,
                            'color': [0, 255, 255],  # 青色
                            'label': 'truck'
                        }
                    ]
                }
            }
            
            detection_result = {'detections': postprocess_result['details']['detections']}
            frame_shape = frame.shape
            
            print(f"✅ 测试数据：")
            print(f"  - 告警状态: {postprocess_result['hit']}")
            print(f"  - 检测数量: {len(postprocess_result['details']['detections'])}")
            print(f"  - 算法包颜色: 橙色[255,165,0], 青色[0,255,255]")
            
            # 解析结果
            parsed_result = TaskExecutionService._parse_postprocess_result(
                postprocess_result, detection_result, algorithm_config, frame_shape
            )
            
            alert_targets = parsed_result['details'].get('alert_targets', [])
            print(f"\n✅ 解析结果：")
            print(f"  - 告警目标数量: {len(alert_targets)}")
            for i, target in enumerate(alert_targets):
                print(f"    目标{i+1}: 原始颜色={target.get('original_color')}")
            
            # 绘制结果
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, parsed_result)
            
            cv2.imwrite("test_alert_with_algorithm_color.jpg", annotated_frame)
            print(f"\n📸 保存结果: test_alert_with_algorithm_color.jpg")
            print(f"  ℹ️  应该显示算法包提供的橙色和青色检测框")
            
            return len(alert_targets) > 0
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_alert_without_algorithm_color(self):
        """测试告警状态 + 无算法包颜色"""
        print(f"\n{'='*60}")
        print("测试：告警状态 + 无算法包颜色")
        print(f"{'='*60}")
        
        try:
            frame = self.create_test_frame()
            algorithm_config = self.create_algorithm_config()
            
            # 算法包输出：告警状态，不提供颜色
            postprocess_result = {
                'hit': True,
                'message': '检测到车辆违规',
                'details': {
                    'detections': [
                        {
                            'xyxy': [150, 150, 250, 220],
                            'conf': 0.89,
                            'label': 'car'
                            # 注意：没有color字段
                        },
                        {
                            'xyxy': [520, 200, 620, 280],
                            'conf': 0.76,
                            'label': 'truck'
                            # 注意：没有color字段
                        }
                    ]
                }
            }
            
            detection_result = {'detections': postprocess_result['details']['detections']}
            frame_shape = frame.shape
            
            print(f"✅ 测试数据：")
            print(f"  - 告警状态: {postprocess_result['hit']}")
            print(f"  - 检测数量: {len(postprocess_result['details']['detections'])}")
            print(f"  - 算法包颜色: 无")
            
            # 解析结果
            parsed_result = TaskExecutionService._parse_postprocess_result(
                postprocess_result, detection_result, algorithm_config, frame_shape
            )
            
            alert_targets = parsed_result['details'].get('alert_targets', [])
            print(f"\n✅ 解析结果：")
            print(f"  - 告警目标数量: {len(alert_targets)}")
            for i, target in enumerate(alert_targets):
                print(f"    目标{i+1}: 原始颜色={target.get('original_color')}")
            
            # 绘制结果
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, parsed_result)
            
            cv2.imwrite("test_alert_without_algorithm_color.jpg", annotated_frame)
            print(f"\n📸 保存结果: test_alert_without_algorithm_color.jpg")
            print(f"  ℹ️  应该显示我们的默认紫色检测框（车辆告警）")
            
            return len(alert_targets) > 0
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_no_alert(self):
        """测试无告警状态"""
        print(f"\n{'='*60}")
        print("测试：无告警状态")
        print(f"{'='*60}")
        
        try:
            frame = self.create_test_frame()
            algorithm_config = self.create_algorithm_config()
            
            # 算法包输出：无告警状态
            postprocess_result = {
                'hit': False,
                'message': '未检测到违规',
                'details': {
                    'detections': [
                        {
                            'xyxy': [300, 400, 380, 480],
                            'conf': 0.82,
                            'color': [0, 255, 0],  # 绿色
                            'label': 'car'
                        }
                    ]
                }
            }
            
            detection_result = {'detections': postprocess_result['details']['detections']}
            frame_shape = frame.shape
            
            print(f"✅ 测试数据：")
            print(f"  - 告警状态: {postprocess_result['hit']}")
            print(f"  - 检测数量: {len(postprocess_result['details']['detections'])}")
            
            # 解析结果
            parsed_result = TaskExecutionService._parse_postprocess_result(
                postprocess_result, detection_result, algorithm_config, frame_shape
            )
            
            alert_targets = parsed_result['details'].get('alert_targets', [])
            print(f"\n✅ 解析结果：")
            print(f"  - 告警目标数量: {len(alert_targets)}")
            
            # 绘制结果
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, parsed_result)
            
            cv2.imwrite("test_no_alert_simplified.jpg", annotated_frame)
            print(f"\n📸 保存结果: test_no_alert_simplified.jpg")
            print(f"  ℹ️  应该只显示配置区域，不显示检测框")
            
            return len(alert_targets) == 0
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主测试函数"""
    print("🔍 简化告警逻辑测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = SimplifiedAlertLogicTester()
    
    # 运行测试
    test_results = []
    
    print(f"\n🧪 第一阶段：测试告警状态 + 算法包提供颜色")
    result1 = tester.test_alert_with_algorithm_color()
    test_results.append(("告警+算法包颜色", result1))
    
    print(f"\n🧪 第二阶段：测试告警状态 + 无算法包颜色")
    result2 = tester.test_alert_without_algorithm_color()
    test_results.append(("告警+默认颜色", result2))
    
    print(f"\n🧪 第三阶段：测试无告警状态")
    result3 = tester.test_no_alert()
    test_results.append(("无告警", result3))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("🏁 简化告警逻辑测试结果汇总")
    print(f"{'='*60}")
    
    print(f"📊 测试结果:")
    success_count = 0
    for name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n🎯 总体评估:")
    if success_count == len(test_results):
        print("✅ 所有测试通过！简化告警逻辑工作正常")
        print("🔧 新逻辑特点:")
        print("  1. ✅ 优先使用算法包提供的颜色")
        print("  2. ✅ 无颜色时使用我们的默认颜色")
        print("  3. ✅ 只关注是否告警，不过度依赖颜色判断")
        print("  4. ✅ 逻辑简单清晰，易于维护")
    elif success_count > 0:
        print(f"⚠️  部分测试通过 ({success_count}/{len(test_results)})")
    else:
        print("❌ 所有测试失败，需要进一步调试")
    
    print(f"\n📸 生成的测试图片:")
    print("  - test_alert_with_algorithm_color.jpg: 使用算法包颜色的告警框")
    print("  - test_alert_without_algorithm_color.jpg: 使用默认颜色的告警框")
    print("  - test_no_alert_simplified.jpg: 无告警状态")
    
    return success_count == len(test_results)


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
