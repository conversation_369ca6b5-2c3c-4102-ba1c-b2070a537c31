"""
人员入侵算法简洁测试
直接调用模型和后处理器进行测试
"""

import sys
import os
import cv2
import numpy as np
import yaml
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "model"))
sys.path.insert(0, str(current_dir / "postprocessor"))

# 导入模型和后处理器
from zql_detect import Model
from person_intrusion import Postprocessor


class PersonIntrusionDetector:
    """人员入侵检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.model = None
        self.postprocessor = None
        self.config = None
        
    def load_config(self):
        """加载配置文件"""
        try:
            # 读取模型配置
            with open('model/model.yaml', 'r', encoding='utf-8') as f:
                model_config = yaml.safe_load(f)
            
            # 读取后处理器配置
            with open('postprocessor/postprocessor.yaml', 'r', encoding='utf-8') as f:
                postprocessor_config = yaml.safe_load(f)
            
            self.config = {
                'model': model_config,
                'postprocessor': postprocessor_config
            }
            
            print("✅ 配置文件加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def initialize_model(self):
        """初始化模型"""
        try:
            if not self.config:
                print("❌ 请先加载配置文件")
                return False
            
            # 获取模型配置
            zql_person_config = self.config['model']['zql_person']
            
            # 创建模型实例
            self.model = Model(
                acc_id=0,
                name="person_intrusion",
                conf=zql_person_config['args']
            )
            
            if not self.model.status:
                print("❌ 模型初始化失败")
                return False
            
            print("✅ 模型初始化成功")
            print(f"   - 设备: {self.model.device}")
            print(f"   - 图像尺寸: {self.model.img_size}")
            return True
            
        except Exception as e:
            print(f"❌ 模型初始化失败: {e}")
            return False
    
    def initialize_postprocessor(self):
        """初始化后处理器"""
        try:
            if not self.config:
                print("❌ 请先加载配置文件")
                return False
            
            # 创建后处理器实例
            self.postprocessor = Postprocessor(
                source_id=0,
                alg_name="person_intrusion"
            )
            
            # 配置后处理器参数（模拟从数据库传来的配置）
            self.postprocessor.reserved_args = {
                'strategy': 'center',  # 检测策略：center表示检测目标中心点是否在区域内
                'polygons': {
                    'polygon_0': {
                        'polygon': [[160, 120], [480, 120], [480, 360], [160, 360]],  # 中心禁区坐标
                        'color': [255, 0, 0],  # 红色
                        'thickness': 2
                    }
                },
                'confidence_threshold': 0.25,  # 置信度阈值
                'alert_interval': 5,  # 告警间隔（秒）
                'alert_message': '检测到人员入侵禁区'  # 告警消息
            }
            
            print("✅ 后处理器初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 后处理器初始化失败: {e}")
            return False
    
    def detect_intrusion(self, image_path):
        """
        检测人员入侵
        
        Args:
            image_path: 图像路径
            
        Returns:
            dict: 检测结果
        """
        try:
            if not self.model or not self.postprocessor:
                print("❌ 请先初始化模型和后处理器")
                return None
            
            # 读取图像
            if not os.path.exists(image_path):
                print(f"❌ 图像文件不存在: {image_path}")
                return None
            
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法读取图像: {image_path}")
                return None
            
            print(f"📸 处理图像: {os.path.basename(image_path)} ({image.shape})")
            
            # 转换为RGB格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 模型检测
            print("🔍 进行目标检测...")
            detection_result = self.model.detect(image_rgb)
            
            # 后处理告警判断
            print("⚙️ 进行后处理告警判断...")
            alert_result = self.postprocessor.process(detection_result)
            
            # 分析结果
            is_alert = alert_result.get('hit', False)
            alert_message = alert_result.get('message', '')
            
            # 统计检测信息
            engine_results = detection_result.get('model_name', {}).get('engine_result', [])
            person_count = sum(1 for det in engine_results if det.get('label') == 0)
            
            result = {
                'image_path': image_path,
                'image_shape': image.shape,
                'detection_result': detection_result,
                'alert_result': alert_result,
                'is_alert': is_alert,
                'alert_message': alert_message,
                'total_detections': len(engine_results),
                'person_count': person_count,
                'success': True
            }
            
            print(f"🎯 检测完成:")
            print(f"   - 检测到目标: {len(engine_results)} 个")
            print(f"   - 检测到人员: {person_count} 人")
            print(f"   - 是否告警: {'🚨 是' if is_alert else '✅ 否'}")
            if alert_message:
                print(f"   - 告警消息: {alert_message}")
            
            return result
            
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            return {
                'image_path': image_path,
                'success': False,
                'error': str(e)
            }
    
    def save_result_image(self, result, output_path=None):
        """
        保存结果图像
        
        Args:
            result: 检测结果
            output_path: 输出路径，如果为None则自动生成
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if not result or not result.get('success'):
                print("❌ 无效的检测结果")
                return None
            
            # 读取原始图像
            image = cv2.imread(result['image_path'])
            if image is None:
                print("❌ 无法读取原始图像")
                return None
            
            result_image = image.copy()
            
            # 绘制检测结果
            alert_result = result['alert_result']
            if 'data' in alert_result and 'bbox' in alert_result['data']:
                bbox_data = alert_result['data']['bbox']
                
                # 绘制多边形区域
                if 'polygons' in bbox_data:
                    for polygon_data in bbox_data['polygons'].values():
                        points = np.array(polygon_data['polygon'], np.int32)
                        color = tuple(polygon_data.get('color', [0, 255, 0]))
                        bgr_color = (color[2], color[1], color[0])  # RGB转BGR
                        cv2.polylines(result_image, [points], True, bgr_color, 2)
                        
                        # 添加区域标签
                        cv2.putText(result_image, "RESTRICTED AREA", 
                                   (points[0][0], points[0][1]-10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, bgr_color, 2)
                
                # 绘制检测框
                if 'rectangles' in bbox_data:
                    for rect in bbox_data['rectangles']:
                        if 'xyxy' in rect:
                            x1, y1, x2, y2 = map(int, rect['xyxy'])
                            color = rect.get('color', [0, 255, 0])
                            bgr_color = (color[2], color[1], color[0])  # RGB转BGR
                            
                            cv2.rectangle(result_image, (x1, y1), (x2, y2), bgr_color, 2)
                            
                            label = f"{rect.get('label', 'person')} {rect.get('conf', 0):.2f}"
                            cv2.putText(result_image, label, (x1, y1-10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, bgr_color, 2)
            
            # 添加状态信息
            status_text = "ALERT: INTRUSION DETECTED!" if result['is_alert'] else "SAFE: NO INTRUSION"
            status_color = (0, 0, 255) if result['is_alert'] else (0, 255, 0)
            cv2.putText(result_image, status_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, status_color, 2)
            
            # 添加统计信息
            stats_text = f"Persons: {result['person_count']} | Total: {result['total_detections']}"
            cv2.putText(result_image, stats_text, (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 添加时间戳
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(result_image, timestamp, (10, result_image.shape[0]-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 生成输出路径
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(result['image_path']))[0]
                output_path = f"{base_name}_intrusion_result.jpg"
            
            # 保存图像
            cv2.imwrite(output_path, result_image)
            print(f"💾 结果图像已保存: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 保存结果图像失败: {e}")
            return None


def test_person_intrusion_simple():
    """简单测试人员入侵检测"""
    print("🚨 人员入侵算法简洁测试")
    print("=" * 60)
    
    # 创建检测器
    detector = PersonIntrusionDetector()
    
    # 初始化
    if not detector.load_config():
        return False
    
    if not detector.initialize_model():
        return False
    
    if not detector.initialize_postprocessor():
        return False
    
    # 测试图像列表
    test_images = [
        "D:\\image.jpg",
        "D:\\test1.jpg", 
        "D:\\people.jpg"
    ]
    
    success_count = 0
    
    # 处理每张测试图像
    for i, image_path in enumerate(test_images, 1):
        print(f"\n📸 测试图像 {i}: {os.path.basename(image_path)}")
        print("-" * 40)
        
        # 检测入侵
        result = detector.detect_intrusion(image_path)
        
        if result and result.get('success'):
            # 保存结果图像
            detector.save_result_image(result)
            success_count += 1
        else:
            print(f"⚠️ 图像处理失败")
    
    print(f"\n🎯 测试完成: {success_count}/{len(test_images)} 成功")
    return success_count > 0


if __name__ == "__main__":
    test_person_intrusion_simple()
