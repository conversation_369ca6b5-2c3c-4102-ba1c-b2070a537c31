<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑检测区域和线段"
    width="85%"
    :before-close="handleClose"
    class="geometry-editor-dialog"
  >
    <div class="geometry-editor">
      <div class="editor-toolbar">
        <div class="toolbar-section">
          <el-button
            type="success"
            @click="refreshFrame"
            :loading="loadingFrame"
            icon="Refresh"
            size="small"
          >
            {{ loadingFrame ? '获取中...' : '刷新画面' }}
          </el-button>
        </div>
        
        <div v-if="supportsPolygons || supportsLines" class="toolbar-section">
          <el-radio-group v-model="drawingMode" size="small">
            <el-radio-button v-if="supportsPolygons" label="polygon">绘制区域</el-radio-button>
            <el-radio-button v-if="supportsLines" label="line">绘制线段</el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="toolbar-section">
          <el-button
            type="primary"
            @click="startDrawing"
            :disabled="isDrawing"
            icon="Edit"
            size="small"
          >
            {{ isDrawing ? '绘制中...' : '开始绘制' }}
          </el-button>
          <el-button
            @click="finishDrawing"
            :disabled="!canFinishDrawing"
            icon="Check"
            size="small"
          >
            完成绘制
          </el-button>
          <el-button
            @click="cancelDrawing"
            :disabled="!isDrawing"
            icon="Close"
            size="small"
          >
            取消绘制
          </el-button>
        </div>
        
        <div class="toolbar-section">
          <el-button
            type="danger"
            @click="clearAll"
            icon="Delete"
            size="small"
          >
            清空所有
          </el-button>
        </div>
      </div>

      <div class="editor-content">
        <div class="canvas-container">
          <canvas
            ref="canvasRef"
            @click="handleCanvasClick"
            @mousemove="handleCanvasMouseMove"
            class="drawing-canvas"
          />
          <div v-if="!frameImageLoaded" class="canvas-placeholder">
            <el-icon><Picture /></el-icon>
            <p>点击"刷新画面"获取视频帧</p>
          </div>
        </div>
        
        <div class="geometry-list">
          <!-- 多边形列表 -->
          <div v-if="supportsPolygons && polygons.length > 0" class="list-section">
            <h5>检测区域 ({{ polygons.length }}个)</h5>
            <div class="geometry-items">
              <div
                v-for="(polygon, index) in polygons"
                :key="`polygon-${index}`"
                class="geometry-item"
                :class="{ active: selectedPolygon === index }"
                @click="selectPolygon(index)"
              >
                <span>区域 {{ index + 1 }} ({{ polygon.length }}个点)</span>
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="removePolygon(index)"
                  icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>

          <!-- 线段列表 -->
          <div v-if="supportsLines && lines.length > 0" class="list-section">
            <h5>检测线段 ({{ lines.length }}条)</h5>
            <div class="geometry-items">
              <div
                v-for="(line, index) in lines"
                :key="`line-${index}`"
                class="geometry-item"
                :class="{ active: selectedLine === index }"
                @click="selectLine(index)"
              >
                <span>线段 {{ index + 1 }} ({{ line.length }}个点)</span>
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="removeLine(index)"
                  icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 绘制提示 -->
          <div v-if="(!supportsPolygons || polygons.length === 0) && (!supportsLines || lines.length === 0)" class="empty-state">
            <el-icon><Location /></el-icon>
            <p>选择绘制模式后点击"开始绘制"</p>
            <p class="hint">
              <template v-if="supportsPolygons">区域需要至少3个点</template>
              <template v-if="supportsPolygons && supportsLines">，</template>
              <template v-if="supportsLines">线段需要至少2个点</template>
            </p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveGeometry">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Location } from '@element-plus/icons-vue'
import { getRtspFrame } from '@/api/stream_manage/stream'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  polygons: {
    type: Array,
    default: () => []
  },
  lines: {
    type: Array,
    default: () => []
  },
  videoUrl: {
    type: String,
    default: ''
  },
  supportsPolygons: {
    type: Boolean,
    default: true
  },
  supportsLines: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'update:polygons', 'update:lines'])

// 对话框状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 绘制状态 - 根据支持的功能设置默认模式
const drawingMode = ref(props.supportsPolygons ? 'polygon' : 'line')
const isDrawing = ref(false)
const currentPolygon = ref([])
const currentLine = ref([])
const selectedPolygon = ref(-1)
const selectedLine = ref(-1)

// 画布相关
const canvasRef = ref(null)
const frameImage = ref(null)
const frameImageLoaded = ref(false)
const loadingFrame = ref(false)

// 内部数据
const internalPolygons = ref([])
const internalLines = ref([])

// 计算属性
const canFinishDrawing = computed(() => {
  if (!isDrawing.value) return false
  if (drawingMode.value === 'polygon') {
    return currentPolygon.value.length >= 3
  } else {
    return currentLine.value.length >= 2
  }
})

// 监听props变化
watch(() => props.polygons, (newPolygons) => {
  internalPolygons.value = [...newPolygons]
}, { immediate: true, deep: true })

watch(() => props.lines, (newLines) => {
  internalLines.value = [...newLines]
}, { immediate: true, deep: true })

// 计算属性返回当前数据
const polygons = computed(() => internalPolygons.value)
const lines = computed(() => internalLines.value)

// 方法
const refreshFrame = async () => {
  // 检查对话框是否仍然打开
  if (!dialogVisible.value) {
    console.log('几何编辑器已关闭，取消视频帧获取')
    return
  }

  if (!props.videoUrl) {
    ElMessage.warning('视频URL为空，无法获取画面')
    return
  }

  loadingFrame.value = true
  try {
    const frameData = {
      rtspUrl: props.videoUrl,
      timeout: 5
    }

    const response = await getRtspFrame(frameData)

    // 再次检查对话框是否仍然打开（异步操作完成后）
    if (!dialogVisible.value) {
      console.log('几何编辑器在请求过程中已关闭，忽略响应')
      return
    }

    if (response.code === 200 && response.data && response.data.frame) {
      // 创建图片对象
      const img = new Image()
      img.onload = () => {
        // 最终检查对话框是否仍然打开
        if (!dialogVisible.value) {
          console.log('几何编辑器在图片加载过程中已关闭，忽略结果')
          return
        }
        frameImage.value = img
        frameImageLoaded.value = true
        redrawCanvas()
        ElMessage.success('视频帧获取成功')
      }
      img.onerror = () => {
        if (dialogVisible.value) {
          ElMessage.error('视频帧加载失败')
        }
      }
      img.src = 'data:image/jpeg;base64,' + response.data.frame
    } else {
      if (dialogVisible.value) {
        ElMessage.error(response.msg || '获取视频帧失败')
      }
    }
  } catch (error) {
    console.error('获取视频帧失败:', error)
    if (dialogVisible.value) {
      ElMessage.error('获取视频帧失败，请检查视频流地址')
    }
  } finally {
    loadingFrame.value = false
  }
}

const initCanvas = () => {
  const canvas = canvasRef.value
  if (!canvas) return
  
  // 设置画布尺寸
  canvas.width = 640
  canvas.height = 480
  
  redrawCanvas()
}

const redrawCanvas = () => {
  const canvas = canvasRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 绘制背景（如果有视频帧）
  if (frameImageLoaded.value && frameImage.value) {
    // 绘制视频帧作为背景
    ctx.drawImage(frameImage.value, 0, 0, canvas.width, canvas.height)
  } else {
    // 绘制默认背景
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    ctx.fillStyle = '#666'
    ctx.font = '16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('点击"刷新画面"获取视频帧', canvas.width / 2, canvas.height / 2)
  }

  // 绘制已保存的多边形
  drawPolygons(ctx)

  // 绘制已保存的线段
  drawLines(ctx)

  // 绘制当前正在绘制的图形
  if (isDrawing.value) {
    if (drawingMode.value === 'polygon') {
      drawCurrentPolygon(ctx)
    } else {
      drawCurrentLine(ctx)
    }
  }
}

const drawPolygons = (ctx) => {
  polygons.value.forEach((polygon, index) => {
    if (polygon.length < 3) return
    
    ctx.beginPath()
    ctx.strokeStyle = selectedPolygon.value === index ? '#ff4444' : '#409eff'
    ctx.fillStyle = selectedPolygon.value === index ? 'rgba(255, 68, 68, 0.1)' : 'rgba(64, 158, 255, 0.1)'
    ctx.lineWidth = 2
    
    const firstPoint = polygon[0]
    ctx.moveTo(firstPoint.x * ctx.canvas.width, firstPoint.y * ctx.canvas.height)
    
    for (let i = 1; i < polygon.length; i++) {
      const point = polygon[i]
      ctx.lineTo(point.x * ctx.canvas.width, point.y * ctx.canvas.height)
    }
    
    ctx.closePath()
    ctx.fill()
    ctx.stroke()
    
    // 绘制顶点
    polygon.forEach(point => {
      ctx.beginPath()
      ctx.arc(point.x * ctx.canvas.width, point.y * ctx.canvas.height, 4, 0, 2 * Math.PI)
      ctx.fillStyle = selectedPolygon.value === index ? '#ff4444' : '#409eff'
      ctx.fill()
    })
  })
}

const drawLines = (ctx) => {
  lines.value.forEach((line, index) => {
    if (line.length < 2) return
    
    ctx.beginPath()
    ctx.strokeStyle = selectedLine.value === index ? '#ff4444' : '#67c23a'
    ctx.lineWidth = 3
    
    const firstPoint = line[0]
    ctx.moveTo(firstPoint.x * ctx.canvas.width, firstPoint.y * ctx.canvas.height)
    
    for (let i = 1; i < line.length; i++) {
      const point = line[i]
      ctx.lineTo(point.x * ctx.canvas.width, point.y * ctx.canvas.height)
    }
    
    ctx.stroke()
    
    // 绘制顶点
    line.forEach(point => {
      ctx.beginPath()
      ctx.arc(point.x * ctx.canvas.width, point.y * ctx.canvas.height, 4, 0, 2 * Math.PI)
      ctx.fillStyle = selectedLine.value === index ? '#ff4444' : '#67c23a'
      ctx.fill()
    })
  })
}

const drawCurrentPolygon = (ctx) => {
  if (currentPolygon.value.length === 0) return

  ctx.beginPath()
  ctx.strokeStyle = '#f56c6c'
  ctx.fillStyle = 'rgba(245, 108, 108, 0.1)'
  ctx.lineWidth = 2
  ctx.setLineDash([5, 5])

  const firstPoint = currentPolygon.value[0]
  ctx.moveTo(firstPoint.x * ctx.canvas.width, firstPoint.y * ctx.canvas.height)

  for (let i = 1; i < currentPolygon.value.length; i++) {
    const point = currentPolygon.value[i]
    ctx.lineTo(point.x * ctx.canvas.width, point.y * ctx.canvas.height)
  }

  if (currentPolygon.value.length >= 3) {
    ctx.closePath()
    ctx.fill()
  }

  ctx.stroke()
  ctx.setLineDash([])

  // 绘制顶点
  currentPolygon.value.forEach(point => {
    ctx.beginPath()
    ctx.arc(point.x * ctx.canvas.width, point.y * ctx.canvas.height, 4, 0, 2 * Math.PI)
    ctx.fillStyle = '#f56c6c'
    ctx.fill()
  })
}

const drawCurrentLine = (ctx) => {
  if (currentLine.value.length === 0) return

  ctx.beginPath()
  ctx.strokeStyle = '#f56c6c'
  ctx.lineWidth = 3
  ctx.setLineDash([5, 5])

  const firstPoint = currentLine.value[0]
  ctx.moveTo(firstPoint.x * ctx.canvas.width, firstPoint.y * ctx.canvas.height)

  for (let i = 1; i < currentLine.value.length; i++) {
    const point = currentLine.value[i]
    ctx.lineTo(point.x * ctx.canvas.width, point.y * ctx.canvas.height)
  }

  ctx.stroke()
  ctx.setLineDash([])

  // 绘制顶点
  currentLine.value.forEach(point => {
    ctx.beginPath()
    ctx.arc(point.x * ctx.canvas.width, point.y * ctx.canvas.height, 4, 0, 2 * Math.PI)
    ctx.fillStyle = '#f56c6c'
    ctx.fill()
  })
}

const startDrawing = () => {
  isDrawing.value = true
  selectedPolygon.value = -1
  selectedLine.value = -1

  if (drawingMode.value === 'polygon') {
    currentPolygon.value = []
  } else {
    currentLine.value = []
  }

  ElMessage.info(`开始绘制${drawingMode.value === 'polygon' ? '区域' : '线段'}，点击画布添加点`)
}

const finishDrawing = () => {
  if (!canFinishDrawing.value) return

  if (drawingMode.value === 'polygon') {
    internalPolygons.value.push([...currentPolygon.value])
    currentPolygon.value = []
    ElMessage.success('区域绘制完成')
  } else {
    internalLines.value.push([...currentLine.value])
    currentLine.value = []
    ElMessage.success('线段绘制完成')
  }

  isDrawing.value = false
  redrawCanvas()
}

const cancelDrawing = () => {
  isDrawing.value = false
  currentPolygon.value = []
  currentLine.value = []
  redrawCanvas()
  ElMessage.info('取消绘制')
}

const clearAll = () => {
  internalPolygons.value = []
  internalLines.value = []
  currentPolygon.value = []
  currentLine.value = []
  isDrawing.value = false
  selectedPolygon.value = -1
  selectedLine.value = -1
  redrawCanvas()
  ElMessage.success('已清空所有图形')
}

const handleCanvasClick = (event) => {
  if (!isDrawing.value) return

  const canvas = canvasRef.value
  const rect = canvas.getBoundingClientRect()
  const x = (event.clientX - rect.left) / canvas.offsetWidth
  const y = (event.clientY - rect.top) / canvas.offsetHeight

  const point = { x, y }

  if (drawingMode.value === 'polygon') {
    currentPolygon.value.push(point)
  } else {
    currentLine.value.push(point)
  }

  redrawCanvas()
}

const handleCanvasMouseMove = () => {
  // 可以在这里添加鼠标跟踪预览功能
}

const selectPolygon = (index) => {
  selectedPolygon.value = selectedPolygon.value === index ? -1 : index
  selectedLine.value = -1
  redrawCanvas()
}

const selectLine = (index) => {
  selectedLine.value = selectedLine.value === index ? -1 : index
  selectedPolygon.value = -1
  redrawCanvas()
}

const removePolygon = (index) => {
  internalPolygons.value.splice(index, 1)
  if (selectedPolygon.value === index) {
    selectedPolygon.value = -1
  } else if (selectedPolygon.value > index) {
    selectedPolygon.value--
  }
  redrawCanvas()
  ElMessage.success('区域已删除')
}

const removeLine = (index) => {
  internalLines.value.splice(index, 1)
  if (selectedLine.value === index) {
    selectedLine.value = -1
  } else if (selectedLine.value > index) {
    selectedLine.value--
  }
  redrawCanvas()
  ElMessage.success('线段已删除')
}

const saveGeometry = () => {
  console.log('=== GeometryEditor 保存几何数据 ===')
  console.log('保存的多边形数据:', internalPolygons.value)
  console.log('保存的线段数据:', internalLines.value)

  emit('update:polygons', [...internalPolygons.value])
  emit('update:lines', [...internalLines.value])
  dialogVisible.value = false
  ElMessage.success('保存成功')
}

const handleClose = () => {
  if (isDrawing.value) {
    ElMessage.warning('请先完成或取消当前绘制')
    return false
  }
  dialogVisible.value = false
}

// 监听对话框打开和关闭
watch(dialogVisible, (visible) => {
  if (visible) {
    // 对话框打开时初始化
    nextTick(() => {
      initCanvas()
      if (props.videoUrl) {
        refreshFrame()
      }
    })
  } else {
    // 对话框关闭时清理状态
    console.log('=== 几何编辑器对话框关闭，清理状态 ===')
    loadingFrame.value = false
    frameImage.value = null
    frameImageLoaded.value = false
    isDrawing.value = false
    currentPolygon.value = []
    currentLine.value = []
    selectedPolygon.value = -1
    selectedLine.value = -1
    console.log('几何编辑器状态清理完成')
  }
})
</script>

<style scoped>
.geometry-editor-dialog {
  --el-dialog-padding-primary: 20px;
}

.geometry-editor {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-content {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 0;
}

.canvas-container {
  flex: 1;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f7fa;
}

.drawing-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
  display: block;
}

.canvas-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #909399;
}

.canvas-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.geometry-list {
  width: 280px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background: white;
  overflow-y: auto;
}

.list-section {
  margin-bottom: 20px;
}

.list-section:last-child {
  margin-bottom: 0;
}

.list-section h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.geometry-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.geometry-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.geometry-item:hover {
  background: #e6f7ff;
}

.geometry-item.active {
  background: #e6f7ff;
  border: 1px solid #409eff;
}

.empty-state {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-state .hint {
  font-size: 12px;
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
