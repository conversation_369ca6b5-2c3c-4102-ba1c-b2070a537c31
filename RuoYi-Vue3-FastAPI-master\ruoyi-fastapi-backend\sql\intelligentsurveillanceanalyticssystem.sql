/*
 Navicat Premium Dump SQL

 Source Server         : mysql连接
 Source Server Type    : MySQL
 Source Server Version : 80405 (8.4.5)
 Source Host           : localhost:3306
 Source Schema         : intelligentsurveillanceanalyticssystem

 Target Server Type    : MySQL
 Target Server Version : 80405 (8.4.5)
 File Encoding         : 65001

 Date: 26/07/2025 18:34:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for apscheduler_jobs
-- ----------------------------
DROP TABLE IF EXISTS `apscheduler_jobs`;
CREATE TABLE `apscheduler_jobs`  (
  `id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `next_run_time` double NULL DEFAULT NULL,
  `job_state` blob NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_apscheduler_jobs_next_run_time`(`next_run_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of apscheduler_jobs
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码生成业务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (6, 'surveillance_alert', '监控告警记录表', NULL, NULL, 'SurveillanceAlert', 'crud', '', 'module_admin.system', 'system', 'alert', '监控告警记录', 'insistence', '0', '/', NULL, 'admin', '2025-07-25 09:55:19', 'admin', '2025-07-25 09:55:19', NULL);

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列类型',
  `python_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'PYTHON类型',
  `python_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'PYTHON字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_unique` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否唯一（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (81, 6, 'alert_id', '告警ID', 'int', 'int', 'alertId', '1', '1', '0', NULL, '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-07-25 09:55:19', 'admin', '2025-07-25 09:55:19');
INSERT INTO `gen_table_column` VALUES (82, 6, 'task_id', '任务ID', 'int', 'int', 'taskId', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (83, 6, 'stream_id', '视频流ID', 'int', 'int', 'streamId', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (84, 6, 'alert_type', '告警类型', 'varchar(100)', 'str', 'alertType', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'select', '', 4, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (85, 6, 'alert_level', '告警级别：1-低，2-中，3-高', 'varchar(10)', 'str', 'alertLevel', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 5, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (86, 6, 'alert_message', '告警消息', 'varchar(500)', 'str', 'alertMessage', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 6, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (87, 6, 'alert_time', '告警时间', 'datetime', 'datetime', 'alertTime', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'datetime', '', 7, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (88, 6, 'screenshot_path', '告警截图路径', 'varchar(500)', 'str', 'screenshotPath', '0', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 8, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (89, 6, 'bbox_info', '检测框信息（JSON格式）', 'text', 'str', 'bboxInfo', '0', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'textarea', '', 9, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (90, 6, 'confidence', '检测置信度', 'float', 'float', 'confidence', '0', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 10, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (91, 6, 'status', '处理状态：0-未处理，1-已处理，2-已忽略', 'varchar(10)', 'str', 'status', '0', '0', '1', NULL, '1', '1', '1', '1', 'EQ', 'radio', '', 11, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (92, 6, 'handle_user', '处理人', 'varchar(50)', 'str', 'handleUser', '0', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 12, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (93, 6, 'handle_time', '处理时间', 'datetime', 'datetime', 'handleTime', '0', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'datetime', '', 13, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (94, 6, 'handle_remark', '处理备注', 'varchar(500)', 'str', 'handleRemark', '0', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 14, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (95, 6, 'create_by', '创建者', 'varchar(50)', 'str', 'createBy', '0', '0', '0', NULL, '1', NULL, NULL, NULL, 'EQ', 'input', '', 15, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (96, 6, 'create_time', '创建时间', 'datetime', 'datetime', 'createTime', '0', '0', '0', NULL, '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 16, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (97, 6, 'update_by', '更新者', 'varchar(50)', 'str', 'updateBy', '0', '0', '0', NULL, '1', '1', NULL, NULL, 'EQ', 'input', '', 17, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (98, 6, 'update_time', '更新时间', 'datetime', 'datetime', 'updateTime', '0', '0', '0', NULL, '1', '1', NULL, NULL, 'EQ', 'datetime', '', 18, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (99, 6, 'remark', '备注', 'varchar(500)', 'str', 'remark', '0', '0', '0', NULL, '1', '1', '1', NULL, 'EQ', 'input', '', 19, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');
INSERT INTO `gen_table_column` VALUES (100, 6, 'del_flag', '删除标志：0-正常，1-删除', 'varchar(1)', 'str', 'delFlag', '0', '0', '1', NULL, '1', NULL, NULL, NULL, 'EQ', 'input', '', 20, 'admin', '2025-07-25 09:55:20', 'admin', '2025-07-25 09:55:20');

-- ----------------------------
-- Table structure for surveillance_alert
-- ----------------------------
DROP TABLE IF EXISTS `surveillance_alert`;
CREATE TABLE `surveillance_alert`  (
  `alert_id` int NOT NULL AUTO_INCREMENT COMMENT '告警ID',
  `task_id` int NOT NULL COMMENT '任务ID',
  `stream_id` int NOT NULL COMMENT '视频流ID',
  `alert_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警类型',
  `alert_level` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '告警级别：1-低，2-中，3-高',
  `alert_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警消息',
  `alert_time` datetime NOT NULL COMMENT '告警时间',
  `screenshot_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '告警截图路径',
  `bbox_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '检测框信息（JSON格式）',
  `confidence` float NULL DEFAULT 0 COMMENT '检测置信度',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '处理状态：0-未处理，1-已处理，2-已忽略',
  `handle_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理人',
  `handle_time` datetime NULL DEFAULT NULL COMMENT '处理时间',
  `handle_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理备注',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`alert_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_stream_id`(`stream_id` ASC) USING BTREE,
  INDEX `idx_alert_time`(`alert_time` ASC) USING BTREE,
  INDEX `idx_alert_type`(`alert_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 129 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '监控告警记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of surveillance_alert
-- ----------------------------

-- ----------------------------
-- Table structure for surveillance_config
-- ----------------------------
DROP TABLE IF EXISTS `surveillance_config`;
CREATE TABLE `surveillance_config`  (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配置值',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) NULL DEFAULT 0 COMMENT '是否系统配置',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`config_id`) USING BTREE,
  UNIQUE INDEX `uk_surveillance_config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_surveillance_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of surveillance_config
-- ----------------------------
INSERT INTO `surveillance_config` VALUES (1, 'system.model.upload_path', '/uploads/models', 'string', '模型文件上传路径', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (2, 'system.alert.image_path', '/uploads/alerts/images', 'string', '告警图片保存路径', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (3, 'system.alert.video_path', '/uploads/alerts/videos', 'string', '告警视频保存路径', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (4, 'system.alert.retention_days', '30', 'integer', '告警记录保留天数', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (5, 'system.detection.max_fps', '25', 'integer', '最大检测帧率', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (6, 'system.detection.frame_interval', '200', 'integer', '检测帧间隔(毫秒)', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (7, 'system.rtsp.connection_timeout', '10', 'integer', 'RTSP连接超时时间(秒)', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);
INSERT INTO `surveillance_config` VALUES (8, 'system.rtsp.read_timeout', '5', 'integer', 'RTSP读取超时时间(秒)', 1, '0', 'admin', '2025-07-21 13:29:10', '', NULL);

-- ----------------------------
-- Table structure for surveillance_stream
-- ----------------------------
DROP TABLE IF EXISTS `surveillance_stream`;
CREATE TABLE `surveillance_stream`  (
  `stream_id` bigint NOT NULL AUTO_INCREMENT COMMENT '流ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `stream_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流名称',
  `rtsp_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'RTSP地址',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '安装位置',
  `stream_config` json NULL COMMENT '流配置参数',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '状态(0停用 1启用)',
  `is_recording` tinyint(1) NULL DEFAULT 0 COMMENT '是否录制',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`stream_id`) USING BTREE,
  UNIQUE INDEX `uk_user_stream_name`(`user_id` ASC, `stream_name` ASC) USING BTREE,
  UNIQUE INDEX `uk_user_rtsp_url`(`user_id` ASC, `rtsp_url` ASC) USING BTREE,
  INDEX `idx_surveillance_stream_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频流表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of surveillance_stream
-- ----------------------------
INSERT INTO `surveillance_stream` VALUES (7, 1, '监控1', 'rtsp://ai:ai123098@192.168.25.253:554/Streaming/Channels/101', '门口', 'null', '1', 0, '0', 'admin', '2025-07-21 16:51:04', 'admin', '2025-07-25 08:35:20', '门口的监控');
INSERT INTO `surveillance_stream` VALUES (8, 1, '本地车流测试', 'rtsp://127.0.0.1:8554/test1', 'A路口', 'null', '1', 0, '0', 'admin', '2025-07-25 13:49:47', 'admin', '2025-07-25 13:58:29', NULL);

-- ----------------------------
-- Table structure for surveillance_task
-- ----------------------------
DROP TABLE IF EXISTS `surveillance_task`;
CREATE TABLE `surveillance_task`  (
  `task_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `stream_id` bigint NOT NULL COMMENT '视频流ID',
  `algorithm_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '算法ID',
  `algorithm_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '算法名称',
  `algorithm_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '算法版本',
  `user_config` json NULL COMMENT '用户配置参数JSON',
  `config_id` bigint NULL DEFAULT NULL COMMENT '配置ID(保留字段)',
  `model_id` bigint NULL DEFAULT NULL COMMENT '模型ID(保留字段)',
  `algorithm_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '算法类型(person_intrusion/car_counting)',
  `algorithm_config` json NULL COMMENT '算法配置参数',
  `bbox_config` json NULL COMMENT '区域/线段配置',
  `alert_config` json NULL COMMENT '告警配置',
  `schedule_config` json NULL COMMENT '调度配置',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态(0停止 1运行 2暂停)',
  `last_run_time` datetime NULL DEFAULT NULL COMMENT '最后运行时间',
  `next_run_time` datetime NULL DEFAULT NULL COMMENT '下次运行时间',
  `run_count` int NULL DEFAULT 0 COMMENT '运行次数',
  `alert_count` int NULL DEFAULT 0 COMMENT '告警次数',
  `error_count` int NULL DEFAULT 0 COMMENT '错误次数',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`task_id`) USING BTREE,
  UNIQUE INDEX `uk_surveillance_task_name`(`task_name` ASC) USING BTREE,
  INDEX `idx_surveillance_task_stream`(`stream_id` ASC) USING BTREE,
  INDEX `idx_surveillance_task_model`(`model_id` ASC) USING BTREE,
  INDEX `idx_surveillance_task_algorithm`(`algorithm_type` ASC) USING BTREE,
  INDEX `idx_surveillance_task_status`(`status` ASC) USING BTREE,
  INDEX `idx_surveillance_task_algorithm_id`(`algorithm_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '检测任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of surveillance_task
-- ----------------------------
INSERT INTO `surveillance_task` VALUES (11, '人员的入侵', 7, 'person_intrusion', '区域入侵', 'ks968-v6.0', '{\"algorithm_id\": \"person_intrusion\", \"custom_params\": {}, \"algorithm_name\": \"person_intrusion\", \"detection_areas\": [{\"id\": \"area_1753403758355_0\", \"name\": \"区域1\", \"type\": \"polygon\", \"points\": [{\"x\": 0.3710360221674877, \"y\": 0.5140906813627254}, {\"x\": 0.3710360221674877, \"y\": 0.7305235470941884}, {\"x\": 0.6210360221674877, \"y\": 0.7024674348697395}, {\"x\": 0.5385237068965517, \"y\": 0.45797845691382766}]}], \"detection_lines\": [], \"exclusion_areas\": [], \"alert_parameters\": {\"enable_alert\": true, \"alert_message\": \"区域入侵告警，请注意有人入侵\", \"alert_interval\": 5, \"alert_threshold\": 3}, \"model_parameters\": {\"input_size\": 640, \"nms_threshold\": 0.5, \"max_detections\": 100, \"confidence_threshold\": 0.65}, \"algorithm_version\": \"1.0\"}', NULL, NULL, 'person_intrusion', '{\"version\": \"1.0\", \"created_at\": \"2025-07-25T08:35:58.390072\", \"model_params\": {\"input_size\": 640, \"nms_threshold\": 0.5, \"max_detections\": 100, \"confidence_threshold\": 0.65}, \"custom_params\": {}, \"algorithm_info\": {\"algorithm_id\": \"person_intrusion\", \"algorithm_name\": \"person_intrusion\", \"algorithm_version\": \"1.0\"}}', '{\"version\": \"1.0\", \"created_at\": \"2025-07-25T08:35:58.390072\", \"detection_areas\": [{\"id\": \"area_1753403758355_0\", \"name\": \"区域1\", \"type\": \"polygon\", \"points\": [{\"x\": 0.3710360221674877, \"y\": 0.5140906813627254}, {\"x\": 0.3710360221674877, \"y\": 0.7305235470941884}, {\"x\": 0.6210360221674877, \"y\": 0.7024674348697395}, {\"x\": 0.5385237068965517, \"y\": 0.45797845691382766}]}], \"detection_lines\": [], \"exclusion_areas\": []}', '{\"version\": \"1.0\", \"created_at\": \"2025-07-25T08:35:58.391031\", \"alert_params\": {\"enable_alert\": true, \"alert_message\": \"区域入侵告警，请注意有人入侵\", \"alert_interval\": 5, \"alert_threshold\": 3}}', NULL, '0', '2025-07-25 15:50:32', NULL, 0, 0, 0, '0', '1', '2025-07-25 08:35:58', '1', '2025-07-25 15:50:46', '');
INSERT INTO `surveillance_task` VALUES (12, '车辆检测', 8, 'car_counting', '车辆计数', 'ks968-v6.0', '{\"algorithm_id\": \"car_counting\", \"custom_params\": {}, \"algorithm_name\": \"car_counting\", \"detection_areas\": [{\"id\": \"area_1753423276239_0\", \"name\": \"区域1\", \"type\": \"polygon\", \"points\": [{\"x\": 0.49252988047808766, \"y\": 0.2710036574870912}, {\"x\": 0.5092629482071713, \"y\": 0.4517265490533563}, {\"x\": 0.5427290836653387, \"y\": 0.5257368760757315}, {\"x\": 0.6487051792828685, \"y\": 0.5188521944922547}, {\"x\": 0.624003984063745, \"y\": 0.3742738812392427}, {\"x\": 0.6016932270916334, \"y\": 0.26067663511187605}, {\"x\": 0.5793824701195219, \"y\": 0.24174376075731496}, {\"x\": 0.5371513944223107, \"y\": 0.24690727194492257}, {\"x\": 0.5148406374501993, \"y\": 0.25379195352839934}, {\"x\": 0.49810756972111553, \"y\": 0.25379195352839934}]}], \"detection_lines\": [], \"exclusion_areas\": [], \"alert_parameters\": {\"enable_alert\": true, \"alert_message\": \"车辆计数告警，有车辆经过\", \"alert_interval\": 5, \"alert_threshold\": 1}, \"model_parameters\": {\"input_size\": 640, \"nms_threshold\": 0.5, \"max_detections\": 100, \"confidence_threshold\": 0.01}, \"algorithm_version\": \"1.0\"}', NULL, NULL, 'car_counting', '{\"version\": \"1.0\", \"created_at\": \"2025-07-25T14:01:16.291676\", \"model_params\": {\"input_size\": 640, \"nms_threshold\": 0.5, \"max_detections\": 100, \"confidence_threshold\": 0.01}, \"custom_params\": {}, \"algorithm_info\": {\"algorithm_id\": \"car_counting\", \"algorithm_name\": \"car_counting\", \"algorithm_version\": \"1.0\"}}', '{\"version\": \"1.0\", \"created_at\": \"2025-07-25T14:01:16.292720\", \"detection_areas\": [{\"id\": \"area_1753423276239_0\", \"name\": \"区域1\", \"type\": \"polygon\", \"points\": [{\"x\": 0.49252988047808766, \"y\": 0.2710036574870912}, {\"x\": 0.5092629482071713, \"y\": 0.4517265490533563}, {\"x\": 0.5427290836653387, \"y\": 0.5257368760757315}, {\"x\": 0.6487051792828685, \"y\": 0.5188521944922547}, {\"x\": 0.624003984063745, \"y\": 0.3742738812392427}, {\"x\": 0.6016932270916334, \"y\": 0.26067663511187605}, {\"x\": 0.5793824701195219, \"y\": 0.24174376075731496}, {\"x\": 0.5371513944223107, \"y\": 0.24690727194492257}, {\"x\": 0.5148406374501993, \"y\": 0.25379195352839934}, {\"x\": 0.49810756972111553, \"y\": 0.25379195352839934}]}], \"detection_lines\": [], \"exclusion_areas\": []}', '{\"version\": \"1.0\", \"created_at\": \"2025-07-25T14:01:16.292720\", \"alert_params\": {\"enable_alert\": true, \"alert_message\": \"车辆计数告警，有车辆经过\", \"alert_interval\": 5, \"alert_threshold\": 1}}', NULL, '0', '2025-07-26 10:13:41', NULL, 0, 117, 0, '0', '1', '2025-07-25 14:01:16', '1', '2025-07-26 10:14:01', '');

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2025-07-21 13:29:09', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2025-07-21 13:29:09', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2025-07-21 13:29:09', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2025-07-21 13:29:09', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2025-07-21 13:29:09', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2025-07-21 13:29:09', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 200 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '集团总公司', 0, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '深圳分公司', 1, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '长沙分公司', 2, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (103, 101, '0,100,101', '研发部门', 1, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (105, 101, '0,100,101', '测试部门', 3, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '年糕', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'default', 'sys_job_group', '', '', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '数据库', 'sqlalchemy', 'sys_job_group', '', '', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '数据库分组');
INSERT INTO `sys_dict_data` VALUES (12, 3, 'redis', 'redis', 'sys_job_group', '', '', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, 'reids分组');
INSERT INTO `sys_dict_data` VALUES (13, 1, '默认', 'default', 'sys_job_executor', '', '', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '线程池');
INSERT INTO `sys_dict_data` VALUES (14, 2, '进程池', 'processpool', 'sys_job_executor', '', '', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '进程池');
INSERT INTO `sys_dict_data` VALUES (15, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (16, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (17, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (18, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (19, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (20, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (21, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (22, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (23, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (24, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (25, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (26, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (27, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (28, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (29, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (30, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (31, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (32, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '停用状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '任务执行器', 'sys_job_executor', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '任务执行器列表');
INSERT INTO `sys_dict_type` VALUES (7, '系统是否', 'sys_yes_no', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知类型', 'sys_notice_type', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (9, '通知状态', 'sys_notice_status', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (10, '操作类型', 'sys_oper_type', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (11, '系统状态', 'sys_common_status', '0', 'admin', '2025-07-21 13:29:09', '', NULL, '登录状态列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' COMMENT '任务组名',
  `job_executor` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'default' COMMENT '任务执行器',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_args` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '位置参数',
  `job_kwargs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '关键字参数',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'default', 'default', 'module_task.scheduler_test.job', NULL, NULL, '0/10 * * * * ?', '3', '1', '1', 'admin', '2025-07-21 13:29:09', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'default', 'default', 'module_task.scheduler_test.job', 'test', NULL, '0/15 * * * * ?', '3', '1', '1', 'admin', '2025-07-21 13:29:09', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'default', 'default', 'module_task.scheduler_test.job', 'new', '{\"test\": 111}', '0/20 * * * * ?', '3', '1', '1', 'admin', '2025-07-21 13:29:09', '', NULL, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `job_executor` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务执行器',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_args` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '位置参数',
  `job_kwargs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '关键字参数',
  `job_trigger` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '任务触发器',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 130 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-21 14:18:59');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-21 15:02:38');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-22 20:43:47');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-22 20:50:51');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 08:41:49');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 09:23:11');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 10:16:48');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 11:47:24');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '', '未知', 'Python Requests 2', 'Other', '1', '验证码已失效', '2025-07-23 11:49:20');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '', '未知', 'Python Requests 2', 'Other', '1', '验证码错误', '2025-07-23 13:22:57');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 14:46:57');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '', '未知', 'Edge 138', 'Windows 10', '1', '验证码已失效', '2025-07-23 15:05:04');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 15:05:57');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 15:18:22');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 19:30:17');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-23 22:09:45');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 08:27:09');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 13:31:17');
INSERT INTO `sys_logininfor` VALUES (118, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 13:43:11');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '', '未知', 'Edge 138', 'Windows 10', '1', '验证码错误', '2025-07-24 15:12:30');
INSERT INTO `sys_logininfor` VALUES (120, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 15:12:32');
INSERT INTO `sys_logininfor` VALUES (121, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 19:45:15');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 21:12:29');
INSERT INTO `sys_logininfor` VALUES (123, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 23:01:38');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-24 23:56:33');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-25 10:35:22');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-25 13:23:31');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '', '未知', 'Edge 138', 'Windows 10', '1', '验证码已失效', '2025-07-25 16:27:47');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-25 16:27:50');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '', '未知', 'Edge 138', 'Windows 10', '0', '登录成功', '2025-07-26 10:13:12');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2168 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-07-21 13:29:08', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-07-21 13:29:08', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2025-07-21 13:29:08', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (5, '智能监控', 0, 5, 'surveillance', NULL, '', '', 1, 0, 'M', '0', '0', '', 'redis-list', 'admin', '2025-07-21 13:29:08', 'admin', '2025-07-21 13:32:34', '智能监控系统目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2025-07-21 13:29:08', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2025-07-21 13:29:08', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2025-07-21 13:29:08', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2025-07-21 13:29:08', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2025-07-21 13:29:08', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2025-07-21 13:29:08', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2025-07-21 13:29:08', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2025-07-21 13:29:08', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2025-07-21 13:29:08', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2025-07-21 13:29:08', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2025-07-21 13:29:08', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2025-07-21 13:29:08', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2025-07-21 13:29:08', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2025-07-21 13:29:08', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2025-07-21 13:29:08', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2025-07-21 13:29:08', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2025-07-21 13:29:08', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2025-07-21 13:29:08', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2025-07-21 13:29:08', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2025-07-21 13:29:08', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2152, '视频流管理', 5, 1, 'stream', 'stream_manage/stream/index', NULL, '', 1, 0, 'C', '0', '0', 'stream_manage:stream:list', 'list', 'admin', '2025-07-21 15:36:20', 'admin', '2025-07-21 15:37:43', '视频流管理菜单');
INSERT INTO `sys_menu` VALUES (2153, '视频流管理查询', 2152, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'stream_manage:stream:query', '#', 'admin', '2025-07-21 15:36:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2154, '视频流管理新增', 2152, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'stream_manage:stream:add', '#', 'admin', '2025-07-21 15:36:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2155, '视频流管理修改', 2152, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'stream_manage:stream:edit', '#', 'admin', '2025-07-21 15:36:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2156, '视频流管理删除', 2152, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'stream_manage:stream:remove', '#', 'admin', '2025-07-21 15:36:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2157, '视频流管理导出', 2152, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'stream_manage:stream:export', '#', 'admin', '2025-07-21 15:36:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2164, '任务管理', 5, 2, 'task', 'stream_manage/task/index', NULL, '', 1, 1, 'C', '0', '0', '', 'cascader', 'admin', '2025-07-23 10:37:44', 'admin', '2025-07-23 22:18:10', '');
INSERT INTO `sys_menu` VALUES (2166, '告警记录', 5, 3, 'alert_manage', 'alert_manage/alert/index', '', '', 1, 1, 'C', '0', '0', '', 'build', 'admin', '2025-07-23 10:41:16', 'admin', '2025-07-23 12:03:19', '');
INSERT INTO `sys_menu` VALUES (2167, '实时监控', 5, 4, 'realtimeview', 'surveillance/monitor/index', '', '', 1, 0, 'C', '0', '0', NULL, 'eye-open', 'admin', '2025-07-23 10:45:39', 'admin', '2025-07-23 12:03:29', '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 vfadmin新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2025-07-21 13:29:09', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 vfadmin系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2025-07-21 13:29:09', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 284 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2001, \"menuName\": \"视频流管理\", \"parentId\": 5, \"orderNum\": 2, \"path\": \"stream\", \"component\": \"surveillance/stream/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"surveillance:stream:list\", \"icon\": \"example\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T13:29:08\", \"updateBy\": \"\", \"updateTime\": null, \"remark\": \"视频流管理菜单\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T13:31:22.928088\"}', 0, '', '2025-07-21 13:31:23', 15);
INSERT INTO `sys_oper_log` VALUES (101, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 5, \"menuName\": \"智能监控\", \"parentId\": 0, \"orderNum\": 5, \"path\": \"surveillance\", \"component\": null, \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"M\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"redis-list\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T13:29:08\", \"updateBy\": \"\", \"updateTime\": null, \"remark\": \"智能监控系统目录\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T13:32:33.553086\"}', 0, '', '2025-07-21 13:32:34', 1);
INSERT INTO `sys_oper_log` VALUES (102, '代码生成', 6, 'module_generator.controller.gen_controller.import_gen_table()', 'POST', 1, 'admin', '研发部门', '/dev-api/tool/gen/importTable', '', '未知', '{}', '{\"code\": 200, \"msg\": \"导入成功\", \"success\": true, \"time\": \"2025-07-21T14:30:13.100437\"}', 0, '', '2025-07-21 14:30:13', 22);
INSERT INTO `sys_oper_log` VALUES (103, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T14:43:34.125190\"}', 0, '', '2025-07-21 14:43:34', 33);
INSERT INTO `sys_oper_log` VALUES (104, '代码生成', 8, 'module_generator.controller.gen_controller.batch_gen_code()', 'GET', 1, 'admin', '研发部门', '/dev-api/tool/gen/batchGenCode', '', '未知', '{}', '{\"code\": 200, \"message\": \"获取成功\"}', 0, '', '2025-07-21 14:43:39', 18);
INSERT INTO `sys_oper_log` VALUES (105, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T15:32:38.343228\"}', 0, '', '2025-07-21 15:32:38', 22);
INSERT INTO `sys_oper_log` VALUES (106, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T15:33:28.363710\"}', 0, '', '2025-07-21 15:33:28', 5);
INSERT INTO `sys_oper_log` VALUES (107, '代码生成', 8, 'module_generator.controller.gen_controller.batch_gen_code()', 'GET', 1, 'admin', '研发部门', '/dev-api/tool/gen/batchGenCode', '', '未知', '{}', '{\"code\": 200, \"message\": \"获取成功\"}', 0, '', '2025-07-21 15:33:32', 27);
INSERT INTO `sys_oper_log` VALUES (108, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2001', '', '未知', '{\"menu_ids\": \"2001\"}', '{\"code\": 601, \"msg\": \"存在子菜单,不允许删除\", \"success\": false, \"time\": \"2025-07-21T15:37:08.226694\"}', 1, '存在子菜单,不允许删除', '2025-07-21 15:37:08', 14);
INSERT INTO `sys_oper_log` VALUES (109, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2110', '', '未知', '{\"menu_ids\": \"2110\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:37:19.276312\"}', 0, '', '2025-07-21 15:37:19', 1);
INSERT INTO `sys_oper_log` VALUES (110, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2111', '', '未知', '{\"menu_ids\": \"2111\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:37:22.043381\"}', 0, '', '2025-07-21 15:37:22', 1);
INSERT INTO `sys_oper_log` VALUES (111, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2112', '', '未知', '{\"menu_ids\": \"2112\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:37:24.363994\"}', 0, '', '2025-07-21 15:37:24', 1);
INSERT INTO `sys_oper_log` VALUES (112, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2113', '', '未知', '{\"menu_ids\": \"2113\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:37:26.471249\"}', 0, '', '2025-07-21 15:37:26', 1);
INSERT INTO `sys_oper_log` VALUES (113, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2114', '', '未知', '{\"menu_ids\": \"2114\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:37:28.830448\"}', 0, '', '2025-07-21 15:37:29', 1);
INSERT INTO `sys_oper_log` VALUES (114, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2001', '', '未知', '{\"menu_ids\": \"2001\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:37:31.598863\"}', 0, '', '2025-07-21 15:37:32', 6);
INSERT INTO `sys_oper_log` VALUES (115, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2152, \"menuName\": \"视频流管理\", \"parentId\": 5, \"orderNum\": 1, \"path\": \"stream\", \"component\": \"stream_manage/stream/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"stream_manage:stream:list\", \"icon\": \"list\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T15:36:20\", \"updateBy\": \"\", \"updateTime\": null, \"remark\": \"视频流管理菜单\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T15:37:43.104179\"}', 0, '', '2025-07-21 15:37:43', 2);
INSERT INTO `sys_oper_log` VALUES (116, '代码生成', 6, 'module_generator.controller.gen_controller.import_gen_table()', 'POST', 1, 'admin', '研发部门', '/dev-api/tool/gen/importTable', '', '未知', '{}', '{\"code\": 200, \"msg\": \"导入成功\", \"success\": true, \"time\": \"2025-07-21T15:40:47.297116\"}', 0, '', '2025-07-21 15:40:47', 17);
INSERT INTO `sys_oper_log` VALUES (117, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T15:41:53.023351\"}', 0, '', '2025-07-21 15:41:53', 5);
INSERT INTO `sys_oper_log` VALUES (118, '代码生成', 8, 'module_generator.controller.gen_controller.batch_gen_code()', 'GET', 1, 'admin', '研发部门', '/dev-api/tool/gen/batchGenCode', '', '未知', '{}', '{\"code\": 200, \"message\": \"获取成功\"}', 0, '', '2025-07-21 15:41:58', 21);
INSERT INTO `sys_oper_log` VALUES (119, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2100', '', '未知', '{\"menu_ids\": \"2100\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:14.700080\"}', 0, '', '2025-07-21 15:46:15', 17);
INSERT INTO `sys_oper_log` VALUES (120, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2101', '', '未知', '{\"menu_ids\": \"2101\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:16.623984\"}', 0, '', '2025-07-21 15:46:17', 1);
INSERT INTO `sys_oper_log` VALUES (121, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2102', '', '未知', '{\"menu_ids\": \"2102\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:18.124160\"}', 0, '', '2025-07-21 15:46:18', 1);
INSERT INTO `sys_oper_log` VALUES (122, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2103', '', '未知', '{\"menu_ids\": \"2103\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:20.109259\"}', 0, '', '2025-07-21 15:46:20', 2);
INSERT INTO `sys_oper_log` VALUES (123, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2104', '', '未知', '{\"menu_ids\": \"2104\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:22.218978\"}', 0, '', '2025-07-21 15:46:22', 1);
INSERT INTO `sys_oper_log` VALUES (124, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2105', '', '未知', '{\"menu_ids\": \"2105\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:24.107600\"}', 0, '', '2025-07-21 15:46:24', 1);
INSERT INTO `sys_oper_log` VALUES (125, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2000', '', '未知', '{\"menu_ids\": \"2000\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:46:26.142382\"}', 0, '', '2025-07-21 15:46:26', 1);
INSERT INTO `sys_oper_log` VALUES (126, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2158, \"menuName\": \"AI模型管理\", \"parentId\": 5, \"orderNum\": 1, \"path\": \"model\", \"component\": \"aimodels_manage/model/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"aimodels_manage:model:list\", \"icon\": \"github\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T15:45:48\", \"updateBy\": \"\", \"updateTime\": null, \"remark\": \"AI模型管理菜单\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T15:46:48.216199\"}', 0, '', '2025-07-21 15:46:48', 2);
INSERT INTO `sys_oper_log` VALUES (127, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2120', '', '未知', '{\"menu_ids\": \"2120\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:05.922018\"}', 0, '', '2025-07-21 15:47:06', 1);
INSERT INTO `sys_oper_log` VALUES (128, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2121', '', '未知', '{\"menu_ids\": \"2121\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:08.794388\"}', 0, '', '2025-07-21 15:47:09', 1);
INSERT INTO `sys_oper_log` VALUES (129, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2122', '', '未知', '{\"menu_ids\": \"2122\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:11.004654\"}', 0, '', '2025-07-21 15:47:11', 1);
INSERT INTO `sys_oper_log` VALUES (130, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2123', '', '未知', '{\"menu_ids\": \"2123\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:12.708463\"}', 0, '', '2025-07-21 15:47:13', 2);
INSERT INTO `sys_oper_log` VALUES (131, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2124', '', '未知', '{\"menu_ids\": \"2124\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:16.181301\"}', 0, '', '2025-07-21 15:47:16', 1);
INSERT INTO `sys_oper_log` VALUES (132, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2125', '', '未知', '{\"menu_ids\": \"2125\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:18.570278\"}', 0, '', '2025-07-21 15:47:19', 1);
INSERT INTO `sys_oper_log` VALUES (133, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2126', '', '未知', '{\"menu_ids\": \"2126\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:21.530485\"}', 0, '', '2025-07-21 15:47:22', 1);
INSERT INTO `sys_oper_log` VALUES (134, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2002', '', '未知', '{\"menu_ids\": \"2002\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:24.728500\"}', 0, '', '2025-07-21 15:47:25', 1);
INSERT INTO `sys_oper_log` VALUES (135, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2130', '', '未知', '{\"menu_ids\": \"2130\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:37.418449\"}', 0, '', '2025-07-21 15:47:37', 2);
INSERT INTO `sys_oper_log` VALUES (136, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2131', '', '未知', '{\"menu_ids\": \"2131\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:41.832522\"}', 0, '', '2025-07-21 15:47:42', 1);
INSERT INTO `sys_oper_log` VALUES (137, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2132', '', '未知', '{\"menu_ids\": \"2132\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:44.060870\"}', 0, '', '2025-07-21 15:47:44', 1);
INSERT INTO `sys_oper_log` VALUES (138, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2133', '', '未知', '{\"menu_ids\": \"2133\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:46.127772\"}', 0, '', '2025-07-21 15:47:46', 2);
INSERT INTO `sys_oper_log` VALUES (139, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2134', '', '未知', '{\"menu_ids\": \"2134\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:48.117234\"}', 0, '', '2025-07-21 15:47:48', 1);
INSERT INTO `sys_oper_log` VALUES (140, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2003', '', '未知', '{\"menu_ids\": \"2003\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:50.406651\"}', 0, '', '2025-07-21 15:47:50', 1);
INSERT INTO `sys_oper_log` VALUES (141, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2150', '', '未知', '{\"menu_ids\": \"2150\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:47:58.722646\"}', 0, '', '2025-07-21 15:47:59', 1);
INSERT INTO `sys_oper_log` VALUES (142, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2151', '', '未知', '{\"menu_ids\": \"2151\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:48:00.493825\"}', 0, '', '2025-07-21 15:48:00', 1);
INSERT INTO `sys_oper_log` VALUES (143, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2005', '', '未知', '{\"menu_ids\": \"2005\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:48:02.593910\"}', 0, '', '2025-07-21 15:48:03', 2);
INSERT INTO `sys_oper_log` VALUES (144, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2141', '', '未知', '{\"menu_ids\": \"2141\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:48:04.804941\"}', 0, '', '2025-07-21 15:48:05', 1);
INSERT INTO `sys_oper_log` VALUES (145, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2140', '', '未知', '{\"menu_ids\": \"2140\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:48:06.667110\"}', 0, '', '2025-07-21 15:48:07', 2);
INSERT INTO `sys_oper_log` VALUES (146, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2004', '', '未知', '{\"menu_ids\": \"2004\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T15:48:11.009438\"}', 0, '', '2025-07-21 15:48:11', 1);
INSERT INTO `sys_oper_log` VALUES (147, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"1\", \"rtspUrl\": \"1\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": null, \"createBy\": null, \"createTime\": null, \"updateBy\": null, \"updateTime\": null, \"remark\": null}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-21T16:08:19.683893\"}', 0, '', '2025-07-21 16:08:20', 17);
INSERT INTO `sys_oper_log` VALUES (148, '视频流管理', 3, 'module_stream.controller.stream_controller.delete_stream_manage_stream()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/1', '', '未知', '{\"stream_ids\": \"1\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T16:08:22.537106\"}', 0, '', '2025-07-21 16:08:23', 0);
INSERT INTO `sys_oper_log` VALUES (149, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 1, \"streamName\": \"1\", \"rtspUrl\": \"1\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": null, \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:08:20\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-21T16:08:20\", \"remark\": \"1\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T16:08:50.067086\"}', 0, '', '2025-07-21 16:08:50', 50);
INSERT INTO `sys_oper_log` VALUES (150, '视频流管理', 3, 'module_stream.controller.stream_controller.delete_stream_manage_stream()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/1', '', '未知', '{\"stream_ids\": \"1\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T16:08:52.200553\"}', 0, '', '2025-07-21 16:08:52', 0);
INSERT INTO `sys_oper_log` VALUES (151, '视频流管理', 3, 'module_stream.controller.stream_controller.delete_stream_manage_stream()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/1', '', '未知', '{\"stream_ids\": \"1\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-21T16:08:59.871223\"}', 0, '', '2025-07-21 16:09:00', 0);
INSERT INTO `sys_oper_log` VALUES (152, '视频流管理', 5, 'module_stream.controller.stream_controller.export_stream_manage_stream_list()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/export', '', '未知', 'pageNum: 1\npageSize: 10', '{\"code\": 200, \"message\": \"获取成功\"}', 0, '', '2025-07-21 16:09:07', 23);
INSERT INTO `sys_oper_log` VALUES (153, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"1\", \"rtspUrl\": \"1\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"1\", \"createBy\": null, \"createTime\": null, \"updateBy\": null, \"updateTime\": null, \"remark\": null}', '{\"code\": 500, \"msg\": \"(asyncmy.errors.IntegrityError) (1062, \\\"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_name\'\\\")\\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\\n[parameters: (\'1\', \'1\', \'1\', \'null\', \'1\', 0, \'1\', \'admin\', datetime.datetime(2025, 7, 21, 16, 9, 43, 300324), \'admin\', datetime.datetime(2025, 7, 21, 16, 9, 43, 300335), None)]\\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\", \"success\": false, \"time\": \"2025-07-21T16:09:43.556941\"}', 1, '(asyncmy.errors.IntegrityError) (1062, \"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_name\'\")\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\n[parameters: (\'1\', \'1\', \'1\', \'null\', \'1\', 0, \'1\', \'admin\', datetime.datetime(2025, 7, 21, 16, 9, 43, 300324), \'admin\', datetime.datetime(2025, 7, 21, 16, 9, 43, 300335), None)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)', '2025-07-21 16:09:43', 38);
INSERT INTO `sys_oper_log` VALUES (154, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"1\", \"rtspUrl\": \"1\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"0\", \"isRecording\": 0, \"delFlag\": \"\", \"createBy\": null, \"createTime\": null, \"updateBy\": null, \"updateTime\": null, \"remark\": \"1\"}', '{\"code\": 500, \"msg\": \"(asyncmy.errors.IntegrityError) (1062, \\\"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_name\'\\\")\\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\\n[parameters: (\'1\', \'1\', \'1\', \'null\', \'0\', 0, \'\', \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 21, 801532), \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 21, 801566), \'1\')]\\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\", \"success\": false, \"time\": \"2025-07-21T16:16:21.843489\"}', 1, '(asyncmy.errors.IntegrityError) (1062, \"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_name\'\")\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\n[parameters: (\'1\', \'1\', \'1\', \'null\', \'0\', 0, \'\', \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 21, 801532), \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 21, 801566), \'1\')]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)', '2025-07-21 16:16:22', 18);
INSERT INTO `sys_oper_log` VALUES (155, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"1\", \"rtspUrl\": \"1\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"0\", \"isRecording\": 0, \"delFlag\": \"\", \"createBy\": null, \"createTime\": null, \"updateBy\": null, \"updateTime\": null, \"remark\": \"\"}', '{\"code\": 500, \"msg\": \"(asyncmy.errors.IntegrityError) (1062, \\\"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_name\'\\\")\\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\\n[parameters: (\'1\', \'1\', \'1\', \'null\', \'0\', 0, \'\', \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 29, 370628), \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 29, 370630), \'\')]\\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\", \"success\": false, \"time\": \"2025-07-21T16:16:29.395480\"}', 1, '(asyncmy.errors.IntegrityError) (1062, \"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_name\'\")\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\n[parameters: (\'1\', \'1\', \'1\', \'null\', \'0\', 0, \'\', \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 29, 370628), \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 29, 370630), \'\')]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)', '2025-07-21 16:16:29', 2);
INSERT INTO `sys_oper_log` VALUES (156, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"2\", \"rtspUrl\": \"1\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"0\", \"isRecording\": 0, \"delFlag\": \"\", \"createBy\": null, \"createTime\": null, \"updateBy\": null, \"updateTime\": null, \"remark\": \"\"}', '{\"code\": 500, \"msg\": \"(asyncmy.errors.IntegrityError) (1062, \\\"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_url\'\\\")\\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\\n[parameters: (\'2\', \'1\', \'1\', \'null\', \'0\', 0, \'\', \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 39, 164647), \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 39, 164648), \'\')]\\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\", \"success\": false, \"time\": \"2025-07-21T16:16:39.191510\"}', 1, '(asyncmy.errors.IntegrityError) (1062, \"Duplicate entry \'1\' for key \'surveillance_stream.uk_surveillance_stream_url\'\")\n[SQL: INSERT INTO surveillance_stream (stream_name, rtsp_url, location, stream_config, status, is_recording, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\n[parameters: (\'2\', \'1\', \'1\', \'null\', \'0\', 0, \'\', \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 39, 164647), \'admin\', datetime.datetime(2025, 7, 21, 16, 16, 39, 164648), \'\')]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)', '2025-07-21 16:16:39', 2);
INSERT INTO `sys_oper_log` VALUES (157, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"2\", \"rtspUrl\": \"3\", \"location\": \"1\", \"streamConfig\": null, \"status\": \"0\", \"isRecording\": 0, \"delFlag\": \"\", \"createBy\": null, \"createTime\": null, \"updateBy\": null, \"updateTime\": null, \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-21T16:16:46.261197\"}', 0, '', '2025-07-21 16:16:46', 23);
INSERT INTO `sys_oper_log` VALUES (158, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T16:37:09.269731\"}', 0, '', '2025-07-21 16:37:09', 42);
INSERT INTO `sys_oper_log` VALUES (159, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T16:37:26.155621\"}', 0, '', '2025-07-21 16:37:26', 5);
INSERT INTO `sys_oper_log` VALUES (160, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T16:38:23.159089\"}', 0, '', '2025-07-21 16:38:23', 6);
INSERT INTO `sys_oper_log` VALUES (161, '代码生成', 2, 'module_generator.controller.gen_controller.edit_gen_table()', 'PUT', 1, 'admin', '研发部门', '/dev-api/tool/gen', '', '未知', '请求参数过长', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-21T16:38:32.273139\"}', 0, '', '2025-07-21 16:38:32', 8);
INSERT INTO `sys_oper_log` VALUES (162, '代码生成', 8, 'module_generator.controller.gen_controller.batch_gen_code()', 'GET', 1, 'admin', '研发部门', '/dev-api/tool/gen/batchGenCode', '', '未知', '{}', '{\"code\": 200, \"message\": \"获取成功\"}', 0, '', '2025-07-21 16:38:36', 19);
INSERT INTO `sys_oper_log` VALUES (163, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"本地测试\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"门口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"remark\": null}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-21T16:51:04.078189\"}', 0, '', '2025-07-21 16:51:04', 13);
INSERT INTO `sys_oper_log` VALUES (164, '视频流管理', 5, 'module_stream.controller.stream_controller.export_stream_manage_stream_list()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/export', '', '未知', 'pageNum: 1\npageSize: 10', '{\"code\": 200, \"message\": \"获取成功\"}', 0, '', '2025-07-21 16:51:09', 3);
INSERT INTO `sys_oper_log` VALUES (165, '视频流管理', 3, 'module_stream.controller.stream_controller.delete_stream_manage_stream()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/7', '', '未知', '{\"stream_ids\": \"7\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteStreamModel\\nstreamIds\\n  Field required [type=missing, input_value={\'stream_ids\': \'7\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\", \"success\": false, \"time\": \"2025-07-21T16:52:09.228949\"}', 1, '1 validation error for DeleteStreamModel\nstreamIds\n  Field required [type=missing, input_value={\'stream_ids\': \'7\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing', '2025-07-21 16:52:09', 5);
INSERT INTO `sys_oper_log` VALUES (166, '视频流管理', 3, 'module_stream.controller.stream_controller.delete_stream_manage_stream()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream/7', '', '未知', '{\"stream_ids\": \"7\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteStreamModel\\nstreamIds\\n  Field required [type=missing, input_value={\'stream_ids\': \'7\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\", \"success\": false, \"time\": \"2025-07-21T16:53:03.523650\"}', 1, '1 validation error for DeleteStreamModel\nstreamIds\n  Field required [type=missing, input_value={\'stream_ids\': \'7\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing', '2025-07-21 16:53:04', 1);
INSERT INTO `sys_oper_log` VALUES (167, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2159', '', '未知', '{\"menu_ids\": \"2159\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-22T16:13:39.082806\"}', 0, '', '2025-07-22 16:13:39', 17);
INSERT INTO `sys_oper_log` VALUES (168, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2158', '', '未知', '{\"menu_ids\": \"2158\"}', '{\"code\": 601, \"msg\": \"存在子菜单,不允许删除\", \"success\": false, \"time\": \"2025-07-22T16:13:41.827353\"}', 1, '存在子菜单,不允许删除', '2025-07-22 16:13:42', 0);
INSERT INTO `sys_oper_log` VALUES (169, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2160', '', '未知', '{\"menu_ids\": \"2160\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-22T16:13:46.057296\"}', 0, '', '2025-07-22 16:13:46', 1);
INSERT INTO `sys_oper_log` VALUES (170, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2161', '', '未知', '{\"menu_ids\": \"2161\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-22T16:13:47.900700\"}', 0, '', '2025-07-22 16:13:48', 0);
INSERT INTO `sys_oper_log` VALUES (171, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2162', '', '未知', '{\"menu_ids\": \"2162\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-22T16:13:49.725986\"}', 0, '', '2025-07-22 16:13:50', 1);
INSERT INTO `sys_oper_log` VALUES (172, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2163', '', '未知', '{\"menu_ids\": \"2163\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-22T16:13:51.303722\"}', 0, '', '2025-07-22 16:13:51', 1);
INSERT INTO `sys_oper_log` VALUES (173, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2158', '', '未知', '{\"menu_ids\": \"2158\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-22T16:13:53.126630\"}', 0, '', '2025-07-22 16:13:53', 1);
INSERT INTO `sys_oper_log` VALUES (174, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/4', '', '未知', '{\"menu_ids\": \"4\"}', '{\"code\": 601, \"msg\": \"菜单已分配,不允许删除\", \"success\": false, \"time\": \"2025-07-23T09:13:56.865683\"}', 1, '菜单已分配,不允许删除', '2025-07-23 09:13:57', 14);
INSERT INTO `sys_oper_log` VALUES (175, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 4, \"menuName\": \"若依官网\", \"parentId\": 0, \"orderNum\": 4, \"path\": \"http://ruoyi.vip\", \"component\": null, \"query\": \"\", \"routeName\": \"\", \"isFrame\": 0, \"isCache\": 0, \"menuType\": \"M\", \"visible\": \"0\", \"status\": \"1\", \"perms\": \"\", \"icon\": \"guide\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T13:29:08\", \"updateBy\": \"\", \"updateTime\": null, \"remark\": \"若依官网地址\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T09:14:06.560151\"}', 0, '', '2025-07-23 09:14:07', 1);
INSERT INTO `sys_oper_log` VALUES (176, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/4', '', '未知', '{\"menu_ids\": \"4\"}', '{\"code\": 601, \"msg\": \"菜单已分配,不允许删除\", \"success\": false, \"time\": \"2025-07-23T09:14:09.868750\"}', 1, '菜单已分配,不允许删除', '2025-07-23 09:14:10', 0);
INSERT INTO `sys_oper_log` VALUES (177, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 4, \"menuName\": \"若依官网\", \"parentId\": 0, \"orderNum\": 4, \"path\": \"/\", \"component\": null, \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"M\", \"visible\": \"0\", \"status\": \"1\", \"perms\": \"\", \"icon\": \"guide\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T13:29:08\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T09:14:07\", \"remark\": \"若依官网地址\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T09:14:25.099302\"}', 0, '', '2025-07-23 09:14:25', 11);
INSERT INTO `sys_oper_log` VALUES (178, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/4', '', '未知', '{\"menu_ids\": \"4\"}', '{\"code\": 601, \"msg\": \"菜单已分配,不允许删除\", \"success\": false, \"time\": \"2025-07-23T09:14:28.149984\"}', 1, '菜单已分配,不允许删除', '2025-07-23 09:14:28', 0);
INSERT INTO `sys_oper_log` VALUES (179, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 4, \"menuName\": \"若依官网\", \"parentId\": 0, \"orderNum\": 4, \"path\": \"/\", \"component\": null, \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"1\", \"perms\": \"\", \"icon\": \"guide\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T13:29:08\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T09:14:25\", \"remark\": \"若依官网地址\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T09:14:36.730325\"}', 0, '', '2025-07-23 09:14:37', 1);
INSERT INTO `sys_oper_log` VALUES (180, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/4', '', '未知', '{\"menu_ids\": \"4\"}', '{\"code\": 601, \"msg\": \"菜单已分配,不允许删除\", \"success\": false, \"time\": \"2025-07-23T09:14:39.584855\"}', 1, '菜单已分配,不允许删除', '2025-07-23 09:14:40', 1);
INSERT INTO `sys_oper_log` VALUES (181, '菜单管理', 1, 'module_admin.controller.menu_controller.add_system_menu()', 'POST', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"parentId\": 5, \"menuName\": \"任务管理\", \"icon\": \"cascader\", \"menuType\": \"C\", \"orderNum\": 2, \"isFrame\": 1, \"isCache\": 0, \"visible\": \"0\", \"status\": \"0\", \"path\": \"stream_manage:task\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-23T10:37:44.361425\"}', 0, '', '2025-07-23 10:37:44', 14);
INSERT INTO `sys_oper_log` VALUES (182, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2164, \"menuName\": \"任务管理\", \"parentId\": 5, \"orderNum\": 2, \"path\": \"task\", \"component\": \"stream_manage/task/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"cascader\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:37:44\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:37:44\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:38:15.347467\"}', 0, '', '2025-07-23 10:38:15', 1);
INSERT INTO `sys_oper_log` VALUES (183, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2164, \"menuName\": \"任务管理\", \"parentId\": 5, \"orderNum\": 2, \"path\": \"task\", \"component\": \"stream_manage/task/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"stream_manage:task:list\", \"icon\": \"cascader\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:37:44\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:38:15\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:38:35.639804\"}', 0, '', '2025-07-23 10:38:36', 1);
INSERT INTO `sys_oper_log` VALUES (184, '菜单管理', 1, 'module_admin.controller.menu_controller.add_system_menu()', 'POST', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"parentId\": 5, \"menuName\": \"告警记录\", \"icon\": \"build\", \"menuType\": \"C\", \"orderNum\": 3, \"isFrame\": 1, \"isCache\": 0, \"visible\": \"0\", \"status\": \"0\", \"path\": \"alert_manage\", \"component\": \"alert_manage/alert\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-23T10:41:16.251527\"}', 0, '', '2025-07-23 10:41:16', 18);
INSERT INTO `sys_oper_log` VALUES (185, '菜单管理', 1, 'module_admin.controller.menu_controller.add_system_menu()', 'POST', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"parentId\": 5, \"menuName\": \"告警记录\", \"icon\": \"build\", \"menuType\": \"C\", \"orderNum\": 3, \"isFrame\": 1, \"isCache\": 0, \"visible\": \"0\", \"status\": \"0\", \"path\": \"alert_manage\", \"component\": \"alert_manage/alert\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-23T10:41:16.256087\"}', 0, '', '2025-07-23 10:41:16', 1);
INSERT INTO `sys_oper_log` VALUES (186, '菜单管理', 3, 'module_admin.controller.menu_controller.delete_system_menu()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/system/menu/2165', '', '未知', '{\"menu_ids\": \"2165\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T10:41:25.240435\"}', 0, '', '2025-07-23 10:41:25', 1);
INSERT INTO `sys_oper_log` VALUES (187, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert\", \"query\": \"alert_manage/alert/index\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:41:16\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:41:49.963509\"}', 0, '', '2025-07-23 10:41:50', 1);
INSERT INTO `sys_oper_log` VALUES (188, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert\", \"query\": \"alert_manage/alert/index\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:41:50\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:42:12.619451\"}', 0, '', '2025-07-23 10:42:13', 0);
INSERT INTO `sys_oper_log` VALUES (189, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:42:13\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:42:37.420299\"}', 0, '', '2025-07-23 10:42:37', 11);
INSERT INTO `sys_oper_log` VALUES (190, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:42:13\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:42:37.429184\"}', 0, '', '2025-07-23 10:42:37', 1);
INSERT INTO `sys_oper_log` VALUES (191, '菜单管理', 1, 'module_admin.controller.menu_controller.add_system_menu()', 'POST', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"parentId\": 2152, \"menuName\": \"实时监控\", \"icon\": \"eye-open\", \"menuType\": \"C\", \"orderNum\": 4, \"isFrame\": 1, \"isCache\": 0, \"visible\": \"0\", \"status\": \"0\", \"path\": \"realtimeview\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-23T10:45:39.313086\"}', 0, '', '2025-07-23 10:45:39', 14);
INSERT INTO `sys_oper_log` VALUES (192, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": null, \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:45:39\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T10:46:09.075837\"}', 0, '', '2025-07-23 10:46:09', 1);
INSERT INTO `sys_oper_log` VALUES (193, '代码生成', 3, 'module_generator.controller.gen_controller.delete_gen_table()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/tool/gen/1,2,3,4,5', '', '未知', '{\"table_ids\": \"1,2,3,4,5\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T11:08:11.196392\"}', 0, '', '2025-07-23 11:08:11', 18);
INSERT INTO `sys_oper_log` VALUES (194, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2164, \"menuName\": \"任务管理\", \"parentId\": 5, \"orderNum\": 2, \"path\": \"task\", \"component\": \"stream_manage/task/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"cascader\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:37:44\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:38:36\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:11:50.120645\"}', 0, '', '2025-07-23 11:11:50', 14);
INSERT INTO `sys_oper_log` VALUES (195, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:42:37\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:12:16.919300\"}', 0, '', '2025-07-23 11:12:17', 1);
INSERT INTO `sys_oper_log` VALUES (196, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": null, \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T10:46:09\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:12:20.232681\"}', 0, '', '2025-07-23 11:12:20', 1);
INSERT INTO `sys_oper_log` VALUES (197, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:12:17\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:21:03.216827\"}', 0, '', '2025-07-23 11:21:03', 17);
INSERT INTO `sys_oper_log` VALUES (198, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A桥头\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-21T16:51:04\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:27:15.919942\"}', 0, '', '2025-07-23 11:27:16', 19);
INSERT INTO `sys_oper_log` VALUES (199, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A路B口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:27:16\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:27:47.157106\"}', 0, '', '2025-07-23 11:27:47', 1);
INSERT INTO `sys_oper_log` VALUES (200, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": null, \"query\": \"surveillance\\\\monitor\\\\index\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:12:20\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:32:29.570677\"}', 0, '', '2025-07-23 11:32:30', 1);
INSERT INTO `sys_oper_log` VALUES (201, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": \"surveillance\\\\monitor\\\\index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:32:30\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:32:49.169638\"}', 0, '', '2025-07-23 11:32:49', 13);
INSERT INTO `sys_oper_log` VALUES (202, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": \"surveillance\\\\monitor\\\\index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:32:49\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:37:45.519618\"}', 0, '', '2025-07-23 11:37:46', 15);
INSERT INTO `sys_oper_log` VALUES (203, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2164, \"menuName\": \"任务管理\", \"parentId\": 5, \"orderNum\": 2, \"path\": \"task\", \"component\": \"stream_manage/task/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"cascader\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:37:44\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:11:50\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:56:28.135671\"}', 0, '', '2025-07-23 11:56:28', 15);
INSERT INTO `sys_oper_log` VALUES (204, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:21:03\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:56:31.887646\"}', 0, '', '2025-07-23 11:56:32', 1);
INSERT INTO `sys_oper_log` VALUES (205, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": \"surveillance\\\\monitor\\\\index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:37:46\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:56:34.084612\"}', 0, '', '2025-07-23 11:56:34', 1);
INSERT INTO `sys_oper_log` VALUES (206, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:56:32\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:58:43.029393\"}', 0, '', '2025-07-23 11:58:43', 19);
INSERT INTO `sys_oper_log` VALUES (207, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"alert_manage:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:56:32\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T11:58:43.048147\"}', 0, '', '2025-07-23 11:58:43', 3);
INSERT INTO `sys_oper_log` VALUES (208, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"surveillance:alert:list\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:58:43\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T12:00:14.474377\"}', 0, '', '2025-07-23 12:00:14', 13);
INSERT INTO `sys_oper_log` VALUES (209, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T12:00:14\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T12:01:49.680968\"}', 0, '', '2025-07-23 12:01:50', 14);
INSERT INTO `sys_oper_log` VALUES (210, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage\\\\alert\\\\index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T12:01:50\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T12:03:07.561316\"}', 0, '', '2025-07-23 12:03:08', 1);
INSERT INTO `sys_oper_log` VALUES (211, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2166, \"menuName\": \"告警记录\", \"parentId\": 5, \"orderNum\": 3, \"path\": \"alert_manage\", \"component\": \"alert_manage/alert/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"build\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:41:16\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T12:03:08\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T12:03:19.451627\"}', 0, '', '2025-07-23 12:03:19', 1);
INSERT INTO `sys_oper_log` VALUES (212, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2167, \"menuName\": \"实时监控\", \"parentId\": 5, \"orderNum\": 4, \"path\": \"realtimeview\", \"component\": \"surveillance/monitor/index\", \"query\": \"\", \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 0, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": null, \"icon\": \"eye-open\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:45:39\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:56:34\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T12:03:28.833162\"}', 0, '', '2025-07-23 12:03:29', 1);
INSERT INTO `sys_oper_log` VALUES (213, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试2\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A路B口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:27:47\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T12:37:37.243251\"}', 0, '', '2025-07-23 12:37:37', 15);
INSERT INTO `sys_oper_log` VALUES (214, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/6', '', '未知', '{\"task_id\": \"6\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T14:47:16.133175\"}', 1, '', '2025-07-23 14:47:16', 0);
INSERT INTO `sys_oper_log` VALUES (215, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/5', '', '未知', '{\"task_ids\": \"5\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteTaskModel\\ntaskIds\\n  Field required [type=missing, input_value={\'task_ids\': \'5\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\", \"success\": false, \"time\": \"2025-07-23T15:52:15.124870\"}', 1, '1 validation error for DeleteTaskModel\ntaskIds\n  Field required [type=missing, input_value={\'task_ids\': \'5\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing', '2025-07-23 15:52:15', 11);
INSERT INTO `sys_oper_log` VALUES (216, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/7', '', '未知', '{\"task_id\": \"7\"}', '{\"code\": 200, \"msg\": \"任务启动成功\", \"success\": true, \"time\": \"2025-07-23T15:59:38.465014\"}', 0, '', '2025-07-23 15:59:38', 12);
INSERT INTO `sys_oper_log` VALUES (217, '检测任务', 0, 'module_stream.controller.task_controller.stop_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/stop/7', '', '未知', '{\"task_id\": \"7\"}', '{\"code\": 200, \"msg\": \"任务停止成功\", \"success\": true, \"time\": \"2025-07-23T15:59:44.328021\"}', 0, '', '2025-07-23 15:59:44', 1);
INSERT INTO `sys_oper_log` VALUES (218, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/5', '', '未知', '{\"task_ids\": \"5\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteTaskModel\\ntaskIds\\n  Field required [type=missing, input_value={\'task_ids\': \'5\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\", \"success\": false, \"time\": \"2025-07-23T16:05:03.402159\"}', 1, '1 validation error for DeleteTaskModel\ntaskIds\n  Field required [type=missing, input_value={\'task_ids\': \'5\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing', '2025-07-23 16:05:03', 17);
INSERT INTO `sys_oper_log` VALUES (219, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/7', '', '未知', '{\"task_ids\": \"7\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteTaskModel\\ntaskIds\\n  Field required [type=missing, input_value={\'task_ids\': \'7\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\", \"success\": false, \"time\": \"2025-07-23T19:31:24.932294\"}', 1, '1 validation error for DeleteTaskModel\ntaskIds\n  Field required [type=missing, input_value={\'task_ids\': \'7\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing', '2025-07-23 19:31:25', 0);
INSERT INTO `sys_oper_log` VALUES (220, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/7', '', '未知', '{\"task_ids\": \"7\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T19:38:33.474577\"}', 0, '', '2025-07-23 19:38:33', 13);
INSERT INTO `sys_oper_log` VALUES (221, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/5', '', '未知', '{\"task_ids\": \"5\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T22:09:53.273988\"}', 0, '', '2025-07-23 22:09:53', 1);
INSERT INTO `sys_oper_log` VALUES (222, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/6', '', '未知', '{\"task_ids\": \"6\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T22:09:56.912803\"}', 0, '', '2025-07-23 22:09:57', 2);
INSERT INTO `sys_oper_log` VALUES (223, '菜单管理', 2, 'module_admin.controller.menu_controller.edit_system_menu()', 'PUT', 1, 'admin', '研发部门', '/dev-api/system/menu', '', '未知', '{\"menuId\": 2164, \"menuName\": \"任务管理\", \"parentId\": 5, \"orderNum\": 2, \"path\": \"task\", \"component\": \"stream_manage/task/index\", \"query\": null, \"routeName\": \"\", \"isFrame\": 1, \"isCache\": 1, \"menuType\": \"C\", \"visible\": \"0\", \"status\": \"0\", \"perms\": \"\", \"icon\": \"cascader\", \"createBy\": \"admin\", \"createTime\": \"2025-07-23T10:37:44\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T11:56:28\", \"remark\": \"\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-23T22:18:10.450412\"}', 0, '', '2025-07-23 22:18:10', 14);
INSERT INTO `sys_oper_log` VALUES (224, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/6', '', '未知', '{\"task_ids\": \"6\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T22:36:37.803586\"}', 0, '', '2025-07-23 22:36:38', 13);
INSERT INTO `sys_oper_log` VALUES (225, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/7', '', '未知', '{\"task_ids\": \"7\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-23T22:36:39.781844\"}', 0, '', '2025-07-23 22:36:40', 1);
INSERT INTO `sys_oper_log` VALUES (226, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:37:06.071957\"}', 1, '', '2025-07-23 22:37:06', 14);
INSERT INTO `sys_oper_log` VALUES (227, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:37:08.651702\"}', 1, '', '2025-07-23 22:37:09', 0);
INSERT INTO `sys_oper_log` VALUES (228, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:37:51.379814\"}', 1, '', '2025-07-23 22:37:51', 11);
INSERT INTO `sys_oper_log` VALUES (229, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:41:31.610512\"}', 1, '', '2025-07-23 22:41:32', 11);
INSERT INTO `sys_oper_log` VALUES (230, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:42:01.401543\"}', 1, '', '2025-07-23 22:42:01', 0);
INSERT INTO `sys_oper_log` VALUES (231, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:49:37.078655\"}', 1, '', '2025-07-23 22:49:37', 0);
INSERT INTO `sys_oper_log` VALUES (232, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T22:49:44.176052\"}', 1, '', '2025-07-23 22:49:44', 0);
INSERT INTO `sys_oper_log` VALUES (233, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T23:03:11.526802\"}', 1, '', '2025-07-23 23:03:12', 14);
INSERT INTO `sys_oper_log` VALUES (234, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T23:12:37.993269\"}', 1, '', '2025-07-23 23:12:38', 13);
INSERT INTO `sys_oper_log` VALUES (235, '检测任务', 0, 'module_stream.controller.task_controller.start_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/start/5', '', '未知', '{\"task_id\": \"5\"}', '{\"code\": 500, \"msg\": \"\", \"success\": false, \"time\": \"2025-07-23T23:12:41.969099\"}', 1, '', '2025-07-23 23:12:42', 0);
INSERT INTO `sys_oper_log` VALUES (236, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/8', '', '未知', '{\"task_ids\": \"8\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-24T09:47:44.369962\"}', 0, '', '2025-07-24 09:47:44', 19);
INSERT INTO `sys_oper_log` VALUES (237, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试2\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A路B口\", \"streamConfig\": null, \"status\": \"0\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-23T12:37:37\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-24T13:32:04.088037\"}', 0, '', '2025-07-24 13:32:04', 1);
INSERT INTO `sys_oper_log` VALUES (238, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试2\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A路B口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-24T13:32:04\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-24T13:32:21.215180\"}', 0, '', '2025-07-24 13:32:21', 1);
INSERT INTO `sys_oper_log` VALUES (239, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/9', '', '未知', '{\"task_ids\": \"9\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-24T13:38:36.337336\"}', 0, '', '2025-07-24 13:38:36', 3);
INSERT INTO `sys_oper_log` VALUES (240, '检测任务', 0, 'module_stream.controller.task_controller.stop_task()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/stop/10', '', '未知', '{\"task_id\": \"10\"}', '{\"code\": 200, \"msg\": \"任务停止成功\", \"success\": true, \"time\": \"2025-07-24T13:42:03.676040\"}', 0, '', '2025-07-24 13:42:04', 19);
INSERT INTO `sys_oper_log` VALUES (241, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试2\", \"rtspUrl\": \"rtsp://ai:ai123098@192.168.25.253:554/Streaming/Channels/101\", \"location\": \"A路B口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-24T13:32:21\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-24T19:45:50.453569\"}', 0, '', '2025-07-24 19:45:50', 0);
INSERT INTO `sys_oper_log` VALUES (242, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"本地测试2\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A路B口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-24T19:45:50\", \"remark\": \"本地使用的视频流测试\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-24T19:48:00.629887\"}', 0, '', '2025-07-24 19:48:01', 0);
INSERT INTO `sys_oper_log` VALUES (243, '检测任务', 3, 'module_stream.controller.task_controller.delete_task()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/stream_manage/task/10', '', '未知', '{\"task_ids\": \"10\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-25T08:34:43.895679\"}', 0, '', '2025-07-25 08:34:44', 1);
INSERT INTO `sys_oper_log` VALUES (244, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 7, \"userId\": 1, \"streamName\": \"监控1\", \"rtspUrl\": \"rtsp://ai:ai123098@192.168.25.253:554/Streaming/Channels/101\", \"location\": \"门口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-21T16:51:04\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-24T19:48:01\", \"remark\": \"门口的监控\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-25T08:35:19.886250\"}', 0, '', '2025-07-25 08:35:20', 1);
INSERT INTO `sys_oper_log` VALUES (245, '代码生成', 6, 'module_generator.controller.gen_controller.import_gen_table()', 'POST', 1, 'admin', '研发部门', '/dev-api/tool/gen/importTable', '', '未知', '{}', '{\"code\": 200, \"msg\": \"导入成功\", \"success\": true, \"time\": \"2025-07-25T09:55:19.554418\"}', 0, '', '2025-07-25 09:55:19', 22);
INSERT INTO `sys_oper_log` VALUES (246, '视频流管理', 1, 'module_stream.controller.stream_controller.add_stream_manage_stream()', 'POST', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": null, \"streamName\": \"本地车流测试\", \"rtspUrl\": \"rtsp://:8554/test1\", \"location\": \"A路口\", \"status\": \"1\", \"isRecording\": 0, \"remark\": null}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-07-25T13:49:47.469235\"}', 0, '', '2025-07-25 13:49:47', 16);
INSERT INTO `sys_oper_log` VALUES (247, '视频流管理', 2, 'module_stream.controller.stream_controller.edit_stream_manage_stream()', 'PUT', 1, 'admin', '研发部门', '/dev-api/stream_manage/stream', '', '未知', '{\"streamId\": 8, \"userId\": 1, \"streamName\": \"本地车流测试\", \"rtspUrl\": \"rtsp://127.0.0.1:8554/test1\", \"location\": \"A路口\", \"streamConfig\": null, \"status\": \"1\", \"isRecording\": 0, \"delFlag\": \"0\", \"createBy\": \"admin\", \"createTime\": \"2025-07-25T13:49:47\", \"updateBy\": \"admin\", \"updateTime\": \"2025-07-25T13:49:47\", \"remark\": null}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-07-25T13:58:29.183504\"}', 0, '', '2025-07-25 13:58:29', 18);
INSERT INTO `sys_oper_log` VALUES (248, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/22', '', '未知', '{\"alert_ids\": \"22\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteAlertModel\\nalertIds\\n  Field required [type=missing, input_value={\'alert_ids\': \'22\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\", \"success\": false, \"time\": \"2025-07-25T15:09:42.659944\"}', 1, '1 validation error for DeleteAlertModel\nalertIds\n  Field required [type=missing, input_value={\'alert_ids\': \'22\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing', '2025-07-25 15:09:43', 16);
INSERT INTO `sys_oper_log` VALUES (249, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/46', '', '未知', '{\"alert_ids\": \"46\"}', '{\"code\": 500, \"msg\": \"1 validation error for DeleteAlertModel\\nalertIds\\n  Field required [type=missing, input_value={\'alert_ids\': \'46\'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\", \"success\": false, \"time\": \"2025-07-25T15:11:49.209280\"}', 1, '1 validation error for DeleteAlertModel\nalertIds\n  Field required [type=missing, input_value={\'alert_ids\': \'46\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing', '2025-07-25 15:11:49', 20);
INSERT INTO `sys_oper_log` VALUES (250, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/8,7,6,5,4,3,2,1', '', '未知', '{\"alert_ids\": \"8,7,6,5,4,3,2,1\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-25T15:20:01.934993\"}', 0, '', '2025-07-25 15:20:02', 13);
INSERT INTO `sys_oper_log` VALUES (251, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/8,7,6,5,4,3,2,1', '', '未知', '{\"alert_ids\": \"8,7,6,5,4,3,2,1\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-25T15:20:07.399706\"}', 0, '', '2025-07-25 15:20:07', 1);
INSERT INTO `sys_oper_log` VALUES (252, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/8,7,6,5,4,3,2,1', '', '未知', '{\"alert_ids\": \"8,7,6,5,4,3,2,1\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-25T15:20:11.521188\"}', 0, '', '2025-07-25 15:20:12', 1);
INSERT INTO `sys_oper_log` VALUES (253, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/8', '', '未知', '{\"alert_ids\": \"8\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-25T15:20:18.736831\"}', 0, '', '2025-07-25 15:20:19', 0);
INSERT INTO `sys_oper_log` VALUES (254, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/7', '', '未知', '{\"alert_ids\": \"7\"}', '{\"code\": 200, \"msg\": \"删除成功\", \"success\": true, \"time\": \"2025-07-25T15:20:20.907264\"}', 0, '', '2025-07-25 15:20:21', 0);
INSERT INTO `sys_oper_log` VALUES (255, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 48, \"status\": \"1\", \"handleRemark\": \"状态变更为已处理\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:28:56.131630\"}', 0, '', '2025-07-25 15:28:56', 17);
INSERT INTO `sys_oper_log` VALUES (256, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 47, \"status\": \"2\", \"handleRemark\": \"状态变更为已忽略\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:29:04.943116\"}', 0, '', '2025-07-25 15:29:05', 1);
INSERT INTO `sys_oper_log` VALUES (257, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 47, \"status\": \"1\", \"handleRemark\": \"状态变更为已处理\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:29:10.855429\"}', 0, '', '2025-07-25 15:29:11', 1);
INSERT INTO `sys_oper_log` VALUES (258, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/47', '', '未知', '{\"alert_ids\": \"47\"}', '{\"code\": 200, \"msg\": \"没有找到要删除的记录\", \"success\": true, \"time\": \"2025-07-25T15:29:16.361245\"}', 0, '', '2025-07-25 15:29:16', 0);
INSERT INTO `sys_oper_log` VALUES (259, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/48', '', '未知', '{\"alert_ids\": \"48\"}', '{\"code\": 200, \"msg\": \"没有找到要删除的记录\", \"success\": true, \"time\": \"2025-07-25T15:29:19.919828\"}', 0, '', '2025-07-25 15:29:20', 0);
INSERT INTO `sys_oper_log` VALUES (260, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/48', '', '未知', '{\"alert_ids\": \"48\"}', '{\"code\": 500, \"msg\": \"没有找到要删除的记录\", \"success\": false, \"time\": \"2025-07-25T15:34:53.646696\"}', 1, '没有找到要删除的记录', '2025-07-25 15:34:54', 18);
INSERT INTO `sys_oper_log` VALUES (261, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 48, \"status\": \"0\", \"handleRemark\": \"状态变更为未处理\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:36:04.641244\"}', 0, '', '2025-07-25 15:36:05', 15);
INSERT INTO `sys_oper_log` VALUES (262, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 47, \"status\": \"0\", \"handleRemark\": \"状态变更为未处理\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:36:07.231018\"}', 0, '', '2025-07-25 15:36:07', 1);
INSERT INTO `sys_oper_log` VALUES (263, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/43', '', '未知', '{\"alert_ids\": \"43\"}', '{\"code\": 500, \"msg\": \"没有找到要删除的记录\", \"success\": false, \"time\": \"2025-07-25T15:36:10.163504\"}', 1, '没有找到要删除的记录', '2025-07-25 15:36:10', 1);
INSERT INTO `sys_oper_log` VALUES (264, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/43', '', '未知', '{\"alert_ids\": \"43\"}', '{\"code\": 500, \"msg\": \"没有找到要删除的记录\", \"success\": false, \"time\": \"2025-07-25T15:36:13.404586\"}', 1, '没有找到要删除的记录', '2025-07-25 15:36:13', 0);
INSERT INTO `sys_oper_log` VALUES (265, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/48,47', '', '未知', '{\"alert_ids\": \"48,47\"}', '{\"code\": 500, \"msg\": \"没有找到要删除的记录\", \"success\": false, \"time\": \"2025-07-25T15:36:40.760936\"}', 1, '没有找到要删除的记录', '2025-07-25 15:36:41', 0);
INSERT INTO `sys_oper_log` VALUES (266, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/48', '', '未知', '{\"alert_ids\": \"48\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:40:12.358497\"}', 0, '', '2025-07-25 15:40:12', 14);
INSERT INTO `sys_oper_log` VALUES (267, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/43', '', '未知', '{\"alert_ids\": \"43\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:40:14.536210\"}', 0, '', '2025-07-25 15:40:15', 1);
INSERT INTO `sys_oper_log` VALUES (268, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 44, \"status\": \"1\", \"handleRemark\": \"状态变更为已处理\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:40:16.696128\"}', 0, '', '2025-07-25 15:40:17', 1);
INSERT INTO `sys_oper_log` VALUES (269, '警告记录状态', 2, 'module_alert.controller.alert_controller.update_alert_status()', 'PUT', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/status', '', '未知', '{\"alertId\": 45, \"status\": \"1\", \"handleRemark\": \"状态变更为已处理\"}', '{\"code\": 200, \"msg\": \"状态更新成功\", \"success\": true, \"time\": \"2025-07-25T15:40:20.166733\"}', 0, '', '2025-07-25 15:40:20', 1);
INSERT INTO `sys_oper_log` VALUES (270, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/45', '', '未知', '{\"alert_ids\": \"45\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:40:23.737776\"}', 0, '', '2025-07-25 15:40:24', 1);
INSERT INTO `sys_oper_log` VALUES (271, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/46', '', '未知', '{\"alert_ids\": \"46\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:40:28.793537\"}', 0, '', '2025-07-25 15:40:29', 1);
INSERT INTO `sys_oper_log` VALUES (272, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/44', '', '未知', '{\"alert_ids\": \"44\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:40:34.381362\"}', 0, '', '2025-07-25 15:40:34', 1);
INSERT INTO `sys_oper_log` VALUES (273, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/47', '', '未知', '{\"alert_ids\": \"47\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:40:37.222401\"}', 0, '', '2025-07-25 15:40:37', 1);
INSERT INTO `sys_oper_log` VALUES (274, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/52', '', '未知', '{\"alert_ids\": \"52\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:56:52.831186\"}', 0, '', '2025-07-25 15:56:53', 28);
INSERT INTO `sys_oper_log` VALUES (275, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/53', '', '未知', '{\"alert_ids\": \"53\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:56:54.008483\"}', 0, '', '2025-07-25 15:56:54', 2);
INSERT INTO `sys_oper_log` VALUES (276, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/51', '', '未知', '{\"alert_ids\": \"51\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:56:56.351299\"}', 0, '', '2025-07-25 15:56:56', 1);
INSERT INTO `sys_oper_log` VALUES (277, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/49', '', '未知', '{\"alert_ids\": \"49\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:56:58.873201\"}', 0, '', '2025-07-25 15:56:59', 1);
INSERT INTO `sys_oper_log` VALUES (278, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/50', '', '未知', '{\"alert_ids\": \"50\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:57:02.089504\"}', 0, '', '2025-07-25 15:57:02', 1);
INSERT INTO `sys_oper_log` VALUES (279, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/54', '', '未知', '{\"alert_ids\": \"54\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:57:05.267515\"}', 0, '', '2025-07-25 15:57:05', 1);
INSERT INTO `sys_oper_log` VALUES (280, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/55', '', '未知', '{\"alert_ids\": \"55\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:57:09.204203\"}', 0, '', '2025-07-25 15:57:09', 1);
INSERT INTO `sys_oper_log` VALUES (281, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/56', '', '未知', '{\"alert_ids\": \"56\"}', '{\"code\": 200, \"msg\": \"成功删除1条记录\", \"success\": true, \"time\": \"2025-07-25T15:57:12.165978\"}', 0, '', '2025-07-25 15:57:12', 1);
INSERT INTO `sys_oper_log` VALUES (282, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/66,65,64,63,62,61,60,59,58,57', '', '未知', '{\"alert_ids\": \"66,65,64,63,62,61,60,59,58,57\"}', '{\"code\": 200, \"msg\": \"成功删除10条记录\", \"success\": true, \"time\": \"2025-07-25T15:57:17.555589\"}', 0, '', '2025-07-25 15:57:17', 7);
INSERT INTO `sys_oper_log` VALUES (283, '警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/68,67', '', '未知', '{\"alert_ids\": \"68,67\"}', '{\"code\": 200, \"msg\": \"成功删除2条记录\", \"success\": true, \"time\": \"2025-07-25T15:57:34.836564\"}', 0, '', '2025-07-25 15:57:35', 2);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2025-07-21 13:29:08', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2025-07-21 13:29:08', '', NULL, '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2025-07-21 13:29:08', '', NULL, '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);
INSERT INTO `sys_role_dept` VALUES (2, 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 2);
INSERT INTO `sys_role_menu` VALUES (2, 3);
INSERT INTO `sys_role_menu` VALUES (2, 4);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 101);
INSERT INTO `sys_role_menu` VALUES (2, 102);
INSERT INTO `sys_role_menu` VALUES (2, 103);
INSERT INTO `sys_role_menu` VALUES (2, 104);
INSERT INTO `sys_role_menu` VALUES (2, 105);
INSERT INTO `sys_role_menu` VALUES (2, 106);
INSERT INTO `sys_role_menu` VALUES (2, 107);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 109);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 111);
INSERT INTO `sys_role_menu` VALUES (2, 112);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 114);
INSERT INTO `sys_role_menu` VALUES (2, 115);
INSERT INTO `sys_role_menu` VALUES (2, 116);
INSERT INTO `sys_role_menu` VALUES (2, 117);
INSERT INTO `sys_role_menu` VALUES (2, 500);
INSERT INTO `sys_role_menu` VALUES (2, 501);
INSERT INTO `sys_role_menu` VALUES (2, 1000);
INSERT INTO `sys_role_menu` VALUES (2, 1001);
INSERT INTO `sys_role_menu` VALUES (2, 1002);
INSERT INTO `sys_role_menu` VALUES (2, 1003);
INSERT INTO `sys_role_menu` VALUES (2, 1004);
INSERT INTO `sys_role_menu` VALUES (2, 1005);
INSERT INTO `sys_role_menu` VALUES (2, 1006);
INSERT INTO `sys_role_menu` VALUES (2, 1007);
INSERT INTO `sys_role_menu` VALUES (2, 1008);
INSERT INTO `sys_role_menu` VALUES (2, 1009);
INSERT INTO `sys_role_menu` VALUES (2, 1010);
INSERT INTO `sys_role_menu` VALUES (2, 1011);
INSERT INTO `sys_role_menu` VALUES (2, 1012);
INSERT INTO `sys_role_menu` VALUES (2, 1013);
INSERT INTO `sys_role_menu` VALUES (2, 1014);
INSERT INTO `sys_role_menu` VALUES (2, 1015);
INSERT INTO `sys_role_menu` VALUES (2, 1016);
INSERT INTO `sys_role_menu` VALUES (2, 1017);
INSERT INTO `sys_role_menu` VALUES (2, 1018);
INSERT INTO `sys_role_menu` VALUES (2, 1019);
INSERT INTO `sys_role_menu` VALUES (2, 1020);
INSERT INTO `sys_role_menu` VALUES (2, 1021);
INSERT INTO `sys_role_menu` VALUES (2, 1022);
INSERT INTO `sys_role_menu` VALUES (2, 1023);
INSERT INTO `sys_role_menu` VALUES (2, 1024);
INSERT INTO `sys_role_menu` VALUES (2, 1025);
INSERT INTO `sys_role_menu` VALUES (2, 1026);
INSERT INTO `sys_role_menu` VALUES (2, 1027);
INSERT INTO `sys_role_menu` VALUES (2, 1028);
INSERT INTO `sys_role_menu` VALUES (2, 1029);
INSERT INTO `sys_role_menu` VALUES (2, 1030);
INSERT INTO `sys_role_menu` VALUES (2, 1031);
INSERT INTO `sys_role_menu` VALUES (2, 1032);
INSERT INTO `sys_role_menu` VALUES (2, 1033);
INSERT INTO `sys_role_menu` VALUES (2, 1034);
INSERT INTO `sys_role_menu` VALUES (2, 1035);
INSERT INTO `sys_role_menu` VALUES (2, 1036);
INSERT INTO `sys_role_menu` VALUES (2, 1037);
INSERT INTO `sys_role_menu` VALUES (2, 1038);
INSERT INTO `sys_role_menu` VALUES (2, 1039);
INSERT INTO `sys_role_menu` VALUES (2, 1040);
INSERT INTO `sys_role_menu` VALUES (2, 1041);
INSERT INTO `sys_role_menu` VALUES (2, 1042);
INSERT INTO `sys_role_menu` VALUES (2, 1043);
INSERT INTO `sys_role_menu` VALUES (2, 1044);
INSERT INTO `sys_role_menu` VALUES (2, 1045);
INSERT INTO `sys_role_menu` VALUES (2, 1046);
INSERT INTO `sys_role_menu` VALUES (2, 1047);
INSERT INTO `sys_role_menu` VALUES (2, 1048);
INSERT INTO `sys_role_menu` VALUES (2, 1049);
INSERT INTO `sys_role_menu` VALUES (2, 1050);
INSERT INTO `sys_role_menu` VALUES (2, 1051);
INSERT INTO `sys_role_menu` VALUES (2, 1052);
INSERT INTO `sys_role_menu` VALUES (2, 1053);
INSERT INTO `sys_role_menu` VALUES (2, 1054);
INSERT INTO `sys_role_menu` VALUES (2, 1055);
INSERT INTO `sys_role_menu` VALUES (2, 1056);
INSERT INTO `sys_role_menu` VALUES (2, 1057);
INSERT INTO `sys_role_menu` VALUES (2, 1058);
INSERT INTO `sys_role_menu` VALUES (2, 1059);
INSERT INTO `sys_role_menu` VALUES (2, 1060);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '超级管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-07-26 10:13:13', 'admin', '2025-07-21 13:29:08', '', NULL, '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'niangao', '年糕', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-07-21 13:29:08', 'admin', '2025-07-21 13:29:08', '', NULL, '测试员');

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 2);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);

SET FOREIGN_KEY_CHECKS = 1;
