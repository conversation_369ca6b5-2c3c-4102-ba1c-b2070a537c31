2025-07-22 21:31:57.827 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:63 - 从目录加载算法: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo
2025-07-22 21:31:57.828 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:73 - 扫描平台: KS988
2025-07-22 21:31:57.828 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\black_screen\model\model.yaml (算法: black_screen)
2025-07-22 21:31:57.829 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: black_screen (平台: KS988)
2025-07-22 21:31:57.829 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\camera_blur\model\model.yaml (算法: camera_blur)
2025-07-22 21:31:57.830 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: camera_blur (平台: KS988)
2025-07-22 21:31:57.830 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\camera_occlusion\model\model.yaml (算法: camera_occlusion)
2025-07-22 21:31:57.830 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: camera_occlusion (平台: KS988)
2025-07-22 21:31:57.831 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\camera_shift\model\model.yaml (算法: camera_shift)
2025-07-22 21:31:57.831 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: camera_shift (平台: KS988)
2025-07-22 21:31:57.832 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\car_counting\model\model.yaml (算法: car_counting)
2025-07-22 21:31:57.834 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: car_counting (平台: KS988)
2025-07-22 21:31:57.834 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\car_misplaced\model\model.yaml (算法: car_misplaced)
2025-07-22 21:31:57.835 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: car_misplaced (平台: KS988)
2025-07-22 21:31:57.835 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\car_type\model\model.yaml (算法: car_type)
2025-07-22 21:31:57.836 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: car_type (平台: KS988)
2025-07-22 21:31:57.836 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\channel_obstruction\model\model.yaml (算法: channel_obstruction)
2025-07-22 21:31:57.837 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: channel_obstruction (平台: KS988)
2025-07-22 21:31:57.837 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\electric_bike_misplaced\model\model.yaml (算法: electric_bike_misplaced)
2025-07-22 21:31:57.838 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: electric_bike_misplaced (平台: KS988)
2025-07-22 21:31:57.838 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\electric_vehicle_elevator\model\model.yaml (算法: electric_vehicle_elevator)
2025-07-22 21:31:57.839 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: electric_vehicle_elevator (平台: KS988)
2025-07-22 21:31:57.839 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\extinguisher_misplaced\model\model.yaml (算法: extinguisher_misplaced)
2025-07-22 21:31:57.840 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: extinguisher_misplaced (平台: KS988)
2025-07-22 21:31:57.840 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\face\model\model.yaml (算法: face)
2025-07-22 21:31:57.841 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: face (平台: KS988)
2025-07-22 21:31:57.841 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\fall_down\model\model.yaml (算法: fall_down)
2025-07-22 21:31:57.842 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: fall_down (平台: KS988)
2025-07-22 21:31:57.842 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\fatigue\model\model.yaml (算法: fatigue)
2025-07-22 21:31:57.843 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: fatigue (平台: KS988)
2025-07-22 21:31:57.843 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\fight\model\model.yaml (算法: fight)
2025-07-22 21:31:57.844 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: fight (平台: KS988)
2025-07-22 21:31:57.845 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\fire\model\model.yaml (算法: fire)
2025-07-22 21:31:57.846 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: fire (平台: KS988)
2025-07-22 21:31:57.846 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\gloves\model\model.yaml (算法: gloves)
2025-07-22 21:31:57.847 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: gloves (平台: KS988)
2025-07-22 21:31:57.847 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\goggles\model\model.yaml (算法: goggles)
2025-07-22 21:31:57.848 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: goggles (平台: KS988)
2025-07-22 21:31:57.848 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\helmet\model\model.yaml (算法: helmet)
2025-07-22 21:31:57.849 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: helmet (平台: KS988)
2025-07-22 21:31:57.849 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\life_jacket\model\model.yaml (算法: life_jacket)
2025-07-22 21:31:57.850 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: life_jacket (平台: KS988)
2025-07-22 21:31:57.850 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\lpr\model\model.yaml (算法: lpr)
2025-07-22 21:31:57.851 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: lpr (平台: KS988)
2025-07-22 21:31:57.851 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\motion\model\model.yaml (算法: motion)
2025-07-22 21:31:57.852 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: motion (平台: KS988)
2025-07-22 21:31:57.852 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\ocr\model\model.yaml (算法: ocr)
2025-07-22 21:31:57.853 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: ocr (平台: KS988)
2025-07-22 21:31:57.853 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\on_duty\model\model.yaml (算法: on_duty)
2025-07-22 21:31:57.854 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: on_duty (平台: KS988)
2025-07-22 21:31:57.854 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\person_counting\model\model.yaml (算法: person_counting)
2025-07-22 21:31:57.855 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: person_counting (平台: KS988)
2025-07-22 21:31:57.855 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\person_departure\model\model.yaml (算法: person_departure)
2025-07-22 21:31:57.856 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: person_departure (平台: KS988)
2025-07-22 21:31:57.857 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\person_gathering\model\model.yaml (算法: person_gathering)
2025-07-22 21:31:57.857 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: person_gathering (平台: KS988)
2025-07-22 21:31:57.858 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\person_intrusion\model\model.yaml (算法: person_intrusion)
2025-07-22 21:31:57.858 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: person_intrusion (平台: KS988)
2025-07-22 21:31:57.859 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\person_loitering\model\model.yaml (算法: person_loitering)
2025-07-22 21:31:57.859 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: person_loitering (平台: KS988)
2025-07-22 21:31:57.860 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\person_sleep\model\model.yaml (算法: person_sleep)
2025-07-22 21:31:57.860 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: person_sleep (平台: KS988)
2025-07-22 21:31:57.861 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\play_phone\model\model.yaml (算法: play_phone)
2025-07-22 21:31:57.861 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: play_phone (平台: KS988)
2025-07-22 21:31:57.862 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\reflective_vest\model\model.yaml (算法: reflective_vest)
2025-07-22 21:31:57.863 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: reflective_vest (平台: KS988)
2025-07-22 21:31:57.863 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\shoes\model\model.yaml (算法: shoes)
2025-07-22 21:31:57.864 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: shoes (平台: KS988)
2025-07-22 21:31:57.864 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\small_car_misplaced\model\model.yaml (算法: small_car_misplaced)
2025-07-22 21:31:57.865 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: small_car_misplaced (平台: KS988)
2025-07-22 21:31:57.865 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\smog\model\model.yaml (算法: smog)
2025-07-22 21:31:57.866 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: smog (平台: KS988)
2025-07-22 21:31:57.867 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\smoke\model\model.yaml (算法: smoke)
2025-07-22 21:31:57.867 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: smoke (平台: KS988)
2025-07-22 21:31:57.868 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\work_clothes\model\model.yaml (算法: work_clothes)
2025-07-22 21:31:57.868 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:118 - 加载算法能力: work_clothes (平台: KS988)
2025-07-22 21:31:57.869 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:73 - 扫描平台: KS968
2025-07-22 21:31:57.869 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 black_screen 已加载，跳过平台 KS968
2025-07-22 21:31:57.869 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 camera_blur 已加载，跳过平台 KS968
2025-07-22 21:31:57.870 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 camera_occlusion 已加载，跳过平台 KS968
2025-07-22 21:31:57.870 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 camera_shift 已加载，跳过平台 KS968
2025-07-22 21:31:57.870 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 car_counting 已加载，跳过平台 KS968
2025-07-22 21:31:57.870 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 car_misplaced 已加载，跳过平台 KS968
2025-07-22 21:31:57.870 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 car_type 已加载，跳过平台 KS968
2025-07-22 21:31:57.871 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 channel_obstruction 已加载，跳过平台 KS968
2025-07-22 21:31:57.871 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 electric_bike_misplaced 已加载，跳过平台 KS968
2025-07-22 21:31:57.871 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 electric_vehicle_elevator 已加载，跳过平台 KS968
2025-07-22 21:31:57.871 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 extinguisher_misplaced 已加载，跳过平台 KS968
2025-07-22 21:31:57.871 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 face 已加载，跳过平台 KS968
2025-07-22 21:31:57.872 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 fall_down 已加载，跳过平台 KS968
2025-07-22 21:31:57.872 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 fatigue 已加载，跳过平台 KS968
2025-07-22 21:31:57.872 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 fight 已加载，跳过平台 KS968
2025-07-22 21:31:57.872 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 fire 已加载，跳过平台 KS968
2025-07-22 21:31:57.872 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 gloves 已加载，跳过平台 KS968
2025-07-22 21:31:57.873 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 goggles 已加载，跳过平台 KS968
2025-07-22 21:31:57.873 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 helmet 已加载，跳过平台 KS968
2025-07-22 21:31:57.873 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 life_jacket 已加载，跳过平台 KS968
2025-07-22 21:31:57.873 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 lpr 已加载，跳过平台 KS968
2025-07-22 21:31:57.874 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 motion 已加载，跳过平台 KS968
2025-07-22 21:31:57.874 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 ocr 已加载，跳过平台 KS968
2025-07-22 21:31:57.874 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 on_duty 已加载，跳过平台 KS968
2025-07-22 21:31:57.874 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 person_counting 已加载，跳过平台 KS968
2025-07-22 21:31:57.875 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 person_departure 已加载，跳过平台 KS968
2025-07-22 21:31:57.875 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 person_gathering 已加载，跳过平台 KS968
2025-07-22 21:31:57.875 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 person_intrusion 已加载，跳过平台 KS968
2025-07-22 21:31:57.875 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 person_loitering 已加载，跳过平台 KS968
2025-07-22 21:31:57.876 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 person_sleep 已加载，跳过平台 KS968
2025-07-22 21:31:57.876 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 play_phone 已加载，跳过平台 KS968
2025-07-22 21:31:57.876 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 reflective_vest 已加载，跳过平台 KS968
2025-07-22 21:31:57.876 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 shoes 已加载，跳过平台 KS968
2025-07-22 21:31:57.877 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 small_car_misplaced 已加载，跳过平台 KS968
2025-07-22 21:31:57.877 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 smog 已加载，跳过平台 KS968
2025-07-22 21:31:57.877 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 smoke 已加载，跳过平台 KS968
2025-07-22 21:31:57.877 |  | DEBUG    | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:82 - 算法 work_clothes 已加载，跳过平台 KS968
2025-07-22 21:31:57.878 |  | INFO     | module_stream.service.algorithm_standard_service:get_algorithm_capabilities:129 - 成功加载 37 个算法能力描述
2025-07-22 21:31:57.893 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS968\helmet\model\model.yaml (算法: helmet)
2025-07-22 21:31:57.893 |  | DEBUG    | module_stream.service.algorithm_standard_service:_has_valid_model_files:168 - 找到模型配置文件: d:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS988\helmet\model\model.yaml (算法: helmet)
