"""
实时监控WebSocket路由
"""

import asyncio
from fastapi import APIRouter, WebSocket, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_stream.controller.monitor_websocket_controller import monitor_websocket_controller
from utils.response_util import ResponseUtil
from utils.log_util import logger

# 创建路由器
websocket_router = APIRouter(prefix="/monitor", tags=["实时监控WebSocket"])


@websocket_router.websocket("/test")
async def test_websocket(websocket: WebSocket):
    """
    测试WebSocket连接
    """
    try:
        await websocket.accept()
        logger.info("WebSocket测试连接已建立")

        await websocket.send_json({
            'type': 'test_success',
            'message': 'WebSocket连接测试成功',
            'timestamp': asyncio.get_event_loop().time()
        })

        # 保持连接一段时间
        await asyncio.sleep(5)
        await websocket.close(code=1000, reason="测试完成")

    except Exception as e:
        logger.error(f"WebSocket测试失败: {e}")
        try:
            await websocket.close(code=4000, reason=f"测试失败: {str(e)}")
        except:
            pass


@websocket_router.websocket("/stream/{task_id}")
async def monitor_stream_websocket(
    websocket: WebSocket,
    task_id: int
):
    """
    实时监控流WebSocket连接（无认证版本）

    :param websocket: WebSocket连接
    :param task_id: 任务ID
    """
    await monitor_websocket_controller.connect_monitor_stream(
        websocket=websocket,
        task_id=task_id
    )


@websocket_router.get("/stats")
async def get_monitor_stats():
    """
    获取监控连接统计信息
    """
    try:
        stats = monitor_websocket_controller.get_connection_stats()
        return ResponseUtil.success(data=stats, msg="获取统计信息成功")
    except Exception as e:
        logger.error(f"获取监控统计失败: {e}")
        return ResponseUtil.error(msg=f"获取统计信息失败: {str(e)}")


@websocket_router.post("/broadcast/{task_id}")
async def broadcast_message(
    task_id: int,
    message: dict
):
    """
    向指定任务的所有监控客户端广播消息
    
    :param task_id: 任务ID
    :param message: 要广播的消息
    """
    try:
        await monitor_websocket_controller.broadcast_to_task(task_id, message)
        return ResponseUtil.success(msg="广播消息成功")
    except Exception as e:
        logger.error(f"广播消息失败: {e}")
        return ResponseUtil.error(msg=f"广播消息失败: {str(e)}")


@websocket_router.post("/disconnect-all")
async def disconnect_all_clients():
    """
    断开所有监控客户端连接
    """
    try:
        await monitor_websocket_controller.disconnect_all_clients()
        return ResponseUtil.success(msg="所有客户端已断开连接")
    except Exception as e:
        logger.error(f"断开所有客户端失败: {e}")
        return ResponseUtil.error(msg=f"断开客户端失败: {str(e)}")
