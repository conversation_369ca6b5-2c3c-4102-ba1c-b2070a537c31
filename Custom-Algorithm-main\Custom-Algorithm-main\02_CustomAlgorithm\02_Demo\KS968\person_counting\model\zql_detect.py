import cv2
import numpy as np

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))
from logger import LOGGER
from model import Model as PyTorchModel


class Model:
    default_args = {
        'img_size': 640,
        'nms_thres': 0.45,
        'conf_thres': 0.25,
        'anchors': [[10, 13], [16, 30], [33, 23], [30, 61], [62, 45], [59, 119], [116, 90], [156, 198], [373, 326]]
    }

    def __init__(self, acc_id, name, conf):
        # 使用PyTorch模型
        self.pytorch_model = PyTorchModel()
        self.acc_id = acc_id
        self.name = name
        self.conf = conf

        # 从配置中获取参数
        self.img_size = conf.get('img_size', self.default_args['img_size'])
        self.nms_thres = conf.get('nms_thres', self.default_args['nms_thres'])
        self.conf_thres = conf.get('conf_thres', self.default_args['conf_thres'])

        # 设置状态
        self.status = self.pytorch_model.status

        # 暴露PyTorch模型的属性
        self.device = self.pytorch_model.device

    @property
    def status(self):
        """获取模型状态"""
        return getattr(self, '_status', False)
    
    @status.setter
    def status(self, value):
        """设置模型状态"""
        self._status = value

    def infer(self, data, **kwargs):
        """
        目标检测
        Args:
            data: 图像数据，ndarray类型，RGB格式（BGR格式需转换）
        Returns: infer_result
        """
        # 直接调用PyTorch模型
        return self.pytorch_model.infer(data, **kwargs)
