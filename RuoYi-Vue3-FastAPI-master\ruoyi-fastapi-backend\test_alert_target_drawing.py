#!/usr/bin/env python3
"""
告警目标绘制测试脚本
测试修正后的检测框绘制逻辑，只绘制告警目标
"""

import cv2
import numpy as np
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class AlertTargetDrawingTester:
    """告警目标绘制测试器"""
    
    def create_test_frame(self, width=640, height=480):
        """创建测试帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(128 + 127 * np.sin(x * 0.01)),
                    int(128 + 127 * np.sin(y * 0.01)),
                    128
                ]
        
        # 添加标题
        cv2.putText(frame, "Alert Target Test", (width//4, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return frame
    
    def create_alert_result_with_targets(self):
        """创建包含告警目标的告警结果"""
        return {
            'hit': True,
            'message': '检测到2个告警目标',
            'data': {
                'bbox': {
                    'rectangles': [[100, 100, 200, 200], [300, 150, 400, 250]],
                    'polygons': {}
                }
            },
            'details': {
                'alert_targets': [
                    {
                        'bbox': [100, 100, 200, 200],
                        'confidence': 0.85,
                        'alert_type': 'area_intrusion',
                        'alert_reason': '进入禁区'
                    },
                    {
                        'bbox': [300, 150, 400, 250],
                        'confidence': 0.72,
                        'alert_type': 'line_crossing',
                        'alert_reason': '越线行为'
                    }
                ],
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '禁区',
                        'points': [
                            {'x': 50, 'y': 50},
                            {'x': 250, 'y': 50},
                            {'x': 250, 'y': 250},
                            {'x': 50, 'y': 250}
                        ]
                    }
                ],
                'configured_lines': [
                    {
                        'id': 'line_1',
                        'name': '警戒线',
                        'points': [
                            {'x': 280, 'y': 0},
                            {'x': 280, 'y': 480}
                        ]
                    }
                ],
                'image_size': {'width': 640, 'height': 480}
            }
        }
    
    def create_alert_result_area_detection(self):
        """创建区域检测告警结果"""
        return {
            'hit': True,
            'message': '检测到区域入侵',
            'data': {},
            'details': {
                'in_area_detections': [
                    {
                        'box': [150, 120, 220, 180],
                        'area_name': '重要区域',
                        'score': 0.91
                    }
                ],
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '重要区域',
                        'points': [
                            {'x': 100, 'y': 100},
                            {'x': 300, 'y': 100},
                            {'x': 300, 'y': 200},
                            {'x': 100, 'y': 200}
                        ]
                    }
                ],
                'configured_lines': [],
                'image_size': {'width': 640, 'height': 480}
            }
        }
    
    def create_alert_result_line_detection(self):
        """创建线段检测告警结果"""
        return {
            'hit': True,
            'message': '检测到越线',
            'data': {},
            'details': {
                'line_crossing_detections': [
                    {
                        'box': [350, 180, 420, 240],
                        'line_name': '计数线',
                        'score': 0.88
                    }
                ],
                'configured_areas': [],
                'configured_lines': [
                    {
                        'id': 'line_1',
                        'name': '计数线',
                        'points': [
                            {'x': 320, 'y': 0},
                            {'x': 320, 'y': 480}
                        ]
                    }
                ],
                'image_size': {'width': 640, 'height': 480}
            }
        }
    
    def create_no_alert_result(self):
        """创建无告警结果"""
        return {
            'hit': False,
            'message': '无告警',
            'data': {},
            'details': {
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '监控区域',
                        'points': [
                            {'x': 200, 'y': 200},
                            {'x': 400, 'y': 200},
                            {'x': 400, 'y': 350},
                            {'x': 200, 'y': 350}
                        ]
                    }
                ],
                'configured_lines': [],
                'image_size': {'width': 640, 'height': 480}
            }
        }
    
    def test_alert_targets_drawing(self):
        """测试告警目标绘制"""
        print("=" * 60)
        print("测试告警目标绘制")
        print("=" * 60)
        
        try:
            frame = self.create_test_frame()
            alert_result = self.create_alert_result_with_targets()
            
            print(f"✅ 创建测试数据")
            print(f"  - 告警状态: {alert_result['hit']}")
            print(f"  - 告警目标: {len(alert_result['details']['alert_targets'])}个")
            
            # 绘制告警目标
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, alert_result)
            
            # 保存结果
            cv2.imwrite("test_alert_targets.jpg", annotated_frame)
            print("✅ 告警目标绘制完成，保存为: test_alert_targets.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 告警目标绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_area_detection_drawing(self):
        """测试区域检测绘制"""
        print("\n" + "=" * 60)
        print("测试区域检测绘制")
        print("=" * 60)
        
        try:
            frame = self.create_test_frame()
            alert_result = self.create_alert_result_area_detection()
            
            print(f"✅ 创建区域检测数据")
            print(f"  - 告警状态: {alert_result['hit']}")
            print(f"  - 区域检测: {len(alert_result['details']['in_area_detections'])}个")
            
            # 绘制区域检测
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, alert_result)
            
            # 保存结果
            cv2.imwrite("test_area_detection.jpg", annotated_frame)
            print("✅ 区域检测绘制完成，保存为: test_area_detection.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 区域检测绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_line_detection_drawing(self):
        """测试线段检测绘制"""
        print("\n" + "=" * 60)
        print("测试线段检测绘制")
        print("=" * 60)
        
        try:
            frame = self.create_test_frame()
            alert_result = self.create_alert_result_line_detection()
            
            print(f"✅ 创建线段检测数据")
            print(f"  - 告警状态: {alert_result['hit']}")
            print(f"  - 线段检测: {len(alert_result['details']['line_crossing_detections'])}个")
            
            # 绘制线段检测
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, alert_result)
            
            # 保存结果
            cv2.imwrite("test_line_detection.jpg", annotated_frame)
            print("✅ 线段检测绘制完成，保存为: test_line_detection.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 线段检测绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_no_alert_drawing(self):
        """测试无告警绘制"""
        print("\n" + "=" * 60)
        print("测试无告警绘制")
        print("=" * 60)
        
        try:
            frame = self.create_test_frame()
            alert_result = self.create_no_alert_result()
            
            print(f"✅ 创建无告警数据")
            print(f"  - 告警状态: {alert_result['hit']}")
            
            # 绘制无告警状态
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, alert_result)
            
            # 保存结果
            cv2.imwrite("test_no_alert.jpg", annotated_frame)
            print("✅ 无告警绘制完成，保存为: test_no_alert.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 无告警绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主测试函数"""
    print("🎯 告警目标绘制测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = AlertTargetDrawingTester()
    
    # 运行测试
    tests = [
        ("告警目标绘制", tester.test_alert_targets_drawing),
        ("区域检测绘制", tester.test_area_detection_drawing),
        ("线段检测绘制", tester.test_line_detection_drawing),
        ("无告警绘制", tester.test_no_alert_drawing)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🏁 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 告警目标绘制功能正常")
        print("\n📋 功能确认:")
        print("  ✅ 只绘制告警目标，不绘制所有检测结果")
        print("  ✅ 支持多种告警类型的颜色区分")
        print("  ✅ 正确显示告警原因和置信度")
        print("  ✅ 无告警时只显示监控状态")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
