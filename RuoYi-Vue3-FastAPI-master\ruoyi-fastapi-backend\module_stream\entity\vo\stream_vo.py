from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class StreamModel(BaseModel):
    """
    视频流管理表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    stream_id: Optional[int] = Field(default=None, description='流ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    stream_name: Optional[str] = Field(default=None, description='流名称')
    rtsp_url: Optional[str] = Field(default=None, description='RTSP地址')
    location: Optional[str] = Field(default=None, description='安装位置')
    stream_config: Optional[dict] = Field(default=None, description='流配置参数')
    status: Optional[str] = Field(default=None, description='状态(0停用 1启用)')
    is_recording: Optional[int] = Field(default=None, description='是否录制')
    del_flag: Optional[str] = Field(default=None, description='删除标志（0代表存在 2代表删除）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')

    @NotBlank(field_name='stream_name', message='流名称不能为空')
    def get_stream_name(self):
        return self.stream_name

    @NotBlank(field_name='rtsp_url', message='RTSP地址不能为空')
    def get_rtsp_url(self):
        return self.rtsp_url


    def validate_fields(self):
        self.get_stream_name()
        self.get_rtsp_url()




class StreamQueryModel(StreamModel):
    """
    视频流管理不分页查询模型
    """
    pass


@as_query
class StreamPageQueryModel(StreamQueryModel):
    """
    视频流管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteStreamModel(BaseModel):
    """
    删除视频流管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    stream_ids: str = Field(description='需要删除的流ID')
