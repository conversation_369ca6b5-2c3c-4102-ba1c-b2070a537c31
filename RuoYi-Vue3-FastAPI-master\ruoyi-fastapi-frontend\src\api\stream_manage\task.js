import request from '@/utils/request'

// 查询检测任务列表
export function listTask(query) {
  return request({
    url: '/stream_manage/task/list',
    method: 'get',
    params: query
  })
}

// 查询检测任务详细
export function getTask(taskId) {
  return request({
    url: '/stream_manage/task/' + taskId,
    method: 'get'
  })
}

// 新增检测任务
export function addTask(data) {
  const formData = new FormData()
  formData.append('taskName', data.taskName)
  formData.append('streamId', data.streamId)
  formData.append('algorithmId', data.algorithmId)
  formData.append('remark', data.remark || '')

  return request({
    url: '/stream_manage/task',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 修改检测任务
export function updateTask(data) {
  const formData = new FormData()
  formData.append('taskId', data.taskId)
  formData.append('taskName', data.taskName)
  formData.append('remark', data.remark || '')

  return request({
    url: '/stream_manage/task',
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除检测任务
export function delTask(taskId) {
  return request({
    url: '/stream_manage/task/' + taskId,
    method: 'delete'
  })
}

// 启动任务
export function startTask(taskId) {
  return request({
    url: '/stream_manage/task/start/' + taskId,
    method: 'post'
  })
}

// 停止任务
export function stopTask(taskId) {
  return request({
    url: '/stream_manage/task/stop/' + taskId,
    method: 'post'
  })
}

// 暂停任务
export function pauseTask(taskId) {
  return request({
    url: '/stream_manage/task/pause/' + taskId,
    method: 'post'
  })
}

// 批量启动任务
export function batchStartTasks(taskIds) {
  return request({
    url: '/surveillance/monitor/batch-start',
    method: 'post',
    data: taskIds
  })
}

// 批量停止任务
export function batchStopTasks(taskIds) {
  return request({
    url: '/surveillance/monitor/batch-stop',
    method: 'post',
    data: taskIds
  })
}

// 统一启动任务（单个任务也使用批量接口）
export function startTaskUnified(taskId) {
  return batchStartTasks([taskId])
}

// 统一停止任务（单个任务也使用批量接口）
export function stopTaskUnified(taskId) {
  return batchStopTasks([taskId])
}
