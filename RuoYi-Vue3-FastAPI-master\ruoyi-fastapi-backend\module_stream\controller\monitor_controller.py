from fastapi import APIRouter, Depends, Request, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from config.get_db import get_db
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_stream.service.monitor_service import MonitorService
from module_stream.entity.vo.task_vo import TaskPageQueryModel
from utils.log_util import logger
from utils.response_util import ResponseUtil


monitorController = APIRouter(prefix='/surveillance/monitor', dependencies=[Depends(LoginService.get_current_user)])


@monitorController.get(
    '/tasks', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:monitor:view'))]
)
async def get_monitor_tasks(
    request: Request,
    pageNum: int = Query(1, description='当前页码'),
    pageSize: int = Query(100, description='每页记录数'),
    taskName: Optional[str] = Query(None, description='任务名称'),
    status: Optional[str] = Query(None, description='状态'),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取监控任务列表

    :param request: Request对象
    :param pageNum: 当前页码
    :param pageSize: 每页记录数
    :param taskName: 任务名称
    :param status: 状态
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 监控任务列表
    """
    # 构建查询参数对象
    task_page_query_model = TaskPageQueryModel(
        task_name=taskName,
        status=status,
        page_num=pageNum,
        page_size=pageSize
    )
    
    # 获取监控任务列表
    monitor_tasks_result = await MonitorService.get_monitor_tasks_services(
        query_db, task_page_query_model, current_user.user.user_id
    )
    logger.info('获取监控任务列表成功')
    return ResponseUtil.success(model_content=monitor_tasks_result)


@monitorController.get(
    '/running-tasks', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:monitor:view'))]
)
async def get_running_tasks(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取运行中的任务列表

    :param request: Request对象
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 运行中的任务列表
    """
    running_tasks_result = await MonitorService.get_running_tasks_services(
        query_db, current_user.user.user_id
    )
    logger.info('获取运行中任务列表成功')
    return ResponseUtil.success(data=running_tasks_result)


@monitorController.get(
    '/task-status/{task_id}', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:monitor:view'))]
)
async def get_task_status(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取任务状态信息

    :param request: Request对象
    :param task_id: 任务ID
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 任务状态信息
    """
    task_status_result = await MonitorService.get_task_status_services(
        query_db, task_id, current_user.user.user_id
    )
    logger.info(f'获取任务 {task_id} 状态成功')
    return ResponseUtil.success(data=task_status_result)


@monitorController.get(
    '/statistics', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:monitor:view'))]
)
async def get_monitor_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取监控统计信息

    :param request: Request对象
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 监控统计信息
    """
    statistics_result = await MonitorService.get_monitor_statistics_services(
        query_db, current_user.user.user_id
    )
    logger.info('获取监控统计信息成功')
    return ResponseUtil.success(data=statistics_result)


@monitorController.post(
    '/batch-start', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:monitor:control'))]
)
async def batch_start_tasks(
    request: Request,
    task_ids: List[int] = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量启动任务

    :param request: Request对象
    :param task_ids: 任务ID列表
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 批量启动结果
    """
    try:
        batch_start_result = await MonitorService.batch_start_tasks_services(
            query_db, task_ids, current_user.user.user_id
        )

        # 检查启动结果
        success_count = batch_start_result.get('success_count', 0)
        failed_count = batch_start_result.get('failed_count', 0)
        total_count = batch_start_result.get('total_count', len(task_ids))

        if success_count == total_count:
            logger.info(f'批量启动任务完全成功: {task_ids}')
            return ResponseUtil.success(
                data=batch_start_result,
                msg=f"成功启动 {success_count} 个任务"
            )
        elif success_count > 0:
            logger.warning(f'批量启动任务部分成功: 成功{success_count}, 失败{failed_count}')
            return ResponseUtil.success(
                data=batch_start_result,
                msg=f"部分成功：启动 {success_count} 个任务，{failed_count} 个失败"
            )
        else:
            failed_tasks = batch_start_result.get('failed_tasks', [])
            error_msg = f"启动任务失败: {[t.get('error', 'Unknown') for t in failed_tasks]}"
            logger.error(f'批量启动任务失败: {task_ids}, 错误: {error_msg}')
            return ResponseUtil.error(msg=error_msg, data=batch_start_result)

    except Exception as e:
        logger.error(f'批量启动任务异常: {task_ids}, 错误: {e}')
        return ResponseUtil.error(msg=f"启动任务异常: {str(e)}")


@monitorController.post(
    '/batch-stop', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:monitor:control'))]
)
async def batch_stop_tasks(
    request: Request,
    task_ids: List[int] = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量停止任务

    :param request: Request对象
    :param task_ids: 任务ID列表
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 批量停止结果
    """
    try:
        batch_stop_result = await MonitorService.batch_stop_tasks_services(
            query_db, task_ids, current_user.user.user_id
        )

        # 检查停止结果
        success_count = batch_stop_result.get('success_count', 0)
        failed_count = batch_stop_result.get('failed_count', 0)
        total_count = batch_stop_result.get('total_count', len(task_ids))

        # 停止任务：只要没有异常就算成功
        if success_count > 0 or failed_count == 0:
            logger.info(f'批量停止任务成功: {task_ids}')
            return ResponseUtil.success(
                data=batch_stop_result,
                msg=f"成功停止 {success_count} 个任务"
            )
        else:
            failed_tasks = batch_stop_result.get('failed_tasks', [])
            error_msg = f"停止任务失败: {[t.get('error', 'Unknown') for t in failed_tasks]}"
            logger.error(f'批量停止任务失败: {task_ids}, 错误: {error_msg}')
            return ResponseUtil.error(msg=error_msg, data=batch_stop_result)

    except Exception as e:
        logger.error(f'批量停止任务异常: {task_ids}, 错误: {e}')
        return ResponseUtil.error(msg=f"停止任务异常: {str(e)}")
