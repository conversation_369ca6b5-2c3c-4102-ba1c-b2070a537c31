# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

name: 🚀 Feature Request
description: Suggest a YOLOv5 idea
# title: " "
labels: [enhancement]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for submitting a YOLOv5 🚀 Feature Request!

  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please search the [issues](https://github.com/ultralytics/yolov5/issues) to see if a similar feature request already exists.
      options:
        - label: >
            I have searched the YOLOv5 [issues](https://github.com/ultralytics/yolov5/issues) and found no similar feature requests.
          required: true

  - type: textarea
    attributes:
      label: Description
      description: A short description of your feature.
      placeholder: |
        What new feature would you like to see in YOLOv5?
    validations:
      required: true

  - type: textarea
    attributes:
      label: Use case
      description: |
        Describe the use case of your feature request. It will help us understand and prioritize the feature request.
      placeholder: |
        How would this feature be used, and who would use it?

  - type: textarea
    attributes:
      label: Additional
      description: Anything else you would like to share?

  - type: checkboxes
    attributes:
      label: Are you willing to submit a PR?
      description: >
        (Optional) We encourage you to submit a [Pull Request](https://github.com/ultralytics/yolov5/pulls) (PR) to help improve YOLOv5 for everyone, especially if you have a good understanding of how to implement a fix or feature.
        See the YOLOv5 [Contributing Guide](https://docs.ultralytics.com/help/contributing) to get started.
      options:
        - label: Yes I'd like to help by submitting a PR!
