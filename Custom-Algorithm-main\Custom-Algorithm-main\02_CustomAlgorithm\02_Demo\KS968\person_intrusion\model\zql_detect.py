import cv2
import numpy as np

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

# 导入本地模块
from logger import LOGGER

# 导入RknnModel - 使用绝对路径导入
model_file = os.path.join(current_dir, 'model.py')
import importlib.util
spec = importlib.util.spec_from_file_location("model_module", model_file)
model_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(model_module)
RknnModel = model_module.RknnModel


class Model(RknnModel):
    default_args = {
        'img_size': 640,
        'nms_thres': 0.45,
        'conf_thres': 0.25,
        'anchors': [[10, 13], [16, 30], [33, 23], [30, 61], [62, 45], [59, 119], [116, 90], [156, 198], [373, 326]]
    }

    def __init__(self, acc_id, name, conf):
        super().__init__(acc_id, name, conf, ['model'])

    def __yolov5_post_process(self, input_data):
        masks = [[0, 1, 2], [3, 4, 5], [6, 7, 8]]
        boxes, classes, scores = [], [], []
        for input_, mask in zip(input_data, masks):
            b, c, s = self.__process(input_, mask)
            b, c, s = self.__filter_boxes(b, c, s)
            boxes.append(b)
            classes.append(c)
            scores.append(s)
        boxes = np.concatenate(boxes)
        boxes = self._xywh2xyxy(boxes)
        classes = np.concatenate(classes)
        scores = np.concatenate(scores)
        nboxes, nclasses, nscores = [], [], []
        keep = self._nms_boxes(boxes, scores)
        if len(keep) != 0:
            nboxes.append(boxes[keep])
            nclasses.append(classes[keep])
            nscores.append(scores[keep])
        if not nclasses and not nscores:
            return None, None, None
        return np.concatenate(nboxes), np.concatenate(nclasses), np.concatenate(nscores)

    def __process(self, input_, mask):
        anchors = [self.anchors[i] for i in mask]
        grid_h, grid_w = map(int, input_.shape[0:2])
        box_confidence = input_[..., 4]
        box_confidence = np.expand_dims(box_confidence, axis=-1)
        box_class_probs = input_[..., 5:]
        box_xy = input_[..., :2] * 2 - 0.5
        col = np.tile(np.arange(0, grid_w), grid_w).reshape(-1, grid_w)
        row = np.tile(np.arange(0, grid_h).reshape(-1, 1), grid_h)
        col = col.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=-2)
        row = row.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=-2)
        grid = np.concatenate((col, row), axis=-1)
        box_xy += grid
        box_xy *= int(self.img_size / grid_h)
        box_wh = pow(input_[..., 2:4] * 2, 2)
        box_wh = box_wh * anchors
        return np.concatenate((box_xy, box_wh), axis=-1), box_confidence, box_class_probs

    def __filter_boxes(self, boxes, box_confidences, box_class_probs):
        """
        Filter boxes with box threshold. It's a bit different with origin yolov5 post process!
        Args:
            boxes: ndarray, boxes of objects.
            box_confidences: ndarray, confidences of objects.
            box_class_probs: ndarray, class_probs of objects.
        Returns:
            boxes: ndarray, filtered boxes.
            classes: ndarray, classes for boxes.
            scores: ndarray, scores for boxes.
        """
        boxes = boxes.reshape(-1, 4)
        box_confidences = box_confidences.reshape(-1)
        box_class_probs = box_class_probs.reshape(-1, box_class_probs.shape[-1])
        _box_pos = np.where(box_confidences >= self.conf_thres)
        boxes = boxes[_box_pos]
        box_confidences = box_confidences[_box_pos]
        box_class_probs = box_class_probs[_box_pos]
        class_max_score = np.max(box_class_probs, axis=-1)
        classes = np.argmax(box_class_probs, axis=-1)
        _class_pos = np.where(class_max_score >= self.conf_thres)
        return boxes[_class_pos], classes[_class_pos], (class_max_score * box_confidences)[_class_pos]

    def _load_args(self, args):
        try:
            self.img_size = args.get('img_size', self.default_args['img_size'])
            self.nms_thres = args.get('nms_thres', self.default_args['nms_thres'])
            self.conf_thres = args.get('conf_thres', self.default_args['conf_thres'])
            self.anchors = args.get('anchors', self.default_args['anchors'])
        except:
            LOGGER.exception('_load_args')
            return False
        return True

    def infer(self, data, **kwargs):
        """
        目标检测 - 使用PyTorch模型进行推理
        Args:
            data: 图像数据，ndarray类型，RGB格式（BGR格式需转换）
        Returns: infer_result
        """
        infer_result = []
        if self.status and 'model' in self.models:
            try:
                image = data
                raw_width, raw_height = image.shape[1], image.shape[0]

                # 默认不缩放，保持原始尺寸（生产环境配置）
                use_letterbox = kwargs.get('use_letterbox', False)
                target_size = kwargs.get('target_size', 0)  # 0表示不缩放
                preserve_aspect_ratio = kwargs.get('preserve_aspect_ratio', True)

                LOGGER.info(f"推理配置: use_letterbox={use_letterbox}, target_size={target_size}, preserve_aspect_ratio={preserve_aspect_ratio}")
                LOGGER.info(f"原始图像尺寸: {image.shape}")

                # 图像预处理
                processed_image = image.copy()
                scale = 1.0
                dw, dh = 0, 0

                if target_size > 0:
                    if preserve_aspect_ratio:
                        # 保持宽高比的缩放
                        if max(image.shape[:2]) != target_size:
                            scale = target_size / max(image.shape[:2])
                            if raw_height > raw_width:
                                processed_image = cv2.resize(image, (int(raw_width * scale), target_size))
                            else:
                                processed_image = cv2.resize(image, (target_size, int(raw_height * scale)))

                        if use_letterbox:
                            processed_image, dw, dh = self._letterbox(processed_image, (target_size, target_size))
                    else:
                        # 直接缩放到目标尺寸
                        scale_w = target_size / raw_width
                        scale_h = target_size / raw_height
                        scale = min(scale_w, scale_h)
                        processed_image = cv2.resize(image, (target_size, target_size))
                else:
                    # 不缩放，保持原始尺寸
                    LOGGER.info("保持原始图像尺寸，不进行缩放")

                LOGGER.info(f"处理后图像尺寸: {processed_image.shape}, 缩放比例: {scale}, 填充: ({dw}, {dh})")

                # 使用PyTorch模型进行推理
                model = self.models['model']

                # 将OpenCV图像转换为PyTorch张量格式
                import torch
                import numpy as np
                import cv2

                # 确保图像是numpy数组
                if not isinstance(processed_image, np.ndarray):
                    processed_image = np.array(processed_image)

                # 转换图像格式：从 HWC 到 CHW，并添加batch维度
                if len(processed_image.shape) == 3:
                    # 从 BGR 转换为 RGB（如果需要）
                    if processed_image.shape[2] == 3:
                        processed_image = processed_image[:, :, ::-1].copy()  # BGR to RGB，使用copy()避免负步长

                    # YOLOv5需要输入尺寸是32的倍数，调整图像尺寸
                    h, w = processed_image.shape[:2]

                    # 计算最接近的32的倍数
                    new_h = ((h + 31) // 32) * 32
                    new_w = ((w + 31) // 32) * 32

                    # 如果尺寸需要调整，进行resize
                    if new_h != h or new_w != w:
                        processed_image = cv2.resize(processed_image, (new_w, new_h))
                        LOGGER.info(f"调整图像尺寸: {w}x{h} -> {new_w}x{new_h}")

                    # 转换为张量并调整维度：HWC -> CHW -> BCHW
                    tensor_image = torch.from_numpy(processed_image).float()
                    tensor_image = tensor_image.permute(2, 0, 1)  # HWC -> CHW
                    tensor_image = tensor_image.unsqueeze(0)  # CHW -> BCHW (添加batch维度)

                    # 归一化到0-1范围
                    tensor_image = tensor_image / 255.0

                    LOGGER.info(f"转换后张量形状: {tensor_image.shape}")
                else:
                    LOGGER.error(f"不支持的图像维度: {processed_image.shape}")
                    return []

                # 执行推理 - 兼容不同的模型类型（参考yolo_ROI_ai.py）
                results = model(tensor_image)

                # 处理不同类型的检测结果
                detections = self._process_model_results(results)

                # 获取支持的类别
                class_mapping = self._get_class_mapping_from_config()
                supported_classes = list(class_mapping.get('class2label', {}).keys())
                supported_classes = [int(c) for c in supported_classes]  # 确保是整数

                # 转换检测结果格式
                for _, detection in detections.iterrows():
                    if detection['confidence'] >= self.conf_thres:
                        # 只处理配置文件中支持的类别
                        if int(detection['class']) in supported_classes:
                            obj = {
                                'label': int(detection['class']),
                                'conf': round(float(detection['confidence']), 2),
                                'name': detection.get('name', 'unknown'),
                                'ch_name': detection.get('ch_name', detection.get('name', 'unknown'))
                            }

                        # 坐标转换回原始图像
                        x1, y1, x2, y2 = detection['xmin'], detection['ymin'], detection['xmax'], detection['ymax']

                        # 去除letterbox填充
                        x1, y1, x2, y2 = x1 - dw, y1 - dh, x2 - dw, y2 - dh

                        # 缩放回原始尺寸
                        if scale != 1:
                            x1, y1, x2, y2 = x1 / scale, y1 / scale, x2 / scale, y2 / scale

                        # 确保坐标在图像范围内
                        xyxy = [
                            max(0, min(int(x1), raw_width)),
                            max(0, min(int(y1), raw_height)),
                            max(0, min(int(x2), raw_width)),
                            max(0, min(int(y2), raw_height))
                        ]

                        obj['xyxy'] = xyxy
                        infer_result.append(obj)

                LOGGER.info(f"检测到 {len(infer_result)} 个目标")

            except Exception as e:
                LOGGER.exception(f'推理失败: {e}')
        else:
            LOGGER.warning("模型未加载或状态异常")

        return infer_result

    def _process_model_results(self, results):
        """处理不同类型的模型结果"""
        import pandas as pd

        try:
            # 从配置文件读取类别映射
            class_mapping = self._get_class_mapping_from_config()

            # 处理torch.hub加载的模型结果
            if hasattr(results, 'pandas'):
                detections = results.pandas().xyxy[0]
                # 添加中文名称
                if 'name' not in detections.columns:
                    detections['name'] = detections['class'].apply(lambda x: self._get_class_name(int(x), class_mapping))
                detections['ch_name'] = detections['name'].apply(lambda x: class_mapping.get('label_map', {}).get(x, x))
                return detections

            # 处理直接torch.load的模型结果
            elif hasattr(results, 'pred') and len(results.pred) > 0:
                pred = results.pred[0].cpu().numpy()
                if len(pred) > 0:
                    detections = pd.DataFrame(pred, columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class'])
                    # 添加英文和中文类别名称
                    detections['name'] = detections['class'].apply(lambda x: self._get_class_name(int(x), class_mapping))
                    detections['ch_name'] = detections['name'].apply(lambda x: class_mapping.get('label_map', {}).get(x, x))
                else:
                    detections = pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])
                return detections

            # 处理ultralytics YOLO模型结果
            elif hasattr(results, 'boxes'):
                if results.boxes is not None and len(results.boxes) > 0:
                    boxes = results.boxes.xyxy.cpu().numpy()
                    confs = results.boxes.conf.cpu().numpy()
                    classes = results.boxes.cls.cpu().numpy()

                    detections = pd.DataFrame({
                        'xmin': boxes[:, 0],
                        'ymin': boxes[:, 1],
                        'xmax': boxes[:, 2],
                        'ymax': boxes[:, 3],
                        'confidence': confs,
                        'class': classes
                    })

                    # 添加英文和中文类别名称
                    detections['name'] = detections['class'].apply(lambda x: self._get_class_name(int(x), class_mapping))
                    detections['ch_name'] = detections['name'].apply(lambda x: class_mapping.get('label_map', {}).get(x, x))
                else:
                    detections = pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])
                return detections

            else:
                # 未知模型类型
                LOGGER.warning(f"未知的模型结果类型: {type(results)}")
                return pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])

        except Exception as e:
            LOGGER.error(f"处理模型结果失败: {e}")
            return pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])

    def _get_class_mapping_from_config(self):
        """从配置文件读取类别映射"""
        try:
            import yaml
            import os

            # 读取后处理器配置文件
            config_path = os.path.join(os.path.dirname(__file__), '..', 'postprocessor', 'postprocessor.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                # 获取第一个模型的配置
                if 'model' in config:
                    for model_name, model_config in config['model'].items():
                        if 'label' in model_config:
                            return model_config['label']

            LOGGER.warning("未找到类别映射配置，使用默认配置")
            return {
                'class2label': {0: 'person'},
                'label_map': {'person': '人'}
            }

        except Exception as e:
            LOGGER.error(f"读取类别映射配置失败: {e}")
            return {
                'class2label': {0: 'person'},
                'label_map': {'person': '人'}
            }

    def _get_class_name(self, class_id, class_mapping):
        """根据类别ID获取类别名称"""
        class2label = class_mapping.get('class2label', {})
        return class2label.get(class_id, class2label.get(str(class_id), 'unknown'))
