<template>
  <div class="app-container">
    <div class="main-layout">
      <!-- 左侧主要内容 -->
      <div class="main-content" :class="{ 'with-config': showConfigPanel }">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流名称" prop="streamName">
        <el-input
          v-model="queryParams.streamName"
          placeholder="请输入流名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="RTSP" prop="rtspUrl">
        <el-input
          v-model="queryParams.rtspUrl"
          placeholder="请输入RTSP地址"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安装位置" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入安装位置"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option label="停用" value="0" />
          <el-option label="启用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否录制" prop="isRecording">
        <el-select v-model="queryParams.isRecording" placeholder="请选择是否录制" clearable style="width: 240px">
          <el-option label="否" :value="0" />
          <el-option label="是" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['stream_manage:stream:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['stream_manage:stream:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['stream_manage:stream:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['stream_manage:stream:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="streamList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="流名称" align="center" prop="streamName" />
      <el-table-column label="RTSP地址" align="center" prop="rtspUrl" />
      <el-table-column label="安装位置" align="center" prop="location" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
            {{ scope.row.status === '1' ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否录制" align="center" prop="isRecording">
        <template #default="scope">
          <el-tag :type="scope.row.isRecording === 1 ? 'success' : 'info'">
            {{ scope.row.isRecording === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="success" icon="VideoPlay" @click="handleTest(scope.row)" v-hasPermi="['stream_manage:stream:query']">测试</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['stream_manage:stream:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['stream_manage:stream:remove']">删除</el-button>
          <el-button link type="warning" icon="Setting" @click="handleConfig(scope.row)" v-hasPermi="['stream_manage:stream:edit']">算法配置</el-button>
        </template>
      </el-table-column>
    </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>

      <!-- 右侧算法配置面板 -->
      <div v-if="showConfigPanel" class="config-panel">
        <div class="config-header">
          <h3>算法配置</h3>
          <el-button
            type="text"
            icon="Close"
            @click="closeConfigPanel"
            class="close-btn"
          />
        </div>
        <div class="config-content">
          <AlgorithmConfig
            :stream-id="currentStreamId"
            :video-url="currentVideoUrl"
            @config-saved="onConfigSaved"
          />
        </div>
      </div>
    </div>

    <!-- 添加或修改视频流管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="streamRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item v-if="renderField(true, true)" label="名称" prop="streamName">
        <el-input v-model="form.streamName" placeholder="请输入视频流名称" />
      </el-form-item>
      <el-form-item v-if="renderField(true, true)" label="RTSP" prop="rtspUrl">
        <el-input v-model="form.rtspUrl" placeholder="请输入RTSP地址" />
      </el-form-item>
      <el-form-item v-if="renderField(true, true)" label="监控位置" prop="location">
        <el-input v-model="form.location" placeholder="请输入监控位置" />
      </el-form-item>
      <el-form-item v-if="renderField(true, true)" label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="0">停用</el-radio>
          <el-radio value="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="renderField(true, true)" label="是否录制" prop="isRecording">
        <el-radio-group v-model="form.isRecording">
          <el-radio :value="0">否</el-radio>
          <el-radio :value="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="renderField(true, true)" label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- RTSP测试预览弹窗 -->
    <el-dialog
      title="RTSP连接测试"
      v-model="testDialogVisible"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :before-close="handleTestDialogClose"
    >
      <div v-loading="testLoading" element-loading-text="正在测试连接...">
        <!-- 流信息 -->
        <el-descriptions title="视频流信息" :column="2" border>
          <el-descriptions-item label="流名称">{{ currentTestStream.streamName }}</el-descriptions-item>
          <el-descriptions-item label="RTSP地址">{{ currentTestStream.rtspUrl }}</el-descriptions-item>
          <el-descriptions-item label="安装位置">{{ currentTestStream.location || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentTestStream.status === '1' ? 'success' : 'danger'">
              {{ currentTestStream.status === '1' ? '启用' : '停用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 测试结果 -->
        <div style="margin-top: 20px;">
          <h4>测试结果</h4>
          <div v-if="testResult.success !== undefined">
            <el-alert
              :title="testResult.message"
              :type="testResult.success ? 'success' : 'error'"
              :closable="false"
              show-icon
            />

            <!-- 连接失败时显示解决建议 -->
            <div v-if="!testResult.success" style="margin-top: 15px;">
              <el-card shadow="never" style="background-color: #fdf6ec;">
                <template #header>
                  <span style="color: #e6a23c;">💡 解决建议</span>
                </template>
                <ul style="margin: 0; padding-left: 20px; color: #606266;">
                  <li>检查RTSP地址格式是否正确（如：rtsp://用户名:密码@IP:端口/路径）</li>
                  <li>确认摄像头设备是否正常工作且网络连接正常</li>
                  <li>验证用户名和密码是否正确</li>
                  <li>检查防火墙设置是否阻止了RTSP端口（通常是554）</li>
                  <li>尝试使用其他RTSP客户端工具验证地址可用性</li>
                </ul>
              </el-card>
            </div>

            <!-- 连接成功时显示详细信息 -->
            <div v-if="testResult.success && testResult.data" style="margin-top: 15px;">
              <el-descriptions title="视频流参数" :column="3" border size="small">
                <el-descriptions-item label="分辨率">{{ testResult.data.width }} × {{ testResult.data.height }}</el-descriptions-item>
                <el-descriptions-item label="帧率">{{ testResult.data.fps }} FPS</el-descriptions-item>
                <el-descriptions-item label="测试时间">{{ new Date().toLocaleString() }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-if="testResult.success && previewImage" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h4>实时预览</h4>
            <el-button
              type="primary"
              size="small"
              @click="refreshPreview"
              :loading="testLoading"
              icon="Refresh"
            >
              刷新画面
            </el-button>
          </div>
          <div style="text-align: center; border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px;">
            <img
              :src="previewImage"
              alt="视频预览"
              style="max-width: 100%; max-height: 400px; border-radius: 4px;"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeTestDialog">关 闭</el-button>
          <el-button type="primary" @click="performTest" :loading="testLoading">重新测试</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Stream">
import { listStream, getStream, delStream, addStream, updateStream, testRtspConnection, getRtspFrame } from "@/api/stream_manage/stream";
import AlgorithmConfig from "@/components/AlgorithmConfig/index.vue";

const { proxy } = getCurrentInstance();

const streamList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 测试相关的响应式数据
const testDialogVisible = ref(false);
const testLoading = ref(false);

// 算法配置相关的响应式数据
const showConfigPanel = ref(false);
const currentStreamId = ref(null);
const currentVideoUrl = ref('');
const testResult = ref({});
const currentTestStream = ref({});
const previewImage = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    streamName: null,
    rtspUrl: null,
    location: null,
    status: null,
    isRecording: null,
  },
  rules: {
    streamName: [
      { required: true, message: "流名称不能为空", trigger: "blur" }
    ],
    rtspUrl: [
      { required: true, message: "RTSP地址不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询视频流管理列表 */
function getList() {
  loading.value = true;
  listStream(queryParams.value).then(response => {
    streamList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    streamId: null,
    streamName: null,
    rtspUrl: null,
    location: null,
    status: "1",
    isRecording: 0,
    remark: null,
  };
  proxy.resetForm("streamRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.streamId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加视频流管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _streamId = row.streamId || ids.value;
  getStream(_streamId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改视频流管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["streamRef"].validate(valid => {
    if (valid) {
      if (form.value.streamId != null) {
        updateStream(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStream(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _streamIds = row.streamId || ids.value;
  let confirmMessage = '';

  if (row.streamId) {
    // 单个删除，显示流名称
    confirmMessage = `是否确认删除视频流"${row.streamName}"？`;
  } else {
    // 批量删除，显示数量
    const selectedStreams = streamList.value.filter(item => ids.value.includes(item.streamId));
    const streamNames = selectedStreams.map(item => item.streamName).join('、');
    if (selectedStreams.length === 1) {
      confirmMessage = `是否确认删除视频流"${streamNames}"？`;
    } else {
      confirmMessage = `是否确认删除选中的${selectedStreams.length}个视频流（${streamNames}）？`;
    }
  }

  proxy.$modal.confirm(confirmMessage).then(function() {
    return delStream(_streamIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}


/** 测试按钮操作 */
function handleTest(row) {
  currentTestStream.value = row;
  testResult.value = {};
  previewImage.value = '';
  testDialogVisible.value = true;

  // 开始测试
  performTest();
}

/** 执行RTSP测试 */
async function performTest() {
  testLoading.value = true;
  try {
    const testData = {
      rtspUrl: currentTestStream.value.rtspUrl,
      timeout: 10
    };

    const response = await testRtspConnection(testData);
    if (response.code === 200) {
      testResult.value = {
        success: true,
        message: response.msg,
        data: response.data
      };

      // 如果测试成功，获取预览图像
      if (response.data && response.data.frame) {
        previewImage.value = 'data:image/jpeg;base64,' + response.data.frame;
      }
    } else {
      testResult.value = {
        success: false,
        message: response.msg || '测试失败',
        data: null
      };
    }
  } catch (error) {
    // 提供友好的错误提示
    let friendlyMessage = '测试过程中发生错误';

    if (error.message) {
      const errorMsg = error.message.toLowerCase();
      if (errorMsg.includes('network') || errorMsg.includes('timeout')) {
        friendlyMessage = '网络连接超时，请检查网络连接或稍后重试';
      } else if (errorMsg.includes('500')) {
        friendlyMessage = '服务器内部错误，请联系管理员';
      } else if (errorMsg.includes('401') || errorMsg.includes('403')) {
        friendlyMessage = '权限不足，请重新登录后再试';
      } else {
        friendlyMessage = '测试失败，请检查RTSP地址是否正确';
      }
    }

    testResult.value = {
      success: false,
      message: friendlyMessage,
      data: null
    };
  } finally {
    testLoading.value = false;
  }
}

/** 刷新预览图像 */
async function refreshPreview() {
  if (!currentTestStream.value.rtspUrl) return;

  testLoading.value = true;
  try {
    const frameData = {
      rtspUrl: currentTestStream.value.rtspUrl,
      timeout: 5
    };

    const response = await getRtspFrame(frameData);
    if (response.code === 200 && response.data && response.data.frame) {
      previewImage.value = 'data:image/jpeg;base64,' + response.data.frame;
      proxy.$modal.msgSuccess('预览图像已刷新');
    } else {
      proxy.$modal.msgError(response.msg || '获取预览图像失败');
    }
  } catch (error) {
    // 提供友好的错误提示
    let friendlyMessage = '获取预览图像失败';

    if (error.message) {
      const errorMsg = error.message.toLowerCase();
      if (errorMsg.includes('network') || errorMsg.includes('timeout')) {
        friendlyMessage = '网络连接超时，请检查网络连接';
      } else if (errorMsg.includes('500')) {
        friendlyMessage = '服务器错误，请稍后重试';
      } else {
        friendlyMessage = '获取预览图像失败，请检查视频流状态';
      }
    }

    proxy.$modal.msgError(friendlyMessage);
  } finally {
    testLoading.value = false;
  }
}

/** 关闭测试对话框 */
function closeTestDialog() {
  console.log('=== 关闭测试对话框 ===')

  // 清理状态
  testLoading.value = false
  testResult.value = {}
  previewImage.value = ''
  currentTestStream.value = {}

  // 关闭对话框
  testDialogVisible.value = false

  console.log('测试对话框已关闭，状态已清理')
}

/** 处理测试对话框关闭事件 */
function handleTestDialogClose(done) {
  console.log('=== 处理测试对话框关闭事件 ===')

  // 如果正在加载，先停止加载状态
  if (testLoading.value) {
    console.log('停止正在进行的测试请求')
    testLoading.value = false
  }

  // 清理所有相关状态
  testResult.value = {}
  previewImage.value = ''
  currentTestStream.value = {}

  // 执行关闭
  done()

  console.log('测试对话框关闭处理完成')
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('stream_manage/stream/export', {
    ...queryParams.value
  }, `stream_${new Date().getTime()}.xlsx`);
}

/** 是否渲染字段 */
function renderField(insert, edit) {
  return form.value.streamId == null ? insert : edit;
}

/** 算法配置 */
function handleConfig(row) {
  currentStreamId.value = row.streamId;
  currentVideoUrl.value = row.rtspUrl;
  showConfigPanel.value = true;
}

/** 关闭配置面板 */
function closeConfigPanel() {
  showConfigPanel.value = false;
  currentStreamId.value = null;
  currentVideoUrl.value = '';
}

/** 配置保存成功回调 */
function onConfigSaved(configData) {
  proxy.$modal.msgSuccess('算法配置保存成功');
  // 这里可以添加其他逻辑，比如刷新列表等
}

getList();
</script>

<style scoped>
.app-container {
  padding: 20px;
  height: calc(100vh - 84px);
  overflow: hidden;
}
.main-layout {
  display: flex;
  height: 100%;
  gap: 16px;
  width: 100%;
  overflow: hidden;
}

.main-content {
  flex: 1;
  transition: all 0.3s ease;
  min-width: 0;
  overflow: auto;
}

.main-content.with-config {
  flex: 1;
  width: calc(100% - 416px); /* 400px + 16px gap */
}

.config-panel {
  width: 400px;
  flex-shrink: 0;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
  min-height: 600px;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #333;
  background: #2a2a2a;
  border-radius: 8px 8px 0 0;
}

.config-header h3 {
  margin: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  color: #999;
  padding: 4px;
}

.close-btn:hover {
  color: #fff;
  background: #333;
}

.config-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: 0; /* 配合 flex: 1 确保正确的滚动行为 */
  /* 美化滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #666 transparent;
}

/* Webkit 浏览器滚动条样式 */
.config-content::-webkit-scrollbar {
  width: 6px;
}

.config-content::-webkit-scrollbar-track {
  background: transparent;
}

.config-content::-webkit-scrollbar-thumb {
  background-color: #666;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.config-content::-webkit-scrollbar-thumb:hover {
  background-color: #888;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-layout {
    flex-direction: column;
    height: auto;
  }

  .main-content.with-config {
    flex: 1;
    width: 100%;
  }

  .config-panel {
    width: 100%;
    height: 400px;
    min-height: 400px;
    margin-top: 16px;
  }
}
</style>