<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑检测区域"
    width="80%"
    :before-close="handleClose"
    class="polygon-editor-dialog"
  >
    <div class="polygon-editor">
      <div class="editor-toolbar">
        <el-button
          type="success"
          @click="refreshFrame"
          :loading="loadingFrame"
          icon="Refresh"
        >
          {{ loadingFrame ? '获取中...' : '刷新画面' }}
        </el-button>
        <el-button
          type="primary"
          @click="startDrawing"
          :disabled="isDrawing"
          icon="Edit"
        >
          {{ isDrawing ? '绘制中...' : '开始绘制' }}
        </el-button>
        <el-button
          @click="finishDrawing"
          :disabled="!isDrawing || currentPolygon.length < 3"
          icon="Check"
        >
          完成绘制
        </el-button>
        <el-button
          @click="cancelDrawing"
          :disabled="!isDrawing"
          icon="Close"
        >
          取消绘制
        </el-button>
        <el-button
          type="danger"
          @click="clearAll"
          icon="Delete"
        >
          清空所有
        </el-button>
      </div>

      <div class="canvas-container">
        <canvas
          ref="canvasRef"
          @mousedown="onMouseDown"
          @mousemove="onMouseMove"
          @mouseup="onMouseUp"
          @dblclick="onDoubleClick"
        ></canvas>
        <video
          ref="videoRef"
          :src="videoUrl"
          @loadedmetadata="onVideoLoaded"
          style="display: none"
          muted
          autoplay
        />
      </div>

      <div class="polygon-list">
        <h4>已绘制区域 ({{ polygons.length }})</h4>
        <div v-if="polygons.length === 0" class="empty-list">
          暂无绘制区域
        </div>
        <div v-else class="list-items">
          <div 
            v-for="(polygon, index) in polygons"
            :key="index"
            class="polygon-item"
            :class="{ active: selectedPolygonIndex === index }"
            @click="selectPolygon(index)"
          >
            <span>区域 {{ index + 1 }} ({{ polygon.length }} 个点)</span>
            <el-button 
              type="danger" 
              size="small"
              @click.stop="removePolygon(index)"
              icon="Delete"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="savePolygons">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, Close, Delete, Refresh } from '@element-plus/icons-vue'
import { getRtspFrame } from '@/api/stream_manage/stream'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  polygons: {
    type: Array,
    default: () => []
  },
  videoUrl: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'update:polygons'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canvasRef = ref(null)
const videoRef = ref(null)
const isDrawing = ref(false)
const currentPolygon = ref([])
const localPolygons = ref([])
const selectedPolygonIndex = ref(-1)
const canvasSize = ref({ width: 800, height: 450 })

// 绘制状态
const isDragging = ref(false)
const dragPointIndex = ref(-1)
const dragPolygonIndex = ref(-1)

// 视频帧相关
const loadingFrame = ref(false)
const frameImage = ref(null)
const frameImageLoaded = ref(false)

// 方法
const initCanvas = () => {
  const canvas = canvasRef.value
  if (!canvas) return

  canvas.width = canvasSize.value.width
  canvas.height = canvasSize.value.height
  
  // 设置画布样式
  canvas.style.border = '1px solid #333'
  canvas.style.cursor = 'crosshair'
  
  drawCanvas()
}

const drawCanvas = () => {
  const canvas = canvasRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 绘制背景（如果有视频帧）
  drawVideoFrame(ctx)

  // 绘制已完成的多边形
  localPolygons.value.forEach((polygon, polygonIndex) => {
    drawPolygon(ctx, polygon, polygonIndex === selectedPolygonIndex.value)
  })

  // 绘制当前正在绘制的多边形
  if (isDrawing.value && currentPolygon.value.length > 0) {
    drawPolygon(ctx, currentPolygon.value, false, true)
  }
}

const drawVideoFrame = (ctx) => {
  if (frameImage.value && frameImageLoaded.value) {
    // 绘制视频帧
    ctx.drawImage(frameImage.value, 0, 0, canvasSize.value.width, canvasSize.value.height)
    // 添加半透明遮罩以便更好地看到绘制的多边形
    ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'
    ctx.fillRect(0, 0, canvasSize.value.width, canvasSize.value.height)
  } else {
    // 绘制默认背景
    ctx.fillStyle = '#2a2a2a'
    ctx.fillRect(0, 0, canvasSize.value.width, canvasSize.value.height)

    // 显示提示文字
    ctx.fillStyle = '#666'
    ctx.font = '16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('点击"刷新画面"获取视频帧', canvasSize.value.width / 2, canvasSize.value.height / 2)
  }
}

const drawPolygon = (ctx, points, isSelected = false, isDrawing = false) => {
  if (points.length < 2) return

  ctx.beginPath()
  ctx.moveTo(points[0].x, points[0].y)
  
  for (let i = 1; i < points.length; i++) {
    ctx.lineTo(points[i].x, points[i].y)
  }
  
  if (!isDrawing && points.length > 2) {
    ctx.closePath()
    ctx.fillStyle = isSelected ? 'rgba(64, 158, 255, 0.3)' : 'rgba(255, 255, 255, 0.2)'
    ctx.fill()
  }
  
  ctx.strokeStyle = isSelected ? '#409eff' : (isDrawing ? '#67c23a' : '#fff')
  ctx.lineWidth = isSelected ? 3 : 2
  ctx.stroke()

  // 绘制控制点
  points.forEach((point, index) => {
    ctx.beginPath()
    ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
    ctx.fillStyle = isSelected ? '#409eff' : (isDrawing ? '#67c23a' : '#fff')
    ctx.fill()
    ctx.strokeStyle = '#000'
    ctx.lineWidth = 1
    ctx.stroke()
  })
}

const getMousePos = (event) => {
  const canvas = canvasRef.value
  const rect = canvas.getBoundingClientRect()
  return {
    x: (event.clientX - rect.left) * (canvas.width / rect.width),
    y: (event.clientY - rect.top) * (canvas.height / rect.height)
  }
}

const startDrawing = () => {
  isDrawing.value = true
  currentPolygon.value = []
  selectedPolygonIndex.value = -1
  const canvas = canvasRef.value
  if (canvas) {
    canvas.style.cursor = 'crosshair'
  }
}

const finishDrawing = () => {
  if (currentPolygon.value.length >= 3) {
    localPolygons.value.push([...currentPolygon.value])
    currentPolygon.value = []
    isDrawing.value = false
    drawCanvas()
    ElMessage.success('区域绘制完成')
  }
}

const cancelDrawing = () => {
  currentPolygon.value = []
  isDrawing.value = false
  drawCanvas()
}

const clearAll = () => {
  localPolygons.value = []
  currentPolygon.value = []
  isDrawing.value = false
  selectedPolygonIndex.value = -1
  drawCanvas()
}

const onMouseDown = (event) => {
  const pos = getMousePos(event)
  
  if (isDrawing.value) {
    // 添加新点
    currentPolygon.value.push(pos)
    drawCanvas()
  } else {
    // 检查是否点击了控制点
    const { polygonIndex, pointIndex } = findNearestPoint(pos)
    if (polygonIndex !== -1 && pointIndex !== -1) {
      isDragging.value = true
      dragPolygonIndex.value = polygonIndex
      dragPointIndex.value = pointIndex
      selectedPolygonIndex.value = polygonIndex
    } else {
      selectedPolygonIndex.value = -1
    }
    drawCanvas()
  }
}

const onMouseMove = (event) => {
  if (isDragging.value && dragPolygonIndex.value !== -1 && dragPointIndex.value !== -1) {
    const pos = getMousePos(event)
    localPolygons.value[dragPolygonIndex.value][dragPointIndex.value] = pos
    drawCanvas()
  }
}

const onMouseUp = () => {
  isDragging.value = false
  dragPolygonIndex.value = -1
  dragPointIndex.value = -1
}

const onDoubleClick = () => {
  if (isDrawing.value && currentPolygon.value.length >= 3) {
    finishDrawing()
  }
}

const findNearestPoint = (pos) => {
  const threshold = 10
  
  for (let polygonIndex = 0; polygonIndex < localPolygons.value.length; polygonIndex++) {
    const polygon = localPolygons.value[polygonIndex]
    for (let pointIndex = 0; pointIndex < polygon.length; pointIndex++) {
      const point = polygon[pointIndex]
      const distance = Math.sqrt(
        Math.pow(pos.x - point.x, 2) + Math.pow(pos.y - point.y, 2)
      )
      if (distance <= threshold) {
        return { polygonIndex, pointIndex }
      }
    }
  }
  
  return { polygonIndex: -1, pointIndex: -1 }
}

const selectPolygon = (index) => {
  selectedPolygonIndex.value = index
  drawCanvas()
}

const removePolygon = (index) => {
  localPolygons.value.splice(index, 1)
  if (selectedPolygonIndex.value === index) {
    selectedPolygonIndex.value = -1
  } else if (selectedPolygonIndex.value > index) {
    selectedPolygonIndex.value--
  }
  drawCanvas()
}

const refreshFrame = async () => {
  if (!props.videoUrl) {
    ElMessage.warning('请先配置视频流地址')
    return
  }

  loadingFrame.value = true
  try {
    const frameData = {
      rtspUrl: props.videoUrl,
      timeout: 5
    }

    const response = await getRtspFrame(frameData)
    if (response.code === 200 && response.data && response.data.frame) {
      // 创建图片对象
      const img = new Image()
      img.onload = () => {
        frameImage.value = img
        frameImageLoaded.value = true
        drawCanvas()
        ElMessage.success('视频帧获取成功')
      }
      img.onerror = () => {
        ElMessage.error('视频帧加载失败')
      }
      img.src = 'data:image/jpeg;base64,' + response.data.frame
    } else {
      ElMessage.error(response.msg || '获取视频帧失败')
    }
  } catch (error) {
    console.error('获取视频帧失败:', error)
    ElMessage.error('获取视频帧失败，请检查视频流地址')
  } finally {
    loadingFrame.value = false
  }
}

const onVideoLoaded = () => {
  nextTick(() => {
    initCanvas()
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

const savePolygons = () => {
  // 转换坐标为相对坐标 (0-1)
  const normalizedPolygons = localPolygons.value.map(polygon =>
    polygon.map(point => ({
      x: point.x / canvasSize.value.width,
      y: point.y / canvasSize.value.height
    }))
  )

  emit('update:polygons', normalizedPolygons)
  dialogVisible.value = false

  // 更清楚的保存反馈
  if (normalizedPolygons.length > 0) {
    ElMessage.success(`区域配置已更新，共${normalizedPolygons.length}个区域。请点击"保存配置"完成最终保存。`)
  } else {
    ElMessage.info('已清空所有检测区域。请点击"保存配置"完成最终保存。')
  }
}

// 监听器
watch(dialogVisible, (visible) => {
  if (visible) {
    // 转换相对坐标为绝对坐标
    localPolygons.value = props.polygons.map(polygon =>
      polygon.map(point => ({
        x: point.x * canvasSize.value.width,
        y: point.y * canvasSize.value.height
      }))
    )

    // 重置视频帧状态
    frameImage.value = null
    frameImageLoaded.value = false

    nextTick(() => {
      initCanvas()
      // 自动获取一帧视频
      if (props.videoUrl) {
        refreshFrame()
      }
    })
  }
})
</script>

<style scoped>
.polygon-editor-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.polygon-editor {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.editor-toolbar {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.canvas-container {
  display: flex;
  justify-content: center;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.canvas-container canvas {
  border: 1px solid #333;
  cursor: crosshair;
  background: #2a2a2a;
}

.polygon-list h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
}

.empty-list {
  color: #999;
  text-align: center;
  padding: 20px;
}

.list-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.polygon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.polygon-item:hover {
  background: #f5f5f5;
}

.polygon-item.active {
  border-color: #409eff;
  background: #ecf5ff;
}

.dialog-footer {
  text-align: right;
}
</style>
