#!/usr/bin/env python3
"""
检测框绘制流程测试脚本
验证检测框的获取和绘制逻辑
"""

import cv2
import numpy as np
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class DetectionDrawingFlowTester:
    """检测框绘制流程测试器"""
    
    def create_test_frame(self, width=800, height=600):
        """创建测试帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(64 + 64 * np.sin(x * 0.01)),
                    int(64 + 64 * np.sin(y * 0.01)),
                    int(64 + 64 * np.sin((x + y) * 0.005))
                ]
        
        # 添加一些模拟的场景元素
        cv2.rectangle(frame, (50, 50), (150, 150), (100, 100, 100), -1)  # 建筑物
        cv2.rectangle(frame, (600, 400), (750, 550), (80, 80, 80), -1)   # 另一个建筑物
        cv2.circle(frame, (400, 300), 30, (0, 100, 0), -1)              # 树木
        
        return frame
    
    def create_original_detections(self):
        """模拟AI算法包的原始检测结果（包含所有检测目标）"""
        return [
            {'bbox': [200, 200, 280, 350], 'confidence': 0.85, 'class_id': 0, 'label': 'person'},
            {'bbox': [350, 180, 420, 320], 'confidence': 0.72, 'class_id': 0, 'label': 'person'},
            {'bbox': [500, 250, 570, 380], 'confidence': 0.68, 'class_id': 0, 'label': 'person'},
            {'bbox': [100, 400, 180, 550], 'confidence': 0.91, 'class_id': 0, 'label': 'person'},
            {'bbox': [600, 300, 680, 450], 'confidence': 0.76, 'class_id': 0, 'label': 'person'},
        ]
    
    def create_alert_result_with_targets(self):
        """创建包含告警目标的结果（经过后处理判断）"""
        return {
            'hit': True,
            'message': '检测到2个人员入侵禁区',
            'data': {
                'bbox': {
                    'rectangles': [[200, 200, 280, 350], [100, 400, 180, 550]],
                    'polygons': {}
                }
            },
            'details': {
                'alert_targets': [
                    {
                        'bbox': [200, 200, 280, 350],
                        'confidence': 0.85,
                        'alert_type': 'area_intrusion',
                        'alert_reason': '进入禁区A'
                    },
                    {
                        'bbox': [100, 400, 180, 550],
                        'confidence': 0.91,
                        'alert_type': 'area_intrusion',
                        'alert_reason': '进入禁区B'
                    }
                ],
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '禁区A',
                        'points': [
                            {'x': 150, 'y': 150},
                            {'x': 350, 'y': 150},
                            {'x': 350, 'y': 400},
                            {'x': 150, 'y': 400}
                        ]
                    },
                    {
                        'id': 'area_2',
                        'name': '禁区B',
                        'points': [
                            {'x': 50, 'y': 350},
                            {'x': 250, 'y': 350},
                            {'x': 250, 'y': 580},
                            {'x': 50, 'y': 580}
                        ]
                    }
                ],
                'configured_lines': [
                    {
                        'id': 'line_1',
                        'name': '警戒线',
                        'start_point': {'x': 400, 'y': 0},
                        'end_point': {'x': 400, 'y': 600}
                    }
                ],
                'image_size': {'width': 800, 'height': 600}
            }
        }
    
    def create_no_alert_result(self):
        """创建无告警结果（有检测但无告警）"""
        return {
            'hit': False,
            'message': '检测到目标但无告警',
            'data': {},
            'details': {
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '监控区域',
                        'points': [
                            {'x': 300, 'y': 200},
                            {'x': 600, 'y': 200},
                            {'x': 600, 'y': 500},
                            {'x': 300, 'y': 500}
                        ]
                    }
                ],
                'configured_lines': [
                    {
                        'id': 'line_1',
                        'name': '计数线',
                        'start_point': {'x': 250, 'y': 0},
                        'end_point': {'x': 250, 'y': 600}
                    }
                ],
                'image_size': {'width': 800, 'height': 600}
            }
        }
    
    def test_alert_target_drawing(self):
        """测试告警目标绘制"""
        print("=" * 60)
        print("测试告警目标绘制（只绘制告警目标，不绘制所有检测结果）")
        print("=" * 60)
        
        try:
            # 创建测试数据
            frame = self.create_test_frame()
            original_detections = self.create_original_detections()
            alert_result = self.create_alert_result_with_targets()
            
            print(f"✅ 模拟数据创建完成")
            print(f"  - 原始检测结果: {len(original_detections)}个目标")
            print(f"  - 告警目标: {len(alert_result['details']['alert_targets'])}个")
            print(f"  - 配置区域: {len(alert_result['details']['configured_areas'])}个")
            print(f"  - 配置线段: {len(alert_result['details']['configured_lines'])}个")
            
            # 保存原始帧（无任何绘制）
            cv2.imwrite("test_original_frame.jpg", frame)
            print(f"📸 保存原始帧: test_original_frame.jpg")
            
            # 绘制所有原始检测结果（错误示例）
            wrong_frame = frame.copy()
            for detection in original_detections:
                bbox = detection['bbox']
                x1, y1, x2, y2 = bbox
                cv2.rectangle(wrong_frame, (x1, y1), (x2, y2), (255, 0, 0), 2)  # 蓝色框
                cv2.putText(wrong_frame, f"{detection['label']} {detection['confidence']:.2f}", 
                           (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            cv2.putText(wrong_frame, "WRONG: All Detections", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            cv2.imwrite("test_wrong_all_detections.jpg", wrong_frame)
            print(f"📸 保存错误示例（绘制所有检测）: test_wrong_all_detections.jpg")
            
            # 正确绘制（只绘制告警目标）
            correct_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(correct_frame, alert_result)
            cv2.putText(correct_frame, "CORRECT: Alert Targets Only", (10, 570), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.imwrite("test_correct_alert_targets.jpg", correct_frame)
            print(f"📸 保存正确示例（只绘制告警目标）: test_correct_alert_targets.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 告警目标绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_no_alert_drawing(self):
        """测试无告警状态绘制"""
        print("\n" + "=" * 60)
        print("测试无告警状态绘制（显示配置区域但无检测框）")
        print("=" * 60)
        
        try:
            frame = self.create_test_frame()
            original_detections = self.create_original_detections()
            no_alert_result = self.create_no_alert_result()
            
            print(f"✅ 无告警数据创建完成")
            print(f"  - 原始检测结果: {len(original_detections)}个目标")
            print(f"  - 告警状态: {no_alert_result['hit']}")
            print(f"  - 配置区域: {len(no_alert_result['details']['configured_areas'])}个")
            
            # 绘制无告警状态
            no_alert_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(no_alert_frame, no_alert_result)
            cv2.putText(no_alert_frame, "No Alert - Config Areas Only", (10, 570), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.imwrite("test_no_alert_monitoring.jpg", no_alert_frame)
            print(f"📸 保存无告警监控状态: test_no_alert_monitoring.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 无告警绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_realtime_monitoring_flow(self):
        """测试实时监控流程"""
        print("\n" + "=" * 60)
        print("测试实时监控流程（模拟连续帧处理）")
        print("=" * 60)
        
        try:
            # 模拟连续的监控帧
            scenarios = [
                ("无告警", self.create_no_alert_result()),
                ("有告警", self.create_alert_result_with_targets()),
                ("无告警", self.create_no_alert_result()),
            ]
            
            for i, (scenario_name, alert_result) in enumerate(scenarios):
                frame = self.create_test_frame()
                
                # 添加帧序号
                cv2.putText(frame, f"Frame {i+1}", (650, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                # 绘制监控内容
                TaskExecutionService._draw_detection_boxes(frame, alert_result)
                
                # 添加场景标识
                cv2.putText(frame, f"Scenario: {scenario_name}", (10, 570), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
                
                # 保存帧
                filename = f"test_realtime_frame_{i+1}_{scenario_name.replace(' ', '_')}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 保存实时监控帧{i+1}: {filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ 实时监控流程测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_different_alert_types(self):
        """测试不同告警类型的绘制"""
        print("\n" + "=" * 60)
        print("测试不同告警类型的绘制（颜色区分）")
        print("=" * 60)
        
        try:
            frame = self.create_test_frame()
            
            # 创建包含多种告警类型的结果
            mixed_alert_result = {
                'hit': True,
                'message': '检测到多种类型告警',
                'details': {
                    'alert_targets': [
                        {
                            'bbox': [100, 100, 180, 200],
                            'confidence': 0.85,
                            'alert_type': 'area_intrusion',
                            'alert_reason': '区域入侵'
                        },
                        {
                            'bbox': [300, 150, 380, 250],
                            'confidence': 0.78,
                            'alert_type': 'line_crossing',
                            'alert_reason': '越线行为'
                        },
                        {
                            'bbox': [500, 200, 580, 300],
                            'confidence': 0.92,
                            'alert_type': 'vehicle_counting',
                            'alert_reason': '车辆计数'
                        }
                    ],
                    'configured_areas': [
                        {
                            'id': 'area_1',
                            'name': '测试区域',
                            'points': [
                                {'x': 50, 'y': 50},
                                {'x': 250, 'y': 50},
                                {'x': 250, 'y': 250},
                                {'x': 50, 'y': 250}
                            ]
                        }
                    ],
                    'configured_lines': [
                        {
                            'id': 'line_1',
                            'name': '测试线段',
                            'start_point': {'x': 280, 'y': 0},
                            'end_point': {'x': 280, 'y': 600}
                        }
                    ],
                    'image_size': {'width': 800, 'height': 600}
                }
            }
            
            # 绘制不同类型的告警
            mixed_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(mixed_frame, mixed_alert_result)
            
            # 添加图例
            cv2.putText(mixed_frame, "Red: Area Intrusion", (10, 520), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            cv2.putText(mixed_frame, "Yellow: Line Crossing", (10, 545), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(mixed_frame, "Purple: Vehicle Counting", (10, 570), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            
            cv2.imwrite("test_different_alert_types.jpg", mixed_frame)
            print(f"📸 保存不同告警类型示例: test_different_alert_types.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 不同告警类型测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主测试函数"""
    print("🎯 检测框绘制流程测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = DetectionDrawingFlowTester()
    
    # 运行测试
    tests = [
        ("告警目标绘制", tester.test_alert_target_drawing),
        ("无告警状态绘制", tester.test_no_alert_drawing),
        ("实时监控流程", tester.test_realtime_monitoring_flow),
        ("不同告警类型", tester.test_different_alert_types)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🏁 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 检测框绘制流程正确")
        print("\n📋 关键确认:")
        print("  ✅ 检测框来源：AI算法包 → 后处理判断 → 告警目标")
        print("  ✅ 绘制原则：只绘制告警目标，不绘制所有检测结果")
        print("  ✅ 实时监控：始终显示配置区域和线段")
        print("  ✅ 告警截图：保存带告警目标的图片")
        print("  ✅ 颜色区分：不同告警类型使用不同颜色")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
