import os
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_alert.dao.alert_dao import AlertDao
from module_alert.entity.vo.alert_vo import DeleteAlertModel, AlertModel, AlertPageQueryModel, AlertStatusUpdateModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.log_util import logger


class AlertService:
    """
    警告记录模块服务层
    """

    @classmethod
    async def get_alert_list_services(
        cls, query_db: AsyncSession, query_object: AlertPageQueryModel, is_page: bool = False
    ):
        """
        获取警告记录列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 警告记录列表信息对象
        """
        alert_list_result = await <PERSON><PERSON><PERSON>ao.get_alert_list(query_db, query_object, is_page)

        return alert_list_result


    @classmethod
    async def add_alert_services(cls, query_db: AsyncSession, page_object: AlertModel):
        """
        新增警告记录信息service

        :param query_db: orm对象
        :param page_object: 新增警告记录对象
        :return: 新增警告记录校验结果
        """
        try:
            await AlertDao.add_alert_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_alert_services(cls, query_db: AsyncSession, page_object: AlertModel):
        """
        编辑警告记录信息service

        :param query_db: orm对象
        :param page_object: 编辑警告记录对象
        :return: 编辑警告记录校验结果
        """
        edit_alert = page_object.model_dump(exclude_unset=True, exclude={'del_flag', 'create_time'})
        alert_info = await cls.alert_detail_services(query_db, page_object.alert_id)
        if alert_info.alert_id is not None and alert_info.alert_id > 0:
            try:
                await AlertDao.edit_alert_dao(query_db, edit_alert)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='警告记录不存在')

    @classmethod
    async def delete_alert_services(cls, query_db: AsyncSession, page_object: DeleteAlertModel, user_id: int = None):
        """
        删除警告记录信息service

        :param query_db: orm对象
        :param page_object: 删除警告记录对象
        :param user_id: 用户ID（用于权限过滤）
        :return: 删除警告记录校验结果
        """
        if page_object.alert_ids:
            alert_id_list = page_object.alert_ids.split(',')
            try:
                deleted_count = 0
                deleted_files_count = 0

                for alert_id in alert_id_list:
                    # 直接从数据库获取告警记录实体对象
                    alert_entity = await AlertDao.get_alert_detail_by_id(query_db, int(alert_id), user_id)
                    logger.info(f"检查告警记录 {alert_id}: 实体对象 = {alert_entity is not None}")

                    if alert_entity and alert_entity.alert_id:
                        # 在删除数据库记录之前，先尝试删除对应的截图文件
                        if alert_entity.screenshot_path:
                            file_deleted = await cls._delete_alert_screenshot(alert_entity.screenshot_path)
                            if file_deleted:
                                deleted_files_count += 1
                                logger.info(f"成功删除告警截图: {alert_entity.screenshot_path}")
                            else:
                                logger.warning(f"删除告警截图失败: {alert_entity.screenshot_path}")
                        else:
                            logger.debug(f"告警记录 {alert_id} 没有截图路径信息")

                        # 删除数据库记录（转换为AlertModel对象）
                        alert_dict = CamelCaseUtil.transform_result(alert_entity)
                        alert_model = AlertModel(**alert_dict)
                        success = await AlertDao.delete_alert_dao(query_db, alert_model)
                        if success:
                            deleted_count += 1
                            logger.info(f"成功删除告警记录 {alert_id}")
                        else:
                            logger.warning(f"删除告警记录 {alert_id} 失败")
                    else:
                        logger.warning(f"告警记录 {alert_id} 不存在或无权限访问")

                await query_db.commit()

                if deleted_count > 0:
                    message = f'成功删除{deleted_count}条记录'
                    if deleted_files_count > 0:
                        message += f'，删除{deleted_files_count}个截图文件'
                    return CrudResponseModel(is_success=True, message=message)
                else:
                    raise ServiceException(message='没有找到要删除的记录')

            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入警告ID为空')

    @classmethod
    async def _delete_alert_screenshot(cls, screenshot_path: str) -> bool:
        """
        删除告警截图文件

        :param screenshot_path: 截图文件路径
        :return: 删除是否成功
        """
        try:
            if not screenshot_path:
                return False

            # 处理不同的路径格式
            file_path = None

            # 如果是新的URL格式（/uploads/alerts/YYYYMMDD/filename.jpg）
            if screenshot_path.startswith('/uploads/'):
                # 移除前缀，获取相对路径
                relative_path = screenshot_path[9:]  # 移除 '/uploads/'
                file_path = Path("uploads") / relative_path

            # 如果是相对路径格式（alerts/YYYYMMDD/filename.jpg）
            elif screenshot_path.startswith('alerts/'):
                # 直接使用 uploads 目录作为基础路径
                file_path = Path("uploads") / screenshot_path

            # 如果是URL格式（以/profile/开头），转换为实际文件路径
            elif screenshot_path.startswith('/profile/'):
                # 移除前缀，获取相对路径
                relative_path = screenshot_path[9:]  # 移除 '/profile/'

                # 尝试从配置中获取上传路径
                try:
                    from config.env import UploadConfig
                    file_path = Path(UploadConfig.UPLOAD_PATH) / relative_path
                except ImportError:
                    # 如果无法导入配置，使用默认路径
                    file_path = Path("vf_admin/upload_path") / relative_path

            # 如果是绝对路径
            elif os.path.isabs(screenshot_path):
                file_path = Path(screenshot_path)

            # 其他情况，尝试作为相对路径处理（基于uploads目录）
            else:
                file_path = Path("uploads") / screenshot_path

            # 检查文件是否存在并删除
            if file_path and file_path.exists() and file_path.is_file():
                file_path.unlink()
                logger.info(f"成功删除截图文件: {file_path}")
                return True
            else:
                logger.warning(f"截图文件不存在或不是文件: {file_path}")
                return False

        except Exception as e:
            logger.error(f"删除截图文件失败: {screenshot_path}, 错误: {e}")
            return False

    @classmethod
    async def update_alert_status_services(
        cls,
        query_db: AsyncSession,
        status_update: AlertStatusUpdateModel,
        handle_user: str
    ):
        """
        更新告警处理状态service

        :param query_db: orm对象
        :param status_update: 状态更新对象
        :param handle_user: 处理人
        :return: 更新结果
        """
        # 验证告警是否存在
        alert_info = await cls.alert_detail_services(query_db, status_update.alert_id)
        if alert_info.alert_id is None or alert_info.alert_id <= 0:
            raise ServiceException(message='告警记录不存在')

        # 验证状态值
        valid_statuses = ['0', '1', '2']  # 0-未处理，1-已处理，2-已忽略
        if status_update.status not in valid_statuses:
            raise ServiceException(message='无效的状态值')

        try:
            # 更新告警状态
            from module_stream.dao.alert_dao import AlertDao as StreamAlertDao
            success = await StreamAlertDao.update_alert_status(
                query_db,
                status_update.alert_id,
                status_update.status,
                handle_user,
                status_update.handle_remark
            )

            if success:
                return CrudResponseModel(is_success=True, message='状态更新成功')
            else:
                return CrudResponseModel(is_success=False, message='状态更新失败')

        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def alert_detail_services(cls, query_db: AsyncSession, alert_id: int, user_id: int = None):
        """
        获取警告记录详细信息service

        :param query_db: orm对象
        :param alert_id: 警告ID
        :param user_id: 用户ID（用于权限过滤）
        :return: 警告ID对应的信息
        """
        alert = await AlertDao.get_alert_detail_by_id(query_db, alert_id=alert_id, user_id=user_id)
        if alert:
            result = AlertModel(**CamelCaseUtil.transform_result(alert))
        else:
            result = AlertModel(**dict())

        return result

    @staticmethod
    async def export_alert_list_services(alert_list: List):
        """
        导出警告记录信息service

        :param alert_list: 警告记录信息列表
        :return: 警告记录信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'alertId': '警告ID',
            'taskId': '任务ID',
            'streamId': '视频流ID',
            'modelId': '模型ID',
            'algorithmType': '算法类型',
            'alertType': '警告类型',
            'alertLevel': '警告等级(low/medium/high)',
            'alertMessage': '警告消息',
            'alertData': '警告数据',
            'frameData': '帧数据',
            'detectionData': '检测结果数据',
            'imagePath': '警告图片路径',
            'videoPath': '警告视频路径',
            'isHandled': '是否已处理',
            'handleBy': '处理人',
            'handleTime': '处理时间',
            'handleRemark': '处理备注',
            'alertTime': '警告时间',
            'delFlag': '删除标志',
            'createTime': '创建时间',
        }
        binary_data = ExcelUtil.export_list2excel(alert_list, mapping_dict)

        return binary_data
