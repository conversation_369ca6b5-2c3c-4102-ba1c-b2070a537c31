#!/usr/bin/env python3
"""
检测框绘制问题诊断脚本
分析检测框绘制过程中的数据结构和字段匹配问题
"""

import cv2
import json
import numpy as np
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class DetectionBoxDebugger:
    """检测框绘制问题诊断器"""
    
    def create_test_frame(self, width=640, height=480):
        """创建测试帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [64, 64, 64]
        
        # 添加一些参考元素
        cv2.rectangle(frame, (50, 50), (150, 150), (100, 100, 100), -1)
        cv2.putText(frame, "Debug Frame", (200, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return frame
    
    def create_real_world_alert_result(self):
        """创建真实世界的告警结果（模拟实际算法输出）"""
        return {
            'hit': True,
            'message': '检测到 2 个告警目标',
            'details': {
                'alert_targets': [
                    {
                        'box': [100, 100, 200, 200],  # 注意：使用 'box' 而不是 'bbox'
                        'score': 0.85,
                        'label': 'vehicle'
                    },
                    {
                        'box': [300, 150, 400, 250],  # 注意：使用 'box' 而不是 'bbox'
                        'score': 0.72,
                        'label': 'vehicle'
                    }
                ],
                'total_detections': 5,
                'algorithm_result': {
                    'hit': True,
                    'message': '车辆计数告警',
                    'details': {
                        'detections': [
                            {'xyxy': [100, 100, 200, 200], 'conf': 0.85, 'color': [255, 0, 0]},
                            {'xyxy': [300, 150, 400, 250], 'conf': 0.72, 'color': [255, 0, 0]},
                            {'xyxy': [500, 200, 600, 300], 'conf': 0.65, 'color': [0, 255, 0]},
                        ]
                    }
                }
            }
        }
    
    def create_standard_alert_result(self):
        """创建标准格式的告警结果"""
        return {
            'hit': True,
            'message': '检测到区域入侵',
            'data': {
                'bbox': {
                    'rectangles': [[150, 150, 250, 250]],
                    'polygons': {}
                }
            },
            'details': {
                'alert_targets': [
                    {
                        'bbox': [150, 150, 250, 250],  # 使用 'bbox'
                        'confidence': 0.89,
                        'alert_type': 'area_intrusion',
                        'alert_reason': '进入禁区'
                    }
                ],
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '禁区',
                        'points': [
                            {'x': 100, 'y': 100},
                            {'x': 300, 'y': 100},
                            {'x': 300, 'y': 300},
                            {'x': 100, 'y': 300}
                        ]
                    }
                ],
                'configured_lines': [],
                'image_size': {'width': 640, 'height': 480}
            }
        }
    
    def analyze_alert_result_structure(self, alert_result, name):
        """分析告警结果的数据结构"""
        print(f"\n{'='*60}")
        print(f"分析告警结果结构: {name}")
        print(f"{'='*60}")
        
        try:
            print(f"✅ 告警结果基本信息:")
            print(f"  - hit: {alert_result.get('hit')}")
            print(f"  - message: {alert_result.get('message')}")
            print(f"  - 顶级键: {list(alert_result.keys())}")
            
            details = alert_result.get('details', {})
            print(f"\n✅ details 结构:")
            print(f"  - details 键: {list(details.keys())}")
            
            # 分析告警目标
            alert_targets = details.get('alert_targets', [])
            print(f"\n✅ alert_targets 分析:")
            print(f"  - 数量: {len(alert_targets)}")
            
            for i, target in enumerate(alert_targets):
                print(f"  - 目标 {i+1}:")
                print(f"    - 键: {list(target.keys())}")
                
                # 检查检测框字段
                bbox_fields = []
                for field in ['bbox', 'box', 'xyxy']:
                    if field in target and target[field]:
                        bbox_fields.append((field, target[field]))
                
                if bbox_fields:
                    print(f"    - 检测框字段: {bbox_fields}")
                else:
                    print(f"    - ❌ 没有找到检测框字段")
                
                # 检查其他重要字段
                for field in ['confidence', 'score', 'conf', 'alert_type', 'alert_reason']:
                    if field in target:
                        print(f"    - {field}: {target[field]}")
            
            # 分析其他检测结果
            other_detections = []
            for field in ['in_area_detections', 'line_crossing_detections']:
                if field in details and details[field]:
                    other_detections.append((field, details[field]))
            
            if other_detections:
                print(f"\n✅ 其他检测结果:")
                for field_name, detections in other_detections:
                    print(f"  - {field_name}: {len(detections)}个")
                    for i, det in enumerate(detections[:2]):  # 只显示前2个
                        print(f"    - 检测 {i+1}: {list(det.keys())}")
            
            # 分析 data.bbox
            data = alert_result.get('data', {})
            if data:
                print(f"\n✅ data 结构:")
                print(f"  - data 键: {list(data.keys())}")
                bbox_data = data.get('bbox', {})
                if bbox_data:
                    print(f"  - bbox 键: {list(bbox_data.keys())}")
                    rectangles = bbox_data.get('rectangles', [])
                    if rectangles:
                        print(f"  - rectangles: {len(rectangles)}个 - {rectangles}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析告警结果结构失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_drawing_with_different_formats(self):
        """测试不同格式的绘制"""
        print(f"\n{'='*60}")
        print("测试不同格式的检测框绘制")
        print(f"{'='*60}")
        
        try:
            frame = self.create_test_frame()
            
            # 测试真实世界格式
            real_alert = self.create_real_world_alert_result()
            real_frame = frame.copy()
            
            print(f"\n🧪 测试真实世界格式绘制:")
            TaskExecutionService._draw_detection_boxes(real_frame, real_alert)
            cv2.imwrite("debug_real_world_format.jpg", real_frame)
            print(f"📸 保存真实世界格式结果: debug_real_world_format.jpg")
            
            # 测试标准格式
            standard_alert = self.create_standard_alert_result()
            standard_frame = frame.copy()
            
            print(f"\n🧪 测试标准格式绘制:")
            TaskExecutionService._draw_detection_boxes(standard_frame, standard_alert)
            cv2.imwrite("debug_standard_format.jpg", standard_frame)
            print(f"📸 保存标准格式结果: debug_standard_format.jpg")
            
            # 手动检查绘制结果
            print(f"\n🔍 手动验证绘制结果:")
            
            # 检查真实世界格式的检测框
            real_targets = real_alert['details']['alert_targets']
            print(f"真实格式告警目标数量: {len(real_targets)}")
            for i, target in enumerate(real_targets):
                box = target.get('box', [])
                if len(box) >= 4:
                    print(f"  目标{i+1}: box={box} - 应该绘制检测框")
                else:
                    print(f"  目标{i+1}: 无效的box={box} - 不会绘制检测框")
            
            # 检查标准格式的检测框
            standard_targets = standard_alert['details']['alert_targets']
            print(f"标准格式告警目标数量: {len(standard_targets)}")
            for i, target in enumerate(standard_targets):
                bbox = target.get('bbox', [])
                if len(bbox) >= 4:
                    print(f"  目标{i+1}: bbox={bbox} - 应该绘制检测框")
                else:
                    print(f"  目标{i+1}: 无效的bbox={bbox} - 不会绘制检测框")
            
            return True
            
        except Exception as e:
            print(f"❌ 不同格式绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_field_compatibility(self):
        """测试字段兼容性"""
        print(f"\n{'='*60}")
        print("测试字段兼容性")
        print(f"{'='*60}")
        
        try:
            # 测试不同的检测框字段名
            test_targets = [
                {'bbox': [100, 100, 200, 200], 'confidence': 0.9, 'name': 'bbox格式'},
                {'box': [300, 100, 400, 200], 'score': 0.8, 'name': 'box格式'},
                {'xyxy': [500, 100, 600, 200], 'conf': 0.7, 'name': 'xyxy格式'},
                {'invalid_field': [700, 100, 800, 200], 'confidence': 0.6, 'name': '无效格式'},
            ]
            
            frame = self.create_test_frame()
            
            print(f"🧪 测试字段兼容性:")
            for i, target in enumerate(test_targets):
                print(f"\n目标 {i+1}: {target['name']}")
                
                # 模拟 _draw_single_alert_target 中的字段获取逻辑
                bbox = target.get('bbox', target.get('box', target.get('xyxy', [])))
                print(f"  - 原始数据: {target}")
                print(f"  - 提取的bbox: {bbox}")
                print(f"  - 是否有效: {len(bbox) >= 4}")
                
                if len(bbox) >= 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(frame, target['name'], (x1, y1-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                    print(f"  ✅ 绘制成功: ({x1}, {y1}) -> ({x2}, {y2})")
                else:
                    print(f"  ❌ 无法绘制: 无效的检测框数据")
            
            cv2.imwrite("debug_field_compatibility.jpg", frame)
            print(f"\n📸 保存字段兼容性测试结果: debug_field_compatibility.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 字段兼容性测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主诊断函数"""
    print("🔍 检测框绘制问题诊断开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    debugger = DetectionBoxDebugger()
    
    # 创建测试数据
    real_alert = debugger.create_real_world_alert_result()
    standard_alert = debugger.create_standard_alert_result()
    
    # 运行诊断
    tests = [
        ("真实世界告警结果结构", lambda: debugger.analyze_alert_result_structure(real_alert, "真实世界格式")),
        ("标准告警结果结构", lambda: debugger.analyze_alert_result_structure(standard_alert, "标准格式")),
        ("不同格式绘制测试", debugger.test_drawing_with_different_formats),
        ("字段兼容性测试", debugger.test_field_compatibility)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("🏁 诊断结果汇总")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项诊断通过")
    
    # 问题分析和建议
    print(f"\n🔧 问题分析和建议:")
    
    if passed < total:
        print("❌ 发现问题，可能的原因:")
        print("  1. 检测框字段名不匹配 (bbox vs box vs xyxy)")
        print("  2. 告警目标数据结构不正确")
        print("  3. 绘制方法中的字段访问逻辑有误")
        print("  4. 告警结果的数据格式与预期不符")
    else:
        print("✅ 所有诊断通过，检测框绘制逻辑正常")
    
    print(f"\n📋 建议检查:")
    print("  1. 查看生成的调试图片文件")
    print("  2. 检查实际算法输出的数据格式")
    print("  3. 确认告警目标的字段名是否正确")
    print("  4. 验证绘制方法的字段兼容性")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 诊断被用户中断")
        sys.exit(0)
