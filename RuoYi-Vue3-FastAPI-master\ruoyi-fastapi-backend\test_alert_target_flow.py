#!/usr/bin/env python3
"""
告警目标完整流程测试
测试从算法包输出到绘制识别框的完整流程
"""

import cv2
import numpy as np
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class AlertTargetFlowTester:
    """告警目标完整流程测试器"""
    
    def create_test_frame(self, width=800, height=600):
        """创建测试帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(64 + 64 * np.sin(x * 0.005)),
                    int(64 + 64 * np.sin(y * 0.005)),
                    64
                ]
        
        # 添加标题
        cv2.putText(frame, "Alert Target Flow Test", (width//4, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
        
        return frame
    
    def create_algorithm_config(self):
        """创建算法配置"""
        return {
            'algorithm_config': {
                'areas': [
                    {
                        'id': 'area_1',
                        'name': '监控区域',
                        'points': [
                            {'x': 100, 'y': 100},
                            {'x': 400, 'y': 100},
                            {'x': 400, 'y': 300},
                            {'x': 100, 'y': 300}
                        ]
                    }
                ],
                'lines': [
                    {
                        'id': 'line_1',
                        'name': '警戒线',
                        'points': [
                            {'x': 500, 'y': 0},
                            {'x': 500, 'y': 600}
                        ]
                    }
                ]
            }
        }
    
    def create_mock_postprocess_result_with_color(self):
        """创建有颜色字段的算法包输出"""
        return {
            'hit': True,
            'message': '检测到车辆违规',
            'details': {
                'detections': [
                    {
                        'xyxy': [150, 150, 250, 220],
                        'conf': 0.89,
                        'color': [255, 0, 0],  # 红色 - 告警
                        'class_id': 0,
                        'label': 'car'
                    },
                    {
                        'xyxy': [520, 200, 620, 280],
                        'conf': 0.76,
                        'color': [255, 0, 0],  # 红色 - 告警
                        'class_id': 0,
                        'label': 'truck'
                    },
                    {
                        'xyxy': [300, 400, 380, 480],
                        'conf': 0.82,
                        'color': [0, 255, 0],  # 绿色 - 正常
                        'class_id': 0,
                        'label': 'car'
                    }
                ]
            }
        }
    
    def create_mock_postprocess_result_no_color(self):
        """创建无颜色字段的算法包输出"""
        return {
            'hit': True,
            'message': '检测到车辆',
            'details': {
                'detections': [
                    {
                        'xyxy': [150, 150, 250, 220],
                        'conf': 0.89,
                        'class_id': 0,
                        'label': 'car'
                        # 注意：没有color字段
                    },
                    {
                        'xyxy': [520, 200, 620, 280],
                        'conf': 0.76,
                        'class_id': 0,
                        'label': 'truck'
                        # 注意：没有color字段
                    }
                ]
            }
        }
    
    def test_complete_flow_with_color(self):
        """测试有颜色字段的完整流程"""
        print(f"\n{'='*60}")
        print("测试有颜色字段的完整流程")
        print(f"{'='*60}")
        
        try:
            # 1. 创建测试数据
            frame = self.create_test_frame()
            algorithm_config = self.create_algorithm_config()
            postprocess_result = self.create_mock_postprocess_result_with_color()
            frame_shape = frame.shape
            
            print(f"✅ 测试数据创建完成")
            print(f"  - 帧尺寸: {frame_shape}")
            print(f"  - 算法输出: {len(postprocess_result['details']['detections'])}个检测")
            
            # 2. 解析后处理结果
            # 创建模拟的检测结果
            detection_result = {'detections': postprocess_result['details']['detections']}

            parsed_result = TaskExecutionService._parse_postprocess_result(
                postprocess_result, detection_result, algorithm_config, frame_shape
            )
            
            print(f"\n✅ 后处理结果解析完成")
            print(f"  - 告警状态: {parsed_result['hit']}")
            print(f"  - 告警消息: {parsed_result['message']}")
            
            alert_targets = parsed_result['details'].get('alert_targets', [])
            print(f"  - 告警目标数量: {len(alert_targets)}")
            
            for i, target in enumerate(alert_targets):
                print(f"    目标{i+1}: box={target['box']}, score={target['score']:.2f}, type={target['alert_type']}")
            
            # 3. 绘制结果
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, parsed_result)
            
            # 保存结果
            cv2.imwrite("test_flow_with_color.jpg", annotated_frame)
            print(f"\n📸 保存结果: test_flow_with_color.jpg")
            
            return len(alert_targets) > 0
            
        except Exception as e:
            print(f"❌ 有颜色字段流程测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_complete_flow_no_color(self):
        """测试无颜色字段的完整流程"""
        print(f"\n{'='*60}")
        print("测试无颜色字段的完整流程")
        print(f"{'='*60}")
        
        try:
            # 1. 创建测试数据
            frame = self.create_test_frame()
            algorithm_config = self.create_algorithm_config()
            postprocess_result = self.create_mock_postprocess_result_no_color()
            frame_shape = frame.shape
            
            print(f"✅ 测试数据创建完成")
            print(f"  - 帧尺寸: {frame_shape}")
            print(f"  - 算法输出: {len(postprocess_result['details']['detections'])}个检测")
            
            # 2. 解析后处理结果
            # 创建模拟的检测结果
            detection_result = {'detections': postprocess_result['details']['detections']}

            parsed_result = TaskExecutionService._parse_postprocess_result(
                postprocess_result, detection_result, algorithm_config, frame_shape
            )
            
            print(f"\n✅ 后处理结果解析完成")
            print(f"  - 告警状态: {parsed_result['hit']}")
            print(f"  - 告警消息: {parsed_result['message']}")
            
            alert_targets = parsed_result['details'].get('alert_targets', [])
            print(f"  - 告警目标数量: {len(alert_targets)}")
            
            for i, target in enumerate(alert_targets):
                print(f"    目标{i+1}: box={target['box']}, score={target['score']:.2f}, type={target['alert_type']}")
            
            # 3. 绘制结果
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, parsed_result)
            
            # 保存结果
            cv2.imwrite("test_flow_no_color.jpg", annotated_frame)
            print(f"\n📸 保存结果: test_flow_no_color.jpg")
            
            return len(alert_targets) > 0
            
        except Exception as e:
            print(f"❌ 无颜色字段流程测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_no_alert_flow(self):
        """测试无告警的完整流程"""
        print(f"\n{'='*60}")
        print("测试无告警的完整流程")
        print(f"{'='*60}")
        
        try:
            # 1. 创建测试数据
            frame = self.create_test_frame()
            algorithm_config = self.create_algorithm_config()
            
            # 无告警的算法输出
            postprocess_result = {
                'hit': False,
                'message': '未检测到违规',
                'details': {
                    'detections': [
                        {
                            'xyxy': [300, 400, 380, 480],
                            'conf': 0.82,
                            'color': [0, 255, 0],  # 绿色 - 正常
                            'class_id': 0,
                            'label': 'car'
                        }
                    ]
                }
            }
            
            frame_shape = frame.shape
            
            print(f"✅ 无告警测试数据创建完成")
            print(f"  - 帧尺寸: {frame_shape}")
            print(f"  - 算法输出: {len(postprocess_result['details']['detections'])}个检测")
            print(f"  - 告警状态: {postprocess_result['hit']}")
            
            # 2. 解析后处理结果
            # 创建模拟的检测结果
            detection_result = {'detections': postprocess_result['details']['detections']}

            parsed_result = TaskExecutionService._parse_postprocess_result(
                postprocess_result, detection_result, algorithm_config, frame_shape
            )
            
            print(f"\n✅ 无告警结果解析完成")
            print(f"  - 告警状态: {parsed_result['hit']}")
            print(f"  - 告警消息: {parsed_result['message']}")
            
            alert_targets = parsed_result['details'].get('alert_targets', [])
            print(f"  - 告警目标数量: {len(alert_targets)}")
            
            # 3. 绘制结果（应该只显示配置区域，不显示检测框）
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, parsed_result)
            
            # 保存结果
            cv2.imwrite("test_flow_no_alert.jpg", annotated_frame)
            print(f"\n📸 保存结果: test_flow_no_alert.jpg")
            print(f"  ℹ️  应该只显示配置区域，不显示检测框")
            
            return len(alert_targets) == 0
            
        except Exception as e:
            print(f"❌ 无告警流程测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主测试函数"""
    print("🔍 告警目标完整流程测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = AlertTargetFlowTester()
    
    # 运行测试
    test_results = []
    
    print(f"\n🧪 第一阶段：测试有颜色字段的完整流程")
    result1 = tester.test_complete_flow_with_color()
    test_results.append(("有颜色字段流程", result1))
    
    print(f"\n🧪 第二阶段：测试无颜色字段的完整流程")
    result2 = tester.test_complete_flow_no_color()
    test_results.append(("无颜色字段流程", result2))
    
    print(f"\n🧪 第三阶段：测试无告警的完整流程")
    result3 = tester.test_no_alert_flow()
    test_results.append(("无告警流程", result3))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("🏁 完整流程测试结果汇总")
    print(f"{'='*60}")
    
    print(f"📊 测试结果:")
    success_count = 0
    for name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n🎯 总体评估:")
    if success_count == len(test_results):
        print("✅ 所有测试通过！告警目标识别框绘制功能正常")
        print("🔧 修复效果:")
        print("  1. ✅ 改进了告警目标提取逻辑")
        print("  2. ✅ 支持多种算法包输出格式")
        print("  3. ✅ 正确绘制告警目标识别框")
        print("  4. ✅ 区分告警和非告警状态")
    elif success_count > 0:
        print(f"⚠️  部分测试通过 ({success_count}/{len(test_results)})")
        print("🔍 需要进一步检查失败的测试用例")
    else:
        print("❌ 所有测试失败，需要进一步调试")
    
    print(f"\n📸 生成的测试图片:")
    print("  - test_flow_with_color.jpg: 有颜色字段的告警目标")
    print("  - test_flow_no_color.jpg: 无颜色字段的告警目标")
    print("  - test_flow_no_alert.jpg: 无告警状态（仅配置区域）")
    
    return success_count == len(test_results)


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
