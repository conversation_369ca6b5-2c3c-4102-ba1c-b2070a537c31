#!/usr/bin/env python3
"""
算法包输出调试脚本
检查算法包后处理的实际输出数据，特别是告警目标的识别框信息
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class AlgorithmOutputDebugger:
    """算法包输出调试器"""
    
    def create_mock_postprocess_result_with_alerts(self):
        """创建包含告警的模拟后处理结果"""
        return {
            'hit': True,
            'message': '检测到车辆违规',
            'details': {
                'detections': [
                    {
                        'xyxy': [100, 100, 200, 200],
                        'conf': 0.85,
                        'color': [255, 0, 0],  # 红色 - 告警
                        'class_id': 0,
                        'label': 'car'
                    },
                    {
                        'xyxy': [300, 150, 400, 250],
                        'conf': 0.72,
                        'color': [255, 0, 0],  # 红色 - 告警
                        'class_id': 0,
                        'label': 'car'
                    },
                    {
                        'xyxy': [500, 200, 600, 300],
                        'conf': 0.68,
                        'color': [0, 255, 0],  # 绿色 - 正常
                        'class_id': 0,
                        'label': 'car'
                    }
                ]
            }
        }
    
    def create_mock_postprocess_result_no_color(self):
        """创建没有color字段的模拟后处理结果"""
        return {
            'hit': True,
            'message': '检测到车辆',
            'details': {
                'detections': [
                    {
                        'xyxy': [100, 100, 200, 200],
                        'conf': 0.85,
                        'class_id': 0,
                        'label': 'car'
                        # 注意：没有color字段
                    },
                    {
                        'xyxy': [300, 150, 400, 250],
                        'conf': 0.72,
                        'class_id': 0,
                        'label': 'car'
                        # 注意：没有color字段
                    }
                ]
            }
        }
    
    def create_mock_postprocess_result_wrong_color(self):
        """创建错误颜色的模拟后处理结果"""
        return {
            'hit': True,
            'message': '检测到车辆',
            'details': {
                'detections': [
                    {
                        'xyxy': [100, 100, 200, 200],
                        'conf': 0.85,
                        'color': [255, 255, 0],  # 黄色 - 不是预期的红色
                        'class_id': 0,
                        'label': 'car'
                    },
                    {
                        'xyxy': [300, 150, 400, 250],
                        'conf': 0.72,
                        'color': [0, 0, 255],  # 蓝色 - 不是预期的红色
                        'class_id': 0,
                        'label': 'car'
                    }
                ]
            }
        }
    
    def analyze_postprocess_result(self, postprocess_result, name):
        """分析后处理结果"""
        print(f"\n{'='*60}")
        print(f"分析后处理结果: {name}")
        print(f"{'='*60}")
        
        try:
            print(f"✅ 后处理结果基本信息:")
            print(f"  - hit: {postprocess_result.get('hit')}")
            print(f"  - message: {postprocess_result.get('message')}")
            print(f"  - 顶级键: {list(postprocess_result.keys())}")
            
            details = postprocess_result.get('details', {})
            print(f"\n✅ details 结构:")
            print(f"  - details 键: {list(details.keys())}")
            
            detections = details.get('detections', [])
            print(f"\n✅ detections 分析:")
            print(f"  - 检测数量: {len(detections)}")
            
            for i, det in enumerate(detections):
                print(f"  - 检测 {i+1}:")
                print(f"    - 键: {list(det.keys())}")
                print(f"    - xyxy: {det.get('xyxy')}")
                print(f"    - conf: {det.get('conf')}")
                print(f"    - color: {det.get('color')}")
                print(f"    - 是否为告警颜色: {det.get('color') == [255, 0, 0]}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析后处理结果失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_alert_target_extraction(self, postprocess_result, name):
        """测试告警目标提取"""
        print(f"\n{'='*60}")
        print(f"测试告警目标提取: {name}")
        print(f"{'='*60}")
        
        try:
            # 模拟 TaskExecutionService._parse_postprocess_result 中的逻辑
            hit = postprocess_result.get('hit', False)
            details = postprocess_result.get('details', {})
            
            print(f"✅ 提取过程:")
            print(f"  - hit: {hit}")
            
            # 统计告警目标
            alert_targets = []
            detections = details.get('detections', [])
            print(f"  - 总检测数量: {len(detections)}")
            
            for i, det in enumerate(detections):
                color = det.get('color')
                is_alert = color == [255, 0, 0]
                print(f"  - 检测 {i+1}: color={color}, 是否告警={is_alert}")
                
                if is_alert:
                    alert_target = {
                        'box': det.get('xyxy', []),
                        'score': det.get('conf', 0.0),
                        'label': 'vehicle'
                    }
                    alert_targets.append(alert_target)
                    print(f"    ✅ 添加到告警目标: {alert_target}")
            
            print(f"\n📊 提取结果:")
            print(f"  - 告警目标数量: {len(alert_targets)}")
            
            if hit and alert_targets:
                message = f"检测到 {len(alert_targets)} 个告警目标"
                result_details = {
                    'alert_targets': alert_targets,
                    'total_detections': len(detections),
                    'algorithm_result': postprocess_result
                }
                print(f"  ✅ 最终结果: {message}")
                print(f"  ✅ alert_targets: {alert_targets}")
            else:
                message = "未检测到告警目标"
                result_details = {
                    'total_detections': len(detections),
                    'algorithm_result': postprocess_result
                }
                print(f"  ❌ 最终结果: {message}")
                print(f"  ❌ 原因: hit={hit}, alert_targets数量={len(alert_targets)}")
            
            return len(alert_targets) > 0
            
        except Exception as e:
            print(f"❌ 告警目标提取测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_improved_extraction_logic(self):
        """测试改进的提取逻辑"""
        print(f"\n{'='*60}")
        print("测试改进的提取逻辑")
        print(f"{'='*60}")
        
        try:
            # 创建测试数据
            postprocess_result = self.create_mock_postprocess_result_no_color()
            
            print(f"✅ 改进的提取逻辑:")
            print(f"  - 不仅依赖color字段")
            print(f"  - 当hit=True时，提取所有检测结果作为告警目标")
            
            hit = postprocess_result.get('hit', False)
            details = postprocess_result.get('details', {})
            detections = details.get('detections', [])
            
            # 改进的逻辑
            alert_targets = []
            if hit:
                for det in detections:
                    # 如果有color字段且为红色，或者没有color字段但hit=True
                    color = det.get('color')
                    is_alert = (color == [255, 0, 0]) or (color is None and hit)
                    
                    if is_alert:
                        alert_target = {
                            'box': det.get('xyxy', []),
                            'score': det.get('conf', 0.0),
                            'label': det.get('label', 'vehicle'),
                            'alert_type': 'vehicle_counting'  # 添加告警类型
                        }
                        alert_targets.append(alert_target)
                        print(f"  ✅ 提取告警目标: {alert_target}")
            
            print(f"\n📊 改进后的结果:")
            print(f"  - 告警目标数量: {len(alert_targets)}")
            
            return len(alert_targets) > 0
            
        except Exception as e:
            print(f"❌ 改进提取逻辑测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主调试函数"""
    print("🔍 算法包输出调试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    debugger = AlgorithmOutputDebugger()
    
    # 创建测试数据
    test_cases = [
        ("正确告警颜色", debugger.create_mock_postprocess_result_with_alerts()),
        ("缺少color字段", debugger.create_mock_postprocess_result_no_color()),
        ("错误告警颜色", debugger.create_mock_postprocess_result_wrong_color())
    ]
    
    # 运行分析
    print(f"\n🧪 第一阶段：分析后处理结果结构")
    for name, postprocess_result in test_cases:
        debugger.analyze_postprocess_result(postprocess_result, name)
    
    print(f"\n🧪 第二阶段：测试告警目标提取")
    extraction_results = []
    for name, postprocess_result in test_cases:
        result = debugger.test_alert_target_extraction(postprocess_result, name)
        extraction_results.append((name, result))
    
    print(f"\n🧪 第三阶段：测试改进的提取逻辑")
    improved_result = debugger.test_improved_extraction_logic()
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("🏁 调试结果汇总")
    print(f"{'='*60}")
    
    print(f"📊 告警目标提取结果:")
    for name, result in extraction_results:
        status = "✅ 成功提取" if result else "❌ 提取失败"
        print(f"  {name}: {status}")
    
    print(f"\n🔧 问题诊断:")
    success_count = sum(1 for _, result in extraction_results if result)
    
    if success_count == 0:
        print("❌ 所有测试都无法提取告警目标")
        print("🔍 可能的问题:")
        print("  1. 算法包没有设置正确的color字段")
        print("  2. color字段值不是预期的[255, 0, 0]")
        print("  3. 后处理逻辑过于严格")
    elif success_count < len(test_cases):
        print("⚠️  部分测试无法提取告警目标")
        print("🔍 建议:")
        print("  1. 检查算法包的color字段设置")
        print("  2. 考虑放宽提取条件")
    else:
        print("✅ 所有测试都能正确提取告警目标")
    
    print(f"\n💡 改进建议:")
    if improved_result:
        print("✅ 改进的提取逻辑可以解决问题")
        print("🔧 建议修改代码:")
        print("  1. 不仅依赖color字段")
        print("  2. 当hit=True时，提取所有检测结果")
        print("  3. 添加告警类型信息")
    else:
        print("❌ 需要进一步分析算法包输出")
    
    return success_count > 0 or improved_result


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 调试被用户中断")
        sys.exit(0)
