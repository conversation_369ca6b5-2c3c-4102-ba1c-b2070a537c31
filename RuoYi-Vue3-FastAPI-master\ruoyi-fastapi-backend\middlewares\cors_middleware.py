from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware


def add_cors_middleware(app: FastAPI):
    """
    添加跨域中间件

    :param app: FastAPI对象
    :return:
    """
    # 前端页面url - 添加更多常用端口支持
    origins = [
        'http://localhost:80',
        'http://127.0.0.1:80',
        'http://localhost:3000',  # 常用的前端开发端口
        'http://127.0.0.1:3000',
        'http://localhost:8080',  # Vue CLI默认端口
        'http://127.0.0.1:8080',
        'http://localhost:5173',  # Vite默认端口
        'http://127.0.0.1:5173',
        'http://localhost:4173',  # Vite预览端口
        'http://127.0.0.1:4173',
        '*',  # 开发环境允许所有来源（生产环境应该移除）
    ]

    # 后台api允许跨域
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )
