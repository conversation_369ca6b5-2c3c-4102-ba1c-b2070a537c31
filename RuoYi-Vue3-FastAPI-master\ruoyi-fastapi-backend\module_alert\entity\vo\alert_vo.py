from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional




class AlertModel(BaseModel):
    """
    警告记录表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    alert_id: Optional[int] = Field(default=None, description='警告ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    task_id: Optional[int] = Field(default=None, description='任务ID')
    stream_id: Optional[int] = Field(default=None, description='视频流ID')
    model_id: Optional[int] = Field(default=None, description='模型ID')
    algorithm_type: Optional[str] = Field(default=None, description='算法类型')
    alert_type: Optional[str] = Field(default=None, description='警告类型')
    alert_level: Optional[str] = Field(default=None, description='警告等级(low/medium/high)')
    alert_message: Optional[str] = Field(default=None, description='警告消息')
    alert_data: Optional[dict] = Field(default=None, description='警告数据')
    frame_data: Optional[dict] = Field(default=None, description='帧数据')
    detection_data: Optional[dict] = Field(default=None, description='检测结果数据')
    image_path: Optional[str] = Field(default=None, description='警告图片路径')
    screenshot_path: Optional[str] = Field(default=None, description='告警截图路径')
    video_path: Optional[str] = Field(default=None, description='警告视频路径')
    is_handled: Optional[int] = Field(default=None, description='是否已处理')
    handle_by: Optional[str] = Field(default=None, description='处理人')
    handle_time: Optional[datetime] = Field(default=None, description='处理时间')
    handle_remark: Optional[str] = Field(default=None, description='处理备注')
    alert_time: Optional[datetime] = Field(default=None, description='警告时间')
    del_flag: Optional[str] = Field(default=None, description='删除标志（0代表存在 2代表删除）')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')

    @NotBlank(field_name='task_id', message='任务ID不能为空')
    def get_task_id(self):
        return self.task_id

    @NotBlank(field_name='stream_id', message='视频流ID不能为空')
    def get_stream_id(self):
        return self.stream_id

    @NotBlank(field_name='model_id', message='模型ID不能为空')
    def get_model_id(self):
        return self.model_id

    @NotBlank(field_name='algorithm_type', message='算法类型不能为空')
    def get_algorithm_type(self):
        return self.algorithm_type

    @NotBlank(field_name='alert_type', message='警告类型不能为空')
    def get_alert_type(self):
        return self.alert_type

    @NotBlank(field_name='alert_time', message='警告时间不能为空')
    def get_alert_time(self):
        return self.alert_time


    def validate_fields(self):
        self.get_task_id()
        self.get_stream_id()
        self.get_model_id()
        self.get_algorithm_type()
        self.get_alert_type()
        self.get_alert_time()




class AlertQueryModel(AlertModel):
    """
    警告记录不分页查询模型
    """
    pass


class AlertPageQueryModel(AlertQueryModel):
    """
    警告记录分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')

    # 添加时间范围筛选字段
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')

    # 添加状态筛选字段（使用status而不是is_handled）
    status: Optional[str] = Field(default=None, description='处理状态：0-未处理，1-已处理，2-已忽略')

    # 保留用户ID字段用于权限过滤
    user_id: Optional[int] = Field(default=None, description='用户ID')


class DeleteAlertModel(BaseModel):
    """
    删除警告记录模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    alert_ids: str = Field(description='需要删除的警告ID')


class AlertStatusUpdateModel(BaseModel):
    """
    告警状态更新模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    alert_id: int = Field(description='告警ID')
    status: str = Field(description='处理状态：0-未处理，1-已处理，2-已忽略')
    handle_remark: Optional[str] = Field(default=None, description='处理备注')
