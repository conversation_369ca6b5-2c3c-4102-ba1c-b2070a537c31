import request from '@/utils/request'

// 查询监控任务列表
export function listMonitorTasks(query) {
  return request({
    url: '/surveillance/monitor/tasks',
    method: 'get',
    params: query
  })
}

// 查询运行中的任务列表
export function listRunningTasks() {
  return request({
    url: '/surveillance/monitor/running-tasks',
    method: 'get'
  })
}

// 查询任务状态信息
export function getTaskStatus(taskId) {
  return request({
    url: '/surveillance/monitor/task-status/' + taskId,
    method: 'get'
  })
}

// 查询监控统计信息
export function getMonitorStatistics() {
  return request({
    url: '/surveillance/monitor/statistics',
    method: 'get'
  })
}

// 批量启动任务
export function batchStartTasks(taskIds) {
  return request({
    url: '/surveillance/monitor/batch-start',
    method: 'post',
    data: taskIds
  })
}

// 批量停止任务
export function batchStopTasks(taskIds) {
  return request({
    url: '/surveillance/monitor/batch-stop',
    method: 'post',
    data: taskIds
  })
}
