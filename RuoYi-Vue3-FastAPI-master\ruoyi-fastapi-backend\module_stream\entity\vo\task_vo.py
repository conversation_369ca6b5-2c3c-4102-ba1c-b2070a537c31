from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional, Dict
from module_admin.annotation.pydantic_annotation import as_query


class TaskModel(BaseModel):
    """
    检测任务表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    task_id: Optional[int] = Field(default=None, description='任务ID')
    task_name: Optional[str] = Field(default=None, description='任务名称')
    stream_id: Optional[int] = Field(default=None, description='视频流ID')

    # 算法信息
    algorithm_id: Optional[str] = Field(default=None, description='算法ID')
    algorithm_name: Optional[str] = Field(default=None, description='算法名称')
    algorithm_version: Optional[str] = Field(default=None, description='算法版本')
    algorithm_type: Optional[str] = Field(default=None, description='算法类型')

    # 算法配置
    user_config: Optional[Dict] = Field(default=None, description='用户配置参数')

    # 保留字段
    model_id: Optional[int] = Field(default=None, description='模型ID')
    config_id: Optional[int] = Field(default=None, description='算法配置ID')
    algorithm_config: Optional[dict] = Field(default=None, description='算法配置参数')
    bbox_config: Optional[dict] = Field(default=None, description='区域/线段配置')
    alert_config: Optional[dict] = Field(default=None, description='告警配置')
    schedule_config: Optional[dict] = Field(default=None, description='调度配置')
    status: Optional[str] = Field(default=None, description='状态(0停止 1运行 2暂停)')
    last_run_time: Optional[datetime] = Field(default=None, description='最后运行时间')
    next_run_time: Optional[datetime] = Field(default=None, description='下次运行时间')
    run_count: Optional[int] = Field(default=None, description='运行次数')
    alert_count: Optional[int] = Field(default=None, description='告警次数')
    error_count: Optional[int] = Field(default=None, description='错误次数')
    del_flag: Optional[str] = Field(default=None, description='删除标志（0代表存在 2代表删除）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')

    @NotBlank(field_name='task_name', message='任务名称不能为空')
    def get_task_name(self):
        return self.task_name

    @NotBlank(field_name='stream_id', message='视频流ID不能为空')
    def get_stream_id(self):
        return self.stream_id

    @NotBlank(field_name='algorithm_id', message='算法ID不能为空')
    def get_algorithm_id(self):
        return self.algorithm_id

    def validate_fields(self):
        self.get_task_name()
        self.get_stream_id()
        self.get_algorithm_id()


class TaskQueryModel(TaskModel):
    """
    检测任务不分页查询模型
    """
    pass


@as_query
class TaskPageQueryModel(TaskQueryModel):
    """
    检测任务分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteTaskModel(BaseModel):
    """
    删除检测任务模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    task_ids: str = Field(description='需要删除的任务ID')


class TaskDetailModel(BaseModel):
    """
    任务详情模型（包含关联的视频流和算法配置信息）
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    # 任务基本信息
    task_id: int
    task_name: str = "未命名任务"
    status: str = "0"
    run_count: int = 0
    alert_count: int = 0
    error_count: int = 0
    last_run_time: Optional[datetime] = None
    create_time: Optional[datetime] = None
    remark: Optional[str] = None

    # 视频流信息
    stream_name: str = "未知视频流"
    stream_url: str = ""
    stream_status: str = "0"
    stream_location: Optional[str] = None
    
    # 算法配置信息
    algorithm_id: Optional[str] = None
    algorithm_name: Optional[str] = None
    algorithm_version: Optional[str] = None
    has_algorithm_config: bool = False

    # 运行状态描述
    status_text: str = ""
    can_start: bool = False
    can_stop: bool = False
    can_pause: bool = False
