"""
算法配置文件
"""

import os
from pathlib import Path

class AlgorithmConfig:
    """算法相关配置"""
    
    # Custom-Algorithm-main 的基础路径
    # 默认假设在项目根目录的上级目录
    ALGORITHM_BASE_PATH = os.environ.get(
        'ALGORITHM_BASE_PATH', 
        str(Path(__file__).parent.parent.parent.parent / "Custom-Algorithm-main")
    )
    
    # 支持的平台
    SUPPORTED_PLATFORMS = ['KS968', 'KS988']
    
    # 支持的模型类型
    MODEL_TYPE_MAPPING = {
        'KS968': 'rknn',
        'KS988': 'onnx'
    }
    
    # 支持的算法类型
    SUPPORTED_ALGORITHMS = [
        'person_intrusion',      # 人员入侵
        'car_counting',          # 车辆计数
        'helmet_detection',      # 安全帽检测
        'smoking_detection',     # 吸烟检测
        'phone_detection',       # 打电话检测
        'fall_detection',        # 跌倒检测
        'fire_detection',        # 火灾检测
        'crowd_density',         # 人群密度
        'vehicle_detection',     # 车辆检测
        'face_recognition',      # 人脸识别
    ]
    
    @classmethod
    def get_algorithm_path(cls) -> Path:
        """获取算法路径"""
        return Path(cls.ALGORITHM_BASE_PATH)
    
    @classmethod
    def get_demo_path(cls) -> Path:
        """获取Demo路径"""
        return cls.get_algorithm_path() / "Custom-Algorithm-main" / "02_CustomAlgorithm" / "02_Demo"
    
    @classmethod
    def is_valid_algorithm_path(cls, path: str = None) -> bool:
        """检查算法路径是否有效"""
        if path:
            demo_path = Path(path) / "Custom-Algorithm-main" / "02_CustomAlgorithm" / "02_Demo"
        else:
            demo_path = cls.get_demo_path()
        
        return demo_path.exists() and demo_path.is_dir()
    
    @classmethod
    def get_platform_model_type(cls, platform: str) -> str:
        """根据平台获取模型类型"""
        return cls.MODEL_TYPE_MAPPING.get(platform, 'unknown')
