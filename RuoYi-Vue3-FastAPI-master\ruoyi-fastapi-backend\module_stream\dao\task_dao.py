from sqlalchemy import delete, select, update, and_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from module_stream.entity.do.task_do import SurveillanceTask
from module_stream.entity.do.stream_do import SurveillanceStream
from module_stream.entity.vo.task_vo import TaskModel, TaskPageQueryModel
from utils.page_util import PageUtil


class TaskDao:
    """
    检测任务模块数据库操作层
    """

    @classmethod
    async def get_task_detail_by_id(cls, db: AsyncSession, task_id: int):
        """
        根据任务ID获取检测任务详细信息

        :param db: orm对象
        :param task_id: 任务ID
        :return: 检测任务信息对象
        """
        task_info = (
            (
                await db.execute(
                    select(SurveillanceTask)
                    .where(SurveillanceTask.task_id == task_id)
                )
            )
            .scalars()
            .first()
        )

        return task_info

    @classmethod
    async def get_task_detail_by_name(cls, db: AsyncSession, task_name: str):
        """
        根据任务名称获取检测任务详细信息

        :param db: orm对象
        :param task_name: 任务名称
        :return: 检测任务信息对象
        """
        task_info = (
            (
                await db.execute(
                    select(SurveillanceTask)
                    .where(SurveillanceTask.task_name == task_name)
                )
            )
            .scalars()
            .first()
        )

        return task_info

    @classmethod
    async def get_task_list(cls, db: AsyncSession, query_object: TaskPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取检测任务列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 检测任务列表信息对象
        """
        query = select(SurveillanceTask)
        
        # 构建查询条件（移除del_flag过滤，使用物理删除）
        conditions = []
        
        if query_object.task_name:
            conditions.append(SurveillanceTask.task_name.like(f'%{query_object.task_name}%'))
        if query_object.stream_id:
            conditions.append(SurveillanceTask.stream_id == query_object.stream_id)
        if query_object.algorithm_type:
            conditions.append(SurveillanceTask.algorithm_type == query_object.algorithm_type)
        if query_object.status:
            conditions.append(SurveillanceTask.status == query_object.status)

        query = query.where(*conditions).order_by(SurveillanceTask.create_time.desc())

        if is_page:
            # 分页查询
            query_result = await PageUtil.paginate(
                db,
                query,
                query_object.page_num,
                query_object.page_size
            )
        else:
            # 不分页查询
            query_result = (await db.execute(query)).scalars().all()

        return query_result

    @classmethod
    async def get_task_list_by_user(cls, db: AsyncSession, query_object: TaskPageQueryModel, user_id: int, is_page: bool = False):
        """
        根据用户ID获取任务列表（通过关联视频流表）

        :param db: 数据库会话
        :param query_object: 查询参数对象
        :param user_id: 用户ID
        :param is_page: 是否分页
        :return: 任务列表
        """
        # 构建查询条件（移除del_flag过滤，使用物理删除）
        conditions = []

        # 权限过滤：只查询当前用户的视频流对应的任务
        # 注意：如果是管理员用户，可以查看所有任务
        # 这里暂时只过滤用户自己的任务，后续可以根据角色权限调整
        conditions.append(SurveillanceStream.user_id == user_id)

        # 任务名称模糊查询
        if query_object.task_name:
            from utils.log_util import logger
            logger.info(f"任务名称搜索条件: {query_object.task_name}")
            conditions.append(SurveillanceTask.task_name.like(f'%{query_object.task_name}%'))

        # 视频流ID精确查询
        if query_object.stream_id:
            conditions.append(SurveillanceTask.stream_id == query_object.stream_id)

        # 算法类型精确查询
        if query_object.algorithm_type:
            conditions.append(SurveillanceTask.algorithm_type == query_object.algorithm_type)

        # 状态精确查询
        if query_object.status:
            conditions.append(SurveillanceTask.status == query_object.status)

        # 构建查询 - 关联视频流表
        query = select(SurveillanceTask).join(
            SurveillanceStream,
            SurveillanceTask.stream_id == SurveillanceStream.stream_id
        ).where(and_(*conditions)).order_by(desc(SurveillanceTask.task_id))

        # 添加调试日志
        from utils.log_util import logger
        logger.info(f"=== 任务列表查询调试 ===")
        logger.info(f"用户ID: {user_id}")
        logger.info(f"查询参数: {query_object}")
        logger.info(f"是否分页: {is_page}")
        logger.info(f"查询条件数量: {len(conditions)}")

        if is_page:
            # 分页查询
            task_list_result = await PageUtil.paginate(
                db,
                query,
                query_object.page_num,
                query_object.page_size,
                is_page=True
            )
            logger.info(f"分页查询结果: total={getattr(task_list_result, 'total', 0)}, rows={len(getattr(task_list_result, 'rows', []))}")
        else:
            # 全量查询
            result = await db.execute(query)
            task_list = result.scalars().all()
            task_list_result = [task for task in task_list]
            logger.info(f"全量查询结果: {len(task_list_result)} 条记录")

        return task_list_result

    @classmethod
    async def add_task_dao(cls, db: AsyncSession, task: TaskModel):
        """
        新增检测任务数据库操作

        :param db: orm对象
        :param task: 检测任务对象
        :return: 新增校验结果
        """
        db_task = SurveillanceTask(**task.model_dump(exclude_unset=True))
        db.add(db_task)
        await db.flush()
        await db.refresh(db_task)
        return db_task

    @classmethod
    async def edit_task_dao(cls, db: AsyncSession, task: TaskModel):
        """
        编辑检测任务数据库操作

        :param db: orm对象
        :param task: 需要更新的检测任务对象
        :return: 编辑校验结果
        """
        await db.execute(
            update(SurveillanceTask)
            .where(SurveillanceTask.task_id == task.task_id)
            .values(**task.model_dump(exclude_unset=True, exclude={'task_id'}))
        )

    @classmethod
    async def delete_task_dao(cls, db: AsyncSession, task_ids: list):
        """
        删除检测任务数据库操作（物理删除）

        :param db: orm对象
        :param task_ids: 需要删除的检测任务id列表
        :return: 删除校验结果
        """
        from utils.log_util import logger

        logger.info(f"=== 删除任务操作 ===")
        logger.info(f"要删除的任务ID列表: {task_ids}")

        # 执行物理删除操作
        result = await db.execute(
            delete(SurveillanceTask)
            .where(SurveillanceTask.task_id.in_(task_ids))
        )

        logger.info(f"删除操作完成，影响行数: {result.rowcount}")

        # 注意：不在DAO层提交事务，由上层控制器统一处理
        return result.rowcount

    @classmethod
    async def update_task_status(cls, db: AsyncSession, task_id: int, status: str, **kwargs):
        """
        更新任务状态

        :param db: orm对象
        :param task_id: 任务ID
        :param status: 新状态
        :param kwargs: 其他需要更新的字段
        :return: 更新结果
        """
        update_data = {'status': status}
        update_data.update(kwargs)
        
        await db.execute(
            update(SurveillanceTask)
            .where(SurveillanceTask.task_id == task_id)
            .values(**update_data)
        )

    @classmethod
    async def get_tasks_by_stream_id(cls, db: AsyncSession, stream_id: int):
        """
        根据视频流ID获取相关任务

        :param db: orm对象
        :param stream_id: 视频流ID
        :return: 任务列表
        """
        tasks = (
            (
                await db.execute(
                    select(SurveillanceTask)
                    .where(SurveillanceTask.stream_id == stream_id)
                )
            )
            .scalars()
            .all()
        )

        return tasks

    @classmethod
    async def get_running_tasks(cls, db: AsyncSession):
        """
        获取所有状态为运行中的任务

        :param db: orm对象
        :return: 运行中的任务列表
        """
        tasks = (
            (
                await db.execute(
                    select(SurveillanceTask)
                    .where(SurveillanceTask.status == '1')
                    .where(SurveillanceTask.del_flag == '0')
                )
            )
            .scalars()
            .all()
        )

        return tasks
