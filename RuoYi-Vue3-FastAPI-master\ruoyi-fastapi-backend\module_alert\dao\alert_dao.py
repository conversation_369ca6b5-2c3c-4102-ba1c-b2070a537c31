from sqlalchemy import delete, select, update, and_
from sqlalchemy.ext.asyncio import AsyncSession
from module_alert.entity.do.alert_do import SurveillanceAlert
from module_alert.entity.vo.alert_vo import AlertModel, AlertPageQueryModel
from module_stream.entity.do.task_do import SurveillanceTask
from module_stream.entity.do.stream_do import SurveillanceStream
from utils.page_util import PageUtil
from utils.log_util import logger


class AlertDao:
    """
    警告记录模块数据库操作层
    """

    @classmethod
    async def get_alert_detail_by_id(cls, db: AsyncSession, alert_id: int, user_id: int = None):
        """
        根据警告ID获取警告记录详细信息

        :param db: orm对象
        :param alert_id: 警告ID
        :param user_id: 用户ID（用于权限过滤）
        :return: 警告记录信息对象
        """
        query = select(SurveillanceAlert)

        if user_id is not None:
            # 添加用户权限过滤
            query = query.join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)\
                         .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)\
                         .where(
                             SurveillanceAlert.alert_id == alert_id,
                             SurveillanceStream.user_id == user_id
                         )
        else:
            query = query.where(SurveillanceAlert.alert_id == alert_id)

        alert_info = (await db.execute(query)).scalars().first()

        return alert_info

    @classmethod
    async def get_alert_detail_by_info(cls, db: AsyncSession, alert: AlertModel):
        """
        根据警告记录参数获取警告记录信息

        :param db: orm对象
        :param alert: 警告记录参数对象
        :return: 警告记录信息对象
        """
        alert_info = (
            (
                await db.execute(
                    select(SurveillanceAlert).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return alert_info

    @classmethod
    async def get_alert_list(cls, db: AsyncSession, query_object: AlertPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取警告记录列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 警告记录列表信息对象
        """
        # 构建查询条件
        conditions = []

        # 只查询当前用户的记录 - 通过stream表关联用户ID
        if query_object.user_id is not None:
            conditions.append(SurveillanceStream.user_id == query_object.user_id)

        # 警告类型精确查询
        if query_object.alert_type:
            conditions.append(SurveillanceAlert.alert_type == query_object.alert_type)

        # 警告等级精确查询
        if query_object.alert_level:
            conditions.append(SurveillanceAlert.alert_level == query_object.alert_level)

        # 算法类型精确查询 - 注意：SurveillanceAlert表中没有algorithm_type字段，需要通过task表关联
        if query_object.algorithm_type:
            # 这里需要根据实际的数据库结构来调整，可能需要通过task表来查询算法类型
            pass

        # 处理状态精确查询
        if query_object.status is not None:
            conditions.append(SurveillanceAlert.status == query_object.status)

        # 时间范围筛选
        if query_object.begin_time:
            try:
                from datetime import datetime, time
                # 将日期字符串转换为datetime对象，设置为当天开始时间
                start_datetime = datetime.combine(
                    datetime.strptime(query_object.begin_time, '%Y-%m-%d').date(),
                    time(0, 0, 0)
                )
                conditions.append(SurveillanceAlert.alert_time >= start_datetime)
            except ValueError as e:
                logger.warning(f"开始时间格式错误: {query_object.begin_time}, 错误: {e}")

        if query_object.end_time:
            try:
                from datetime import datetime, time
                # 将日期字符串转换为datetime对象，设置为当天结束时间
                end_datetime = datetime.combine(
                    datetime.strptime(query_object.end_time, '%Y-%m-%d').date(),
                    time(23, 59, 59)
                )
                conditions.append(SurveillanceAlert.alert_time <= end_datetime)
            except ValueError as e:
                logger.warning(f"结束时间格式错误: {query_object.end_time}, 错误: {e}")

        # 任务ID精确查询
        if query_object.task_id is not None:
            conditions.append(SurveillanceAlert.task_id == query_object.task_id)

        # 视频流ID精确查询
        if query_object.stream_id is not None:
            conditions.append(SurveillanceAlert.stream_id == query_object.stream_id)

        # 构建查询 - 需要JOIN stream表来获取用户信息
        query = (
            select(SurveillanceAlert)
            .join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)
            .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)
        )

        if conditions:
            query = query.where(and_(*conditions))

        query = query.order_by(SurveillanceAlert.alert_id.desc()).distinct()
        alert_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return alert_list

    @classmethod
    async def add_alert_dao(cls, db: AsyncSession, alert: AlertModel):
        """
        新增警告记录数据库操作

        :param db: orm对象
        :param alert: 警告记录对象
        :return:
        """
        db_alert = SurveillanceAlert(**alert.model_dump(exclude={}))
        db.add(db_alert)
        await db.flush()

        return db_alert

    @classmethod
    async def edit_alert_dao(cls, db: AsyncSession, alert: dict):
        """
        编辑警告记录数据库操作

        :param db: orm对象
        :param alert: 需要更新的警告记录字典
        :return:
        """
        await db.execute(update(SurveillanceAlert), [alert])

    @classmethod
    async def delete_alert_dao(cls, db: AsyncSession, alert: AlertModel):
        """
        删除警告记录数据库操作

        :param db: orm对象
        :param alert: 警告记录对象
        :return:
        """
        result = await db.execute(delete(SurveillanceAlert).where(SurveillanceAlert.alert_id == alert.alert_id))
        return result.rowcount > 0

