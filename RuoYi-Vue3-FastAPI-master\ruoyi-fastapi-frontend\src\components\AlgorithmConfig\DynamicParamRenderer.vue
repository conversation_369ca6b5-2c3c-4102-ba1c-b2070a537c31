<template>
  <div class="dynamic-param-renderer">


    <!-- 检测策略 -->
    <div v-if="renderConfig.reserved_args && renderConfig.reserved_args.strategy" class="param-section">
      <h5>检测策略</h5>
      <div class="param-item">
        <DynamicFormItem
          v-if="shouldShowParam(renderConfig.reserved_args.strategy)"
          param-name="strategy"
          :param-config="renderConfig.reserved_args.strategy"
          :model-value="getNestedValue(modelValue.reserved_args, ['strategy'])"
          @update:model-value="updateNestedValue('reserved_args', ['strategy'], $event)"
        />
      </div>
    </div>

    <!-- 模型参数 -->
    <div v-if="renderConfig.model_args" class="param-section">
      <h5>模型参数</h5>
      <div v-for="(modelConfig, modelName) in renderConfig.model_args" :key="modelName" class="model-section">
        <h6>{{ modelName }}</h6>
        <div v-for="(paramConfig, paramName) in modelConfig" :key="paramName" class="param-item">
          <DynamicFormItem
            :param-name="paramName"
            :param-config="paramConfig"
            :model-value="getNestedValue(modelValue.model_args, [modelName, paramName])"
            @update:model-value="updateNestedValue('model_args', [modelName, paramName], $event)"
          />
        </div>
      </div>
    </div>



    <!-- 高级参数 -->
    <div v-if="renderConfig.alert_window || renderConfig.reserved_args" class="param-section">
      <h5>高级参数</h5>

      <!-- 告警参数 -->
      <div v-if="renderConfig.alert_window">
        <div v-for="(paramConfig, paramName) in renderConfig.alert_window" :key="`alert_${paramName}`" class="param-item">
          <DynamicFormItem
            v-if="shouldShowParam(paramConfig)"
            :param-name="paramName"
            :param-config="paramConfig"
            :model-value="getNestedValue(modelValue.alert_window, [paramName])"
            @update:model-value="updateNestedValue('alert_window', [paramName], $event)"
          />
        </div>
      </div>

      <!-- 保留参数（除了strategy） -->
      <div v-if="renderConfig.reserved_args">
        <div v-for="(paramConfig, paramName) in renderConfig.reserved_args" :key="`reserved_${paramName}`" class="param-item">
          <DynamicFormItem
            v-if="paramName !== 'strategy' && shouldShowParam(paramConfig)"
            :param-name="paramName"
            :param-config="paramConfig"
            :model-value="getNestedValue(modelValue.reserved_args, [paramName])"
            @update:model-value="updateNestedValue('reserved_args', [paramName], $event)"
          />
        </div>
      </div>
    </div>

    <!-- 几何配置 -->
    <div v-if="renderConfig.bbox && (supportsPolygons || supportsLines)" class="param-section">
      <h5>几何配置</h5>
      <div class="bbox-config">
        <div class="config-header">
          <span>
            <template v-if="supportsPolygons">
              区域: {{ (modelValue.bbox?.polygons || []).length }}个
            </template>
            <template v-if="supportsPolygons && supportsLines"> | </template>
            <template v-if="supportsLines">
              线段: {{ (modelValue.bbox?.lines || []).length }}条
            </template>
          </span>
          <el-button
            type="primary"
            size="small"
            @click="$emit('edit-geometry', { supportsPolygons, supportsLines })"
          >
            编辑{{ supportsPolygons ? '区域' : '' }}{{ supportsPolygons && supportsLines ? '和' : '' }}{{ supportsLines ? '线段' : '' }}
          </el-button>
        </div>
        <div v-if="(modelValue.bbox?.polygons || []).length > 0" class="polygon-list">
          <div v-for="(polygon, index) in modelValue.bbox.polygons" :key="index" class="polygon-item">
            <span>区域 {{ index + 1 }}</span>
            <el-button
              type="danger"
              size="small"
              @click="removePolygon(index)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import DynamicFormItem from './DynamicFormItem.vue'

const props = defineProps({
  renderConfig: {
    type: Object,
    required: true
  },
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'edit-geometry'])

// 计算算法是否支持多边形和线段（根据智驱力的exits属性）
const supportsPolygons = computed(() => {
  const bboxConfig = props.renderConfig.bbox
  if (!bboxConfig || !bboxConfig.polygons) return false

  const exits = bboxConfig.polygons.exits
  // exits为"must"（必须）或"optional"（可选）时支持多边形
  return exits === 'must' || exits === 'optional'
})

const supportsLines = computed(() => {
  const bboxConfig = props.renderConfig.bbox
  if (!bboxConfig || !bboxConfig.lines) return false

  const exits = bboxConfig.lines.exits
  // exits为"must"（必须）或"optional"（可选）时支持线段
  return exits === 'must' || exits === 'optional'
})

// 获取嵌套对象的值
const getNestedValue = (obj, path) => {
  if (!obj) return undefined
  return path.reduce((current, key) => current?.[key], obj)
}

// 更新嵌套对象的值
const updateNestedValue = (section, path, value) => {
  const newValue = { ...props.modelValue }
  
  // 确保路径存在
  if (!newValue[section]) {
    newValue[section] = {}
  }
  
  let current = newValue[section]
  for (let i = 0; i < path.length - 1; i++) {
    if (!current[path[i]]) {
      current[path[i]] = {}
    }
    current = current[path[i]]
  }
  
  current[path[path.length - 1]] = value
  emit('update:modelValue', newValue)
}

// 删除多边形
const removePolygon = (index) => {
  const newValue = { ...props.modelValue }
  if (!newValue.bbox) newValue.bbox = {}
  if (!newValue.bbox.polygons) newValue.bbox.polygons = []

  newValue.bbox.polygons.splice(index, 1)
  emit('update:modelValue', newValue)
}

// 判断参数是否应该显示（按照智驱力规则：hide=true显示，hide=false隐藏）
const shouldShowParam = (paramConfig) => {
  // 如果明确设置了hide: false，则隐藏
  if (paramConfig.hide === false) {
    return false
  }

  // 如果没有hide属性或hide为true，则显示
  return true
}


</script>

<style scoped>
.dynamic-param-renderer {
  padding: 16px;
}

.param-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.param-section h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.model-section {
  margin-bottom: 16px;
}

.model-section h6 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.param-item {
  margin-bottom: 16px;
}

.bbox-config .config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.polygon-list {
  max-height: 200px;
  overflow-y: auto;
}

.polygon-item,
.line-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.line-list {
  max-height: 200px;
  overflow-y: auto;
}
</style>
