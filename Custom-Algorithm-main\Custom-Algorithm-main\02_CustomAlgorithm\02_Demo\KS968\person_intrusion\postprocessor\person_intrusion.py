"""
人员入侵检测后处理器
符合智驱力标准接口
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

# 导入基类 - 使用绝对路径导入
postprocessor_file = os.path.join(current_dir, 'postprocessor.py')
import importlib.util
spec = importlib.util.spec_from_file_location("postprocessor_module", postprocessor_file)
postprocessor_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(postprocessor_module)
BasePostprocessor = postprocessor_module.Postprocessor


class Postprocessor(BasePostprocessor):
    """人员入侵检测后处理器"""

    def __init__(self, source_id, alg_name):
        super().__init__(source_id, alg_name)

    def _process(self, result, filter_result):
        """处理检测结果"""
        hit = False

        # 获取策略
        if self.strategy is None:
            self.strategy = self.reserved_args.get('strategy', 'center')

        # 生成多边形区域
        polygons = self._gen_polygons()

        try:
            # 获取过滤后的检测结果
            model_name, rectangles = next(iter(filter_result.items()))

            # 处理每个检测框
            for rectangle in rectangles:
                # 检查是否在任何多边形区域内
                in_polygon = False
                for polygon in polygons.values():
                    if self._is_rectangle_in_polygon(rectangle['xyxy'], polygon['polygon'], self.strategy):
                        in_polygon = True
                        rectangle['color'] = self.alert_color
                        polygon['color'] = self.alert_color
                        hit = True
                        break

                if not in_polygon:
                    rectangle['color'] = self.non_alert_color

                # 添加到结果中
                result['data']['bbox']['rectangles'].append(rectangle)

            # 更新多边形
            result['data']['bbox']['polygons'].update(polygons)

            # 设置命中状态
            result['hit'] = hit

            return True

        except Exception as e:
            print(f"[ERROR] 处理检测结果失败: {e}")
            result['hit'] = False
            return False

    def _filter(self, model_name, model_data):
        """过滤检测结果"""
        targets = []
        model_conf = model_data['model_conf']
        engine_result = model_data['engine_result']

        for engine_result_ in engine_result:
            # 过滤掉置信度低于阈值的目标
            if not self._filter_by_conf(model_conf, engine_result_['conf']):
                continue

            # 过滤掉不在label列表中的目标
            label = self._filter_by_label(model_conf, engine_result_['label'])
            if not label:
                continue

            # 坐标缩放
            xyxy = self._scale(engine_result_['xyxy'])

            # 生成矩形框
            targets.append(self._gen_rectangle(xyxy, self.non_alert_color, label, engine_result_['conf']))

        return targets


