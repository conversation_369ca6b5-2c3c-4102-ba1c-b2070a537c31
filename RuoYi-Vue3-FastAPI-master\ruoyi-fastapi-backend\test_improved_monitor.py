#!/usr/bin/env python3
"""
改进后的实时监控测试
参考 yolo_ROI_ai.py 的实现，测试无论是否有检测结果都能正常播放
"""

import cv2
import numpy as np
import time
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class ImprovedMonitorTester:
    """改进的实时监控测试器"""
    
    def __init__(self):
        self.test_task_id = 888  # 测试任务ID
    
    def create_test_frames(self):
        """创建多种测试帧（模拟不同场景）"""
        frames = []
        
        # 场景1：正常帧，无检测结果
        frame1 = np.ones((480, 640, 3), dtype=np.uint8) * 100
        cv2.putText(frame1, "Scene 1: Normal Frame", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame1, "No Detection", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        frames.append(("正常帧-无检测", frame1, [], None))
        
        # 场景2：有检测但无告警
        frame2 = np.ones((480, 640, 3), dtype=np.uint8) * 120
        cv2.putText(frame2, "Scene 2: Detection No Alert", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.rectangle(frame2, (100, 150), (200, 250), (0, 255, 0), 2)
        cv2.putText(frame2, "Car 0.85", (100, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        detection_result2 = {
            'boxes': [[100, 150, 200, 250]],
            'scores': [0.85],
            'labels': [0]
        }
        alert_result2 = {
            'hit': False,
            'message': '检测到目标但无告警',
            'details': {
                'total_detections': 1,
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '监控区域',
                        'points': [
                            {'x': 50, 'y': 300},
                            {'x': 590, 'y': 300},
                            {'x': 590, 'y': 450},
                            {'x': 50, 'y': 450}
                        ]
                    }
                ]
            }
        }
        frames.append(("检测无告警", frame2, detection_result2, alert_result2))
        
        # 场景3：有检测有告警
        frame3 = np.ones((480, 640, 3), dtype=np.uint8) * 80
        cv2.putText(frame3, "Scene 3: Detection With Alert", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.rectangle(frame3, (300, 200), (400, 300), (0, 0, 255), 3)
        cv2.putText(frame3, "Alert Vehicle 0.92", (300, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        detection_result3 = {
            'boxes': [[300, 200, 400, 300]],
            'scores': [0.92],
            'labels': [0]
        }
        alert_result3 = {
            'hit': True,
            'message': '检测到告警目标',
            'details': {
                'alert_targets': [
                    {
                        'box': [300, 200, 400, 300],
                        'score': 0.92,
                        'label': 'car',
                        'alert_type': 'vehicle_counting'
                    }
                ],
                'total_detections': 1,
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '监控区域',
                        'points': [
                            {'x': 50, 'y': 300},
                            {'x': 590, 'y': 300},
                            {'x': 590, 'y': 450},
                            {'x': 50, 'y': 450}
                        ]
                    }
                ]
            }
        }
        frames.append(("检测有告警", frame3, detection_result3, alert_result3))
        
        # 场景4：空帧测试
        frames.append(("空帧测试", None, [], None))
        
        return frames
    
    def test_monitor_stream_scenarios(self):
        """测试不同监控场景"""
        print(f"\n🔍 测试改进后的监控流程")
        
        try:
            # 启动监控流
            success = TaskExecutionService.start_monitor_stream(self.test_task_id)
            if not success:
                print(f"❌ 监控流启动失败")
                return False
            
            print(f"✅ 监控流启动成功")
            
            # 创建测试帧
            test_frames = self.create_test_frames()
            
            # 推送不同场景的帧
            for i, (scene_name, frame, detection_result, alert_result) in enumerate(test_frames):
                print(f"\n📤 推送场景 {i+1}: {scene_name}")
                
                if frame is None:
                    print(f"  - 测试空帧处理")
                else:
                    print(f"  - 帧尺寸: {frame.shape}")
                    print(f"  - 检测结果: {len(detection_result) if detection_result else 0}个")
                    print(f"  - 告警状态: {alert_result.get('hit') if alert_result else False}")
                
                # 推送帧
                TaskExecutionService._push_monitor_frame(
                    self.test_task_id, frame, detection_result, alert_result
                )
                
                time.sleep(0.1)  # 模拟帧间隔
            
            # 验证帧获取
            print(f"\n🔍 验证帧获取:")
            retrieved_count = 0
            
            for i in range(len(test_frames)):
                frame_data = TaskExecutionService.get_monitor_frame(self.test_task_id, timeout=1.0)
                
                if frame_data:
                    retrieved_count += 1
                    print(f"  ✅ 获取帧{i+1}:")
                    print(f"    - 数据大小: {frame_data.get('frame_size', len(frame_data['frame_data']))}字节")
                    print(f"    - 检测状态: {frame_data['has_detection']}")
                    print(f"    - 告警状态: {frame_data['has_alert']}")
                    
                    # 保存第一帧作为测试
                    if i == 0:
                        with open("test_improved_monitor_frame.jpg", "wb") as f:
                            f.write(frame_data['frame_data'])
                        print(f"    📸 保存测试帧: test_improved_monitor_frame.jpg")
                else:
                    print(f"  ⚠️  无法获取帧{i+1}")
            
            print(f"\n📊 获取结果: {retrieved_count}/{len(test_frames)} 帧")
            
            return retrieved_count > 0
            
        except Exception as e:
            print(f"❌ 监控流程测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_continuous_streaming(self):
        """测试连续流式传输（模拟真实场景）"""
        print(f"\n🔍 测试连续流式传输")
        
        try:
            # 模拟连续的视频帧
            for frame_num in range(10):
                # 创建动态帧
                frame = np.ones((480, 640, 3), dtype=np.uint8) * (80 + frame_num * 10)
                
                # 添加帧信息
                cv2.putText(frame, f"Frame {frame_num+1}/10", (50, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.putText(frame, f"Time: {time.time():.2f}", (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                
                # 模拟随机检测结果
                has_detection = frame_num % 3 == 0  # 每3帧有一次检测
                has_alert = frame_num % 5 == 0      # 每5帧有一次告警
                
                detection_result = []
                alert_result = None
                
                if has_detection:
                    x = 100 + frame_num * 20
                    y = 150
                    detection_result = {
                        'boxes': [[x, y, x+100, y+100]],
                        'scores': [0.8 + frame_num * 0.01],
                        'labels': [0]
                    }
                    
                    # 在帧上绘制检测框
                    cv2.rectangle(frame, (x, y), (x+100, y+100), (0, 255, 0), 2)
                    cv2.putText(frame, f"Car {0.8 + frame_num * 0.01:.2f}", 
                               (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                if has_alert:
                    alert_result = {
                        'hit': True,
                        'message': f'帧{frame_num+1}告警',
                        'details': {
                            'alert_targets': [
                                {
                                    'box': [x, y, x+100, y+100],
                                    'score': 0.8 + frame_num * 0.01,
                                    'label': 'car',
                                    'alert_type': 'vehicle_counting'
                                }
                            ] if has_detection else [],
                            'total_detections': 1 if has_detection else 0
                        }
                    }
                
                # 推送帧
                TaskExecutionService._push_monitor_frame(
                    self.test_task_id, frame, detection_result, alert_result
                )
                
                print(f"  📤 推送帧{frame_num+1}: 检测={has_detection}, 告警={has_alert}")
                
                time.sleep(0.05)  # 模拟20FPS
            
            print(f"✅ 连续流式传输测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 连续流式传输测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup(self):
        """清理测试资源"""
        print(f"\n🧹 清理测试资源")
        
        try:
            TaskExecutionService.stop_monitor_stream(self.test_task_id)
            print(f"✅ 监控流已停止")
        except Exception as e:
            print(f"⚠️  清理失败: {e}")


def main():
    """主测试函数"""
    print("🚀 改进后的实时监控测试开始")
    print("📋 参考 yolo_ROI_ai.py 的实现，确保无论是否有检测结果都能正常播放")
    
    tester = ImprovedMonitorTester()
    
    try:
        # 运行测试
        test_results = []
        
        print(f"\n🧪 第一阶段：不同监控场景测试")
        result1 = tester.test_monitor_stream_scenarios()
        test_results.append(("监控场景测试", result1))
        
        print(f"\n🧪 第二阶段：连续流式传输测试")
        result2 = tester.test_continuous_streaming()
        test_results.append(("连续流式传输", result2))
        
        # 汇总结果
        print(f"\n{'='*60}")
        print("🏁 改进后的实时监控测试结果汇总")
        print(f"{'='*60}")
        
        print(f"📊 测试结果:")
        success_count = 0
        for name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {name}: {status}")
            if result:
                success_count += 1
        
        print(f"\n🎯 总体评估:")
        if success_count == len(test_results):
            print("✅ 所有测试通过！改进后的实时监控功能正常")
            print("🔧 改进特点:")
            print("  1. ✅ 参考 yolo_ROI_ai.py 的实现")
            print("  2. ✅ 无论是否有检测结果都能正常播放")
            print("  3. ✅ 保持算法包和后处理的结果流程")
            print("  4. ✅ 优化帧处理和推送逻辑")
            print("  5. ✅ 增强错误处理和日志记录")
        elif success_count > 0:
            print(f"⚠️  部分测试通过 ({success_count}/{len(test_results)})")
        else:
            print("❌ 所有测试失败，需要进一步调试")
        
        return success_count == len(test_results)
        
    finally:
        # 清理资源
        tester.cleanup()


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
