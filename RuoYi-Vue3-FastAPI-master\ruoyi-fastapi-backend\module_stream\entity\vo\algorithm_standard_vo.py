"""
算法配置标准化VO模型
定义统一的算法配置输入输出格式
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field


class Point(BaseModel):
    """坐标点"""
    x: float = Field(..., description='X坐标')
    y: float = Field(..., description='Y坐标')


class Line(BaseModel):
    """线段"""
    id: str = Field(..., description='线段ID')
    name: str = Field(..., description='线段名称')
    points: List[Point] = Field(..., description='线段端点（2个点）')
    direction: Optional[str] = Field(default='both', description='检测方向：left/right/both')


class Polygon(BaseModel):
    """多边形区域"""
    id: str = Field(..., description='区域ID')
    name: str = Field(..., description='区域名称')
    points: List[Point] = Field(..., description='多边形顶点')
    type: Optional[str] = Field(default='detection', description='区域类型：detection/exclusion')


class ModelParameters(BaseModel):
    """模型参数"""
    confidence_threshold: float = Field(default=0.5, ge=0.0, le=1.0, description='置信度阈值')
    nms_threshold: float = Field(default=0.5, ge=0.0, le=1.0, description='NMS阈值')
    input_size: int = Field(default=640, description='输入图像尺寸')
    max_detections: int = Field(default=100, description='最大检测数量')


class AlertParameters(BaseModel):
    """告警参数"""
    enable_alert: bool = Field(default=True, description='是否启用告警')
    alert_interval: int = Field(default=5, description='告警间隔（秒）')
    alert_threshold: int = Field(default=1, description='告警阈值')
    alert_message: str = Field(default='检测到异常', description='告警消息')


class AlgorithmConfig(BaseModel):
    """标准化算法配置"""
    algorithm_id: str = Field(..., description='算法ID')
    algorithm_name: str = Field(..., description='算法名称')
    algorithm_version: str = Field(default='1.0.0', description='算法版本')

    # 几何配置（根据算法类型使用不同的字段）
    detection_areas: List[Polygon] = Field(default=[], description='检测区域')
    exclusion_areas: List[Polygon] = Field(default=[], description='排除区域')
    detection_lines: List[Line] = Field(default=[], description='检测线段')

    # 模型参数 - 使用前端的字段名
    model_parameters: ModelParameters = Field(default_factory=ModelParameters, description='模型参数')

    # 告警参数 - 使用前端的字段名
    alert_parameters: AlertParameters = Field(default_factory=AlertParameters, description='告警参数')

    # 扩展参数（算法特有参数）
    custom_params: Dict[str, Any] = Field(default={}, description='自定义参数')

    # 为了向后兼容，添加属性访问器
    @property
    def model_params(self) -> ModelParameters:
        return self.model_parameters

    @property
    def alert_params(self) -> AlertParameters:
        return self.alert_parameters


class AlgorithmConfigRequest(BaseModel):
    """算法配置请求"""
    stream_id: int = Field(..., description='视频流ID')
    task_name: str = Field(..., description='任务名称')
    config: AlgorithmConfig = Field(..., description='算法配置')
    remark: Optional[str] = Field(default='', description='备注')


class AlgorithmConfigResponse(BaseModel):
    """算法配置响应"""
    config_id: int = Field(..., description='配置ID')
    task_id: int = Field(..., description='任务ID')
    stream_id: int = Field(..., description='视频流ID')
    algorithm_id: str = Field(..., description='算法ID')
    algorithm_name: str = Field(..., description='算法名称')
    task_name: str = Field(..., description='任务名称')
    status: str = Field(..., description='状态')
    create_time: str = Field(..., description='创建时间')
    update_time: str = Field(..., description='更新时间')


class AlgorithmCapability(BaseModel):
    """算法能力描述"""
    algorithm_id: str = Field(..., description='算法ID')
    algorithm_name: str = Field(..., description='算法名称')
    algorithm_version: str = Field(..., description='算法版本')
    description: str = Field(..., description='算法描述')
    
    # 支持的几何类型
    supports_polygons: bool = Field(default=False, description='是否支持多边形区域')
    supports_lines: bool = Field(default=False, description='是否支持线段检测')
    supports_exclusion: bool = Field(default=False, description='是否支持排除区域')
    
    # 参数配置
    configurable_params: List[str] = Field(default=[], description='可配置参数列表')
    default_config: AlgorithmConfig = Field(..., description='默认配置')
    
    # 输入输出格式
    input_format: Dict[str, Any] = Field(default={}, description='输入格式说明')
    output_format: Dict[str, Any] = Field(default={}, description='输出格式说明')


class TaskExecutionRequest(BaseModel):
    """任务执行请求"""
    task_id: int = Field(..., description='任务ID')
    action: str = Field(..., description='操作：start/stop/pause/resume')
    params: Optional[Dict[str, Any]] = Field(default={}, description='执行参数')


class TaskExecutionResponse(BaseModel):
    """任务执行响应"""
    task_id: int = Field(..., description='任务ID')
    action: str = Field(..., description='执行的操作')
    status: str = Field(..., description='执行状态')
    message: str = Field(..., description='执行消息')
    execution_time: str = Field(..., description='执行时间')


class DetectionResult(BaseModel):
    """检测结果标准格式"""
    task_id: int = Field(..., description='任务ID')
    stream_id: int = Field(..., description='视频流ID')
    timestamp: str = Field(..., description='检测时间戳')
    frame_id: int = Field(..., description='帧ID')
    
    # 检测对象
    detections: List[Dict[str, Any]] = Field(default=[], description='检测到的对象')
    
    # 事件信息
    events: List[Dict[str, Any]] = Field(default=[], description='触发的事件')
    
    # 统计信息
    statistics: Dict[str, Any] = Field(default={}, description='统计信息')
    
    # 原始结果（可选）
    raw_result: Optional[Dict[str, Any]] = Field(default=None, description='原始检测结果')


class AlgorithmMetrics(BaseModel):
    """算法性能指标"""
    task_id: int = Field(..., description='任务ID')
    algorithm_id: str = Field(..., description='算法ID')
    
    # 性能指标
    fps: float = Field(..., description='处理帧率')
    latency: float = Field(..., description='处理延迟（毫秒）')
    cpu_usage: float = Field(..., description='CPU使用率')
    memory_usage: float = Field(..., description='内存使用量（MB）')
    
    # 检测指标
    detection_count: int = Field(default=0, description='检测数量')
    alert_count: int = Field(default=0, description='告警数量')
    error_count: int = Field(default=0, description='错误数量')
    
    # 时间信息
    start_time: str = Field(..., description='开始时间')
    last_update: str = Field(..., description='最后更新时间')
    total_runtime: float = Field(..., description='总运行时间（秒）')
