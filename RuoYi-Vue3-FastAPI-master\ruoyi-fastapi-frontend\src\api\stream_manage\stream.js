import request from '@/utils/request'

// 查询视频流管理列表
export function listStream(query) {
  return request({
    url: '/stream_manage/stream/list',
    method: 'get',
    params: query
  })
}

// 查询视频流管理详细
export function getStream(streamId) {
  return request({
    url: '/stream_manage/stream/' + streamId,
    method: 'get'
  })
}

// 新增视频流管理
export function addStream(data) {
  return request({
    url: '/stream_manage/stream',
    method: 'post',
    data: data
  })
}

// 修改视频流管理
export function updateStream(data) {
  return request({
    url: '/stream_manage/stream',
    method: 'put',
    data: data
  })
}

// 删除视频流管理
export function delStream(streamId) {
  return request({
    url: '/stream_manage/stream/' + streamId,
    method: 'delete'
  })
}

// 测试RTSP连接
export function testRtspConnection(data) {
  const formData = new FormData()
  formData.append('rtsp_url', data.rtspUrl)
  formData.append('timeout', data.timeout)

  return request({
    url: '/stream_manage/stream/test',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取RTSP视频帧
export function getRtspFrame(data) {
  const formData = new FormData()
  formData.append('rtsp_url', data.rtspUrl)
  formData.append('timeout', data.timeout)

  return request({
    url: '/stream_manage/stream/frame',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取可用算法列表
export function getAvailableAlgorithms() {
  return request({
    url: '/stream_manage/stream/algorithms',
    method: 'get'
  })
}

// 保存算法配置
export function saveAlgorithmConfig(data) {
  const formData = new FormData()
  formData.append('stream_id', data.streamId)
  formData.append('algorithm_id', data.algorithmId)
  formData.append('algorithm_name', data.algorithmName || '')
  formData.append('algorithm_version', data.algorithmVersion || '')
  formData.append('task_name', data.taskName || '')
  formData.append('user_config', JSON.stringify(data.config))
  formData.append('remark', data.remark || '')

  return request({
    url: '/stream_manage/stream/algorithm-config',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
