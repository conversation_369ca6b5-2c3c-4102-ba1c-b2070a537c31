import request from '@/utils/request'

// 获取算法能力列表
export function getAlgorithmCapabilities() {
  return request({
    url: '/stream_manage/algorithm/standard/capabilities',
    method: 'get'
  })
}

// 保存算法配置（标准化接口）
export function saveStandardAlgorithmConfig(data) {
  return request({
    url: '/stream_manage/algorithm/standard/config',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取算法配置
export function getAlgorithmConfig(streamId, algorithmId) {
  return request({
    url: `/stream_manage/algorithm/standard/config/${streamId}/${algorithmId}`,
    method: 'get'
  })
}

// 获取算法配置模板
export function getAlgorithmConfigTemplate(algorithmId) {
  return request({
    url: `/stream_manage/algorithm/standard/config/template/${algorithmId}`,
    method: 'get'
  })
}

// 获取算法渲染配置（从智驱力目录）
export function getAlgorithmRenderConfig(algorithmId, platform = 'KS968') {
  return request({
    url: `/stream_manage/algorithm/standard/render-config/${algorithmId}`,
    method: 'get',
    params: { platform }
  })
}

// 获取算法默认参数（从智驱力目录）
export function getAlgorithmDefaultParams(algorithmId, platform = 'KS968') {
  return request({
    url: `/stream_manage/algorithm/standard/default-params/${algorithmId}`,
    method: 'get',
    params: { platform }
  })
}

// 获取可用算法列表（从智驱力目录）
export function getAvailableAlgorithms(platform = 'KS968') {
  return request({
    url: `/stream_manage/algorithm/standard/available-algorithms`,
    method: 'get',
    params: { platform }
  })
}

// 获取算法完整信息
export function getAlgorithmFullInfo(algorithmId, platform = 'KS968') {
  return request({
    url: `/stream_manage/algorithm/standard/algorithm-info/${algorithmId}`,
    method: 'get',
    params: { platform }
  })
}

// 获取所有算法及其完整信息
export function getAlgorithmsWithInfo(platform = 'KS968') {
  return request({
    url: `/stream_manage/algorithm/standard/algorithms-with-info`,
    method: 'get',
    params: { platform }
  })
}

// 验证算法配置
export function validateAlgorithmConfig(algorithmId, config) {
  return request({
    url: `/stream_manage/algorithm/standard/config/validate/${algorithmId}`,
    method: 'get',
    params: { config: JSON.stringify(config) }
  })
}

// 执行任务操作
export function executeTask(data) {
  return request({
    url: '/stream_manage/algorithm/standard/task/execute',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
