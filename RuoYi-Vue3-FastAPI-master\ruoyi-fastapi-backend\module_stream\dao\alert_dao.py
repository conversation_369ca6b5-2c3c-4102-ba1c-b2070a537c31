from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from typing import List, Optional
from datetime import datetime

from module_alert.entity.do.alert_do import SurveillanceAlert
from module_stream.entity.do.task_do import SurveillanceTask
from module_stream.entity.do.stream_do import SurveillanceStream
from utils.page_util import PageResponseModel


class AlertDao:
    """
    告警记录数据访问层
    """

    @classmethod
    async def get_alert_list_by_user(
        cls, 
        db: AsyncSession, 
        user_id: int,
        task_id: Optional[int] = None,
        alert_type: Optional[str] = None,
        status: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        page_num: int = 1,
        page_size: int = 10
    ) -> PageResponseModel:
        """
        根据用户ID获取告警记录列表（分页）
        
        :param db: 数据库会话
        :param user_id: 用户ID
        :param task_id: 任务ID（可选）
        :param alert_type: 告警类型（可选）
        :param status: 处理状态（可选）
        :param start_time: 开始时间（可选）
        :param end_time: 结束时间（可选）
        :param page_num: 页码
        :param page_size: 每页大小
        :return: 分页结果
        """
        # 构建查询条件
        conditions = [
            SurveillanceStream.user_id == user_id,  # 只查询当前用户的告警
            SurveillanceAlert.del_flag == '0'  # 未删除的告警
        ]
        
        # 任务ID过滤
        if task_id:
            conditions.append(SurveillanceAlert.task_id == task_id)
        
        # 告警类型过滤
        if alert_type:
            conditions.append(SurveillanceAlert.alert_type == alert_type)
        
        # 处理状态过滤
        if status:
            conditions.append(SurveillanceAlert.status == status)
        
        # 时间范围过滤
        if start_time:
            conditions.append(SurveillanceAlert.alert_time >= start_time)
        if end_time:
            conditions.append(SurveillanceAlert.alert_time <= end_time)
        
        # 构建查询
        query = (
            select(SurveillanceAlert)
            .join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)
            .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)
            .where(and_(*conditions))
            .order_by(desc(SurveillanceAlert.alert_time))
        )
        
        # 获取总数
        count_query = (
            select(func.count(SurveillanceAlert.alert_id))
            .join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)
            .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)
            .where(and_(*conditions))
        )
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page_num - 1) * page_size
        paginated_query = query.offset(offset).limit(page_size)
        
        result = await db.execute(paginated_query)
        alerts = result.scalars().all()
        
        return PageResponseModel(
            rows=list(alerts),
            page_num=page_num,
            page_size=page_size,
            total=total,
            has_next=offset + page_size < total
        )
    
    @classmethod
    async def get_alert_detail_by_id(cls, db: AsyncSession, alert_id: int) -> Optional[SurveillanceAlert]:
        """
        根据ID获取告警详情
        
        :param db: 数据库会话
        :param alert_id: 告警ID
        :return: 告警详情
        """
        query = (
            select(SurveillanceAlert)
            .where(
                SurveillanceAlert.alert_id == alert_id,
                SurveillanceAlert.del_flag == '0'
            )
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    @classmethod
    async def get_alert_statistics_by_user(
        cls, 
        db: AsyncSession, 
        user_id: int,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> dict:
        """
        获取用户的告警统计信息
        
        :param db: 数据库会话
        :param user_id: 用户ID
        :param start_time: 开始时间（可选）
        :param end_time: 结束时间（可选）
        :return: 统计信息
        """
        # 构建查询条件
        conditions = [
            SurveillanceStream.user_id == user_id,
            SurveillanceAlert.del_flag == '0'
        ]
        
        if start_time:
            conditions.append(SurveillanceAlert.alert_time >= start_time)
        if end_time:
            conditions.append(SurveillanceAlert.alert_time <= end_time)
        
        # 总告警数
        total_query = (
            select(func.count(SurveillanceAlert.alert_id))
            .join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)
            .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)
            .where(and_(*conditions))
        )
        
        # 未处理告警数
        unhandled_query = (
            select(func.count(SurveillanceAlert.alert_id))
            .join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)
            .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)
            .where(and_(*conditions, SurveillanceAlert.status == '0'))
        )
        
        # 按告警类型统计
        type_query = (
            select(
                SurveillanceAlert.alert_type,
                func.count(SurveillanceAlert.alert_id).label('count')
            )
            .join(SurveillanceTask, SurveillanceAlert.task_id == SurveillanceTask.task_id)
            .join(SurveillanceStream, SurveillanceAlert.stream_id == SurveillanceStream.stream_id)
            .where(and_(*conditions))
            .group_by(SurveillanceAlert.alert_type)
        )
        
        # 执行查询
        total_result = await db.execute(total_query)
        unhandled_result = await db.execute(unhandled_query)
        type_result = await db.execute(type_query)
        
        total_count = total_result.scalar() or 0
        unhandled_count = unhandled_result.scalar() or 0
        type_stats = {row.alert_type: row.count for row in type_result}
        
        return {
            'total_count': total_count,
            'unhandled_count': unhandled_count,
            'handled_count': total_count - unhandled_count,
            'type_statistics': type_stats
        }
    
    @classmethod
    async def update_alert_status(
        cls, 
        db: AsyncSession, 
        alert_id: int, 
        status: str,
        handle_user: str,
        handle_remark: Optional[str] = None
    ) -> bool:
        """
        更新告警处理状态
        
        :param db: 数据库会话
        :param alert_id: 告警ID
        :param status: 新状态
        :param handle_user: 处理人
        :param handle_remark: 处理备注
        :return: 更新结果
        """
        try:
            alert = await cls.get_alert_detail_by_id(db, alert_id)
            if not alert:
                return False
            
            alert.status = status
            alert.handle_user = handle_user
            alert.handle_time = datetime.now()
            alert.handle_remark = handle_remark
            alert.update_time = datetime.now()
            
            await db.commit()
            return True
            
        except Exception as e:
            await db.rollback()
            raise e
    
    @classmethod
    async def delete_alert(cls, db: AsyncSession, alert_id: int) -> bool:
        """
        删除告警记录（软删除）
        
        :param db: 数据库会话
        :param alert_id: 告警ID
        :return: 删除结果
        """
        try:
            alert = await cls.get_alert_detail_by_id(db, alert_id)
            if not alert:
                return False
            
            alert.del_flag = '1'
            alert.update_time = datetime.now()
            
            await db.commit()
            return True
            
        except Exception as e:
            await db.rollback()
            raise e
    
    @classmethod
    async def batch_delete_alerts(cls, db: AsyncSession, alert_ids: List[int]) -> bool:
        """
        批量删除告警记录（软删除）
        
        :param db: 数据库会话
        :param alert_ids: 告警ID列表
        :return: 删除结果
        """
        try:
            for alert_id in alert_ids:
                alert = await cls.get_alert_detail_by_id(db, alert_id)
                if alert:
                    alert.del_flag = '1'
                    alert.update_time = datetime.now()
            
            await db.commit()
            return True
            
        except Exception as e:
            await db.rollback()
            raise e
