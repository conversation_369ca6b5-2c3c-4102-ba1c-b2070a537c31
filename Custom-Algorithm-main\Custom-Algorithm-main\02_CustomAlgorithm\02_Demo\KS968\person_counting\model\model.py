#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import cv2
import torch
import sys
import os

# 导入正确的Logger
from logger import LOGGER

# 添加YOLOv5路径 - 使用yolov5-master
yolov5_absolute_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master"
if os.path.exists(yolov5_absolute_path):
    sys.path.insert(0, yolov5_absolute_path)
    LOGGER.info(f"添加YOLOv5路径: {yolov5_absolute_path}")
else:
    LOGGER.warning(f"YOLOv5路径不存在: {yolov5_absolute_path}")

class Model:
    def __init__(self, model_path=None, **kwargs):
        # PyTorch模型，不需要model_path
        self.model_names = ['model']
        self.models = {}
        self.status = False
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        LOGGER.info(f"使用设备: {self.device}")
        
        # 设置推理参数
        self.conf_thres = 0.25
        self.nms_thres = 0.45
        
        # 加载模型
        self._load_models()

    def _load_models(self):
        """加载本地YOLOv5模型 - 只使用本地模型，不使用网络或虚拟模型"""
        try:
            # 确保创建缺失的模块
            self._create_missing_modules()
            
            for model_name in self.model_names:
                LOGGER.info(f"加载生产环境YOLOv5模型: {model_name}")

                # 人员计数算法使用yolov5s
                weights_path = os.path.join(yolov5_absolute_path, "yolov5s.pt")
                LOGGER.info(f"使用权重文件: {weights_path}")

                # 检查权重文件是否存在
                if not os.path.exists(weights_path):
                    raise FileNotFoundError(f"生产环境权重文件不存在: {weights_path}")

                model_loaded = False

                # 方法1: 使用torch.hub本地模式
                try:
                    import torch.hub
                    torch.hub.set_dir(yolov5_absolute_path)
                    model = torch.hub.load(yolov5_absolute_path, 'custom',
                                         path=weights_path, source='local',
                                         force_reload=False, trust_repo=True)

                    # 设置模型参数
                    model.conf = getattr(self, 'conf_thres', 0.25)
                    model.iou = getattr(self, 'nms_thres', 0.45)
                    model.eval()

                    # 设置检测类别为人员
                    model.classes = [0]  # 只检测人员
                    LOGGER.info("设置检测类别: [0] (person)")

                    self.models[model_name] = model
                    LOGGER.info("✅ 成功使用torch.hub本地模式加载生产环境YOLOv5模型")
                    model_loaded = True

                except Exception as e1:
                    LOGGER.warning(f"torch.hub本地模式加载失败: {e1}")

                # 方法2: 直接使用torch.load
                if not model_loaded:
                    try:
                        checkpoint = torch.load(weights_path, map_location=self.device)
                        if isinstance(checkpoint, dict) and 'model' in checkpoint:
                            model = checkpoint['model'].float().eval()
                        else:
                            model = checkpoint.float().eval()

                        # 设置基本属性
                        if not hasattr(model, 'conf'):
                            model.conf = getattr(self, 'conf_thres', 0.25)
                        if not hasattr(model, 'iou'):
                            model.iou = getattr(self, 'nms_thres', 0.45)

                        model.to(self.device)
                        self.models[model_name] = model
                        LOGGER.info("✅ 成功使用torch.load直接加载生产环境模型")
                        model_loaded = True

                    except Exception as e2:
                        LOGGER.warning(f"torch.load直接加载失败: {e2}")

                # 方法3: 使用ultralytics包
                if not model_loaded:
                    try:
                        from ultralytics import YOLO
                        model = YOLO(weights_path)
                        self.models[model_name] = model
                        LOGGER.info("✅ 成功使用ultralytics包加载生产环境模型")
                        model_loaded = True
                    except Exception as e3:
                        LOGGER.warning(f"ultralytics包加载失败: {e3}")

                if not model_loaded:
                    raise RuntimeError(f"❌ 生产环境模型加载失败: 所有加载方法都失败了 - {model_name}")

            self.status = True

        except Exception as e:
            LOGGER.exception(f"本地模型加载失败: {e}")
            self.status = False
            raise RuntimeError(f"无法加载本地YOLOv5模型: {e}")

    def _get_target_classes_from_config(self):
        """从配置文件读取目标检测类别"""
        try:
            import yaml
            # 读取后处理器配置文件
            config_path = os.path.join(os.path.dirname(__file__), '..', 'postprocessor', 'postprocessor.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                # 提取class2label配置中的类别ID
                target_classes = []
                if 'model' in config:
                    for model_name, model_config in config['model'].items():
                        if 'label' in model_config and 'class2label' in model_config['label']:
                            class2label = model_config['label']['class2label']
                            # 获取所有配置的类别ID
                            target_classes.extend([int(class_id) for class_id in class2label.keys()])
                
                if target_classes:
                    LOGGER.info(f"从配置文件读取到检测类别: {target_classes}")
                    return list(set(target_classes))  # 去重
                else:
                    LOGGER.warning("配置文件中未找到class2label配置")
            else:
                LOGGER.warning(f"配置文件不存在: {config_path}")
        except Exception as e:
            LOGGER.error(f"读取配置文件失败: {e}")
        
        return None

    def _create_missing_modules(self):
        """简化的模块创建 - 使用yolov5-master应该不需要太多虚拟模块"""
        LOGGER.info("使用yolov5-master，应该有完整的utils模块")

    def infer(self, data, **kwargs):
        """
        目标检测
        Args:
            data: 图像数据，ndarray类型，RGB格式（BGR格式需转换）
        Returns: infer_result
        """
        infer_result = []
        if self.status:
            try:
                image = data
                
                # 使用本地YOLOv5模型进行推理
                LOGGER.info("使用本地YOLOv5模型推理")
                
                # 预处理图像
                LOGGER.info(f"原始图像形状: {image.shape}")

                # 获取原始图像尺寸
                original_shape = image.shape[:2]  # (height, width)
                self.original_shape = original_shape

                # 默认不缩放，保持原始尺寸（生产环境配置）
                use_letterbox = kwargs.get('use_letterbox', False)
                target_size = kwargs.get('target_size', 0)  # 0表示不缩放
                preserve_aspect_ratio = kwargs.get('preserve_aspect_ratio', True)

                LOGGER.info(f"推理配置: use_letterbox={use_letterbox}, target_size={target_size}, preserve_aspect_ratio={preserve_aspect_ratio}")

                # 图像预处理
                processed_image = image.copy()
                scale = 1.0
                dw, dh = 0, 0

                if target_size > 0:
                    if preserve_aspect_ratio:
                        # 保持宽高比的缩放
                        if max(image.shape[:2]) != target_size:
                            scale = target_size / max(image.shape[:2])
                            if original_shape[0] > original_shape[1]:  # height > width
                                processed_image = cv2.resize(image, (int(original_shape[1] * scale), target_size))
                            else:
                                processed_image = cv2.resize(image, (target_size, int(original_shape[0] * scale)))

                        if use_letterbox:
                            processed_image, ratio, pad = self._letterbox(processed_image, (target_size, target_size))
                            dw, dh = pad
                            self.letterbox_ratio = ratio
                            self.letterbox_pad = pad
                    else:
                        # 直接缩放到目标尺寸
                        processed_image = cv2.resize(image, (target_size, target_size))
                        scale = target_size / max(original_shape)
                else:
                    # 不缩放，保持原始尺寸
                    LOGGER.info("保持原始图像尺寸，不进行缩放")

                LOGGER.info(f"处理后图像尺寸: {processed_image.shape}, 缩放比例: {scale}, 填充: ({dw}, {dh})")

                # 获取模型
                model = self.models.get('model')
                if model is None:
                    LOGGER.error("模型未加载")
                    return infer_result

                # 使用torch.hub模型进行推理（参考yolo_ROI_ai.py）
                results = model(processed_image)

                # 处理不同类型的检测结果
                detections = self._process_model_results(results)

                # 获取支持的类别
                class_mapping = self._get_class_mapping_from_config()
                supported_classes = list(class_mapping.get('class2label', {}).keys())
                supported_classes = [int(c) for c in supported_classes]  # 确保是整数

                # 转换检测结果格式
                for _, detection in detections.iterrows():
                    if detection['confidence'] >= self.conf_thres:
                        # 只处理配置文件中支持的类别
                        if int(detection['class']) in supported_classes:
                            obj = {
                                'label': int(detection['class']),
                                'conf': round(float(detection['confidence']), 2),
                                'name': detection.get('name', 'unknown'),
                                'ch_name': detection.get('ch_name', detection.get('name', 'unknown'))
                            }

                            # 坐标转换回原始图像
                            x1, y1, x2, y2 = detection['xmin'], detection['ymin'], detection['xmax'], detection['ymax']

                            # 去除letterbox填充
                            x1, y1, x2, y2 = x1 - dw, y1 - dh, x2 - dw, y2 - dh

                            # 缩放回原始尺寸
                            if scale != 1:
                                x1, y1, x2, y2 = x1 / scale, y1 / scale, x2 / scale, y2 / scale

                            # 确保坐标在图像范围内
                            xyxy = [
                                max(0, min(int(x1), original_shape[1])),
                                max(0, min(int(y1), original_shape[0])),
                                max(0, min(int(x2), original_shape[1])),
                                max(0, min(int(y2), original_shape[0]))
                            ]

                            obj['xyxy'] = xyxy
                            infer_result.append(obj)

                LOGGER.info(f"检测到 {len(infer_result)} 个人员目标")
                
            except Exception as e:
                LOGGER.exception(f"推理失败: {e}")
        
        return infer_result

    def _process_model_results(self, results):
        """处理不同类型的模型结果"""
        import pandas as pd

        try:
            # 从配置文件读取类别映射
            class_mapping = self._get_class_mapping_from_config()

            # 处理torch.hub加载的模型结果
            if hasattr(results, 'pandas'):
                detections = results.pandas().xyxy[0]
                # 添加中文名称
                if 'name' not in detections.columns:
                    detections['name'] = detections['class'].apply(lambda x: self._get_class_name(int(x), class_mapping))
                detections['ch_name'] = detections['name'].apply(lambda x: class_mapping.get('label_map', {}).get(x, x))
                return detections

            # 处理直接torch.load的模型结果
            elif hasattr(results, 'pred') and len(results.pred) > 0:
                pred = results.pred[0].cpu().numpy()
                if len(pred) > 0:
                    detections = pd.DataFrame(pred, columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class'])
                    # 添加英文和中文类别名称
                    detections['name'] = detections['class'].apply(lambda x: self._get_class_name(int(x), class_mapping))
                    detections['ch_name'] = detections['name'].apply(lambda x: class_mapping.get('label_map', {}).get(x, x))
                else:
                    detections = pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])
                return detections

            # 处理ultralytics YOLO模型结果
            elif hasattr(results, 'boxes'):
                if results.boxes is not None and len(results.boxes) > 0:
                    boxes = results.boxes.xyxy.cpu().numpy()
                    confs = results.boxes.conf.cpu().numpy()
                    classes = results.boxes.cls.cpu().numpy()

                    detections = pd.DataFrame({
                        'xmin': boxes[:, 0],
                        'ymin': boxes[:, 1],
                        'xmax': boxes[:, 2],
                        'ymax': boxes[:, 3],
                        'confidence': confs,
                        'class': classes
                    })

                    # 添加英文和中文类别名称
                    detections['name'] = detections['class'].apply(lambda x: self._get_class_name(int(x), class_mapping))
                    detections['ch_name'] = detections['name'].apply(lambda x: class_mapping.get('label_map', {}).get(x, x))
                else:
                    detections = pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])
                return detections

            else:
                # 未知模型类型
                LOGGER.warning(f"未知的模型结果类型: {type(results)}")
                return pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])

        except Exception as e:
            LOGGER.error(f"处理模型结果失败: {e}")
            return pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name', 'ch_name'])

    def _get_class_mapping_from_config(self):
        """从配置文件读取类别映射"""
        try:
            import yaml
            import os

            # 读取后处理器配置文件
            config_path = os.path.join(os.path.dirname(__file__), '..', 'postprocessor', 'postprocessor.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                # 获取第一个模型的配置
                if 'model' in config:
                    for model_name, model_config in config['model'].items():
                        if 'label' in model_config:
                            return model_config['label']

            LOGGER.warning("未找到类别映射配置，使用默认配置")
            return {
                'class2label': {0: 'person'},
                'label_map': {'person': '人'}
            }

        except Exception as e:
            LOGGER.error(f"读取类别映射配置失败: {e}")
            return {
                'class2label': {0: 'person'},
                'label_map': {'person': '人'}
            }

    def _get_class_name(self, class_id, class_mapping):
        """根据类别ID获取类别名称"""
        class2label = class_mapping.get('class2label', {})
        return class2label.get(class_id, class2label.get(str(class_id), 'unknown'))

    def _convert_pytorch_to_rknn_format(self, pytorch_output):
        """将PyTorch YOLOv5输出转换为RKNN格式"""
        try:
            # 去掉batch维度
            if len(pytorch_output.shape) == 3:
                pytorch_output = pytorch_output[0]  # (num_detections, 85)
            
            total_detections, num_classes = pytorch_output.shape
            
            # YOLOv5标准分割比例
            if total_detections == 25200:  # 640x640输入的标准输出
                split_sizes = [19200, 4800, 1200]
                grid_sizes = [80, 40, 20]
            elif total_detections == 22400:  # 其他输入尺寸
                split_sizes = [16800, 4200, 1400]
                grid_sizes = [80, 40, 20]
            else:
                # 动态计算分割
                split_sizes = [total_detections // 2, total_detections // 4, total_detections // 4]
                grid_sizes = [80, 40, 20]
            
            LOGGER.info(f"PyTorch输出形状: {pytorch_output.shape}, 分割大小: {split_sizes}")
            
            rknn_outputs = []
            start_idx = 0
            
            for i, (split_size, grid_size) in enumerate(zip(split_sizes, grid_sizes)):
                end_idx = start_idx + split_size
                scale_output = pytorch_output[start_idx:end_idx]
                
                try:
                    grid_cells = grid_size * grid_size
                    expected_total = 3 * grid_cells
                    
                    if split_size == expected_total:
                        # 完美匹配
                        temp = scale_output.reshape(3, grid_cells, num_classes)
                        temp = temp.transpose(0, 2, 1)  # (3, 85, grid_cells)
                        reshaped = temp.reshape(3, num_classes, grid_size, grid_size)  # (3, 85, grid_size, grid_size)
                    else:
                        # 填充
                        padding_size = expected_total - split_size
                        padding = np.zeros((padding_size, num_classes), dtype=np.float32)
                        padded = np.vstack([scale_output, padding])
                        temp = padded.reshape(3, grid_cells, num_classes)
                        temp = temp.transpose(0, 2, 1)
                        reshaped = temp.reshape(3, num_classes, grid_size, grid_size)
                    
                    rknn_outputs.append(reshaped)
                except ValueError:
                    # 备用方案
                    backup_output = np.zeros((3, num_classes, grid_size, grid_size), dtype=np.float32)
                    rknn_outputs.append(backup_output)
                
                start_idx = end_idx
            
            LOGGER.info(f"PyTorch输出转换成功: {pytorch_output.shape} -> {[out.shape for out in rknn_outputs]}")
            return rknn_outputs
            
        except Exception as e:
            LOGGER.exception(f"PyTorch输出转换失败: {e}")
            # 返回空的RKNN格式输出
            return [np.zeros((3, 85, 80, 80)), np.zeros((3, 85, 40, 40)), np.zeros((3, 85, 20, 20))]

    def _letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
        """调整图像尺寸，保持宽高比"""
        shape = img.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better test mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding

        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2

        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return img, ratio, (dw, dh)

    def __del__(self):
        """析构函数，清理资源"""
        try:
            for model in self.models.values():
                if hasattr(model, 'cpu'):
                    model.cpu()
            self.models.clear()
        except:
            pass
