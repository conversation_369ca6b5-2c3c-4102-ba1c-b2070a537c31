#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志清理脚本
解决多进程日志文件管理问题
"""

import os
import glob
import time
import shutil
from datetime import datetime, timedelta
from pathlib import Path


def cleanup_process_logs(log_dir="logs", days_to_keep=7):
    """
    清理多进程产生的日志文件
    
    Args:
        log_dir: 日志目录
        days_to_keep: 保留的天数
    """
    log_path = Path(log_dir)
    if not log_path.exists():
        print(f"日志目录不存在: {log_dir}")
        return
    
    print(f"开始清理日志目录: {log_path.absolute()}")
    
    # 1. 清理旧日志文件
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    cleaned_count = 0
    
    # 查找所有日志文件模式
    patterns = [
        "*_error_pid*.log",
        "*_error_pid*.log.*",
        "*.zip"
    ]
    
    for pattern in patterns:
        for log_file in log_path.glob(pattern):
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    print(f"删除旧日志: {log_file.name}")
                    log_file.unlink()
                    cleaned_count += 1
            except Exception as e:
                print(f"删除文件失败 {log_file}: {e}")
    
    print(f"清理旧日志完成，删除了 {cleaned_count} 个文件")
    
    # 2. 合并同日期的进程日志
    merge_daily_logs(log_path)
    
    # 3. 显示当前日志状态
    show_log_status(log_path)


def merge_daily_logs(log_path):
    """合并同一天不同进程的日志文件"""
    print("\n开始合并同日期的进程日志...")
    
    # 按日期分组日志文件
    date_groups = {}
    
    for log_file in log_path.glob("*_error_pid*.log"):
        # 提取日期部分，例如: 2025-07-27_error_pid12345.log -> 2025-07-27
        name_parts = log_file.stem.split('_')
        if len(name_parts) >= 3:
            date_part = name_parts[0]  # 2025-07-27
            
            if date_part not in date_groups:
                date_groups[date_part] = []
            date_groups[date_part].append(log_file)
    
    merged_count = 0
    for date, files in date_groups.items():
        if len(files) > 1:  # 只有多个文件才需要合并
            merged_file = log_path / f"{date}_error_merged.log"
            
            try:
                print(f"合并 {date} 的 {len(files)} 个进程日志...")
                
                with open(merged_file, 'w', encoding='utf-8') as outfile:
                    # 按文件修改时间排序
                    sorted_files = sorted(files, key=lambda f: f.stat().st_mtime)
                    
                    for file in sorted_files:
                        try:
                            with open(file, 'r', encoding='utf-8') as infile:
                                outfile.write(f"\n=== 进程日志文件: {file.name} ===\n")
                                outfile.write(infile.read())
                                outfile.write("\n")
                        except Exception as e:
                            print(f"读取文件失败 {file}: {e}")
                
                print(f"合并完成: {merged_file.name}")
                merged_count += 1
                
                # 删除原始文件
                for file in files:
                    try:
                        file.unlink()
                        print(f"删除原始文件: {file.name}")
                    except Exception as e:
                        print(f"删除文件失败 {file}: {e}")
            
            except Exception as e:
                print(f"合并日志失败: {e}")
    
    if merged_count > 0:
        print(f"日志合并完成，合并了 {merged_count} 个日期的日志")
    else:
        print("没有需要合并的日志文件")


def show_log_status(log_path):
    """显示当前日志状态"""
    print("\n=== 当前日志状态 ===")
    
    total_files = 0
    total_size = 0
    
    # 统计各类日志文件
    file_types = {
        '进程日志': 0,
        '合并日志': 0,
        '压缩日志': 0,
        '其他日志': 0
    }
    
    for log_file in log_path.glob("*"):
        if log_file.is_file():
            total_files += 1
            file_size = log_file.stat().st_size
            total_size += file_size
            
            if '_pid' in log_file.name:
                file_types['进程日志'] += 1
            elif '_merged' in log_file.name:
                file_types['合并日志'] += 1
            elif log_file.suffix == '.zip':
                file_types['压缩日志'] += 1
            else:
                file_types['其他日志'] += 1
    
    print(f"总文件数: {total_files}")
    print(f"总大小: {total_size / 1024 / 1024:.2f} MB")
    
    for file_type, count in file_types.items():
        if count > 0:
            print(f"{file_type}: {count} 个")
    
    # 显示最新的几个日志文件
    print("\n最新的日志文件:")
    recent_files = sorted(
        [f for f in log_path.glob("*.log") if f.is_file()],
        key=lambda f: f.stat().st_mtime,
        reverse=True
    )[:5]
    
    for file in recent_files:
        file_time = datetime.fromtimestamp(file.stat().st_mtime)
        file_size = file.stat().st_size / 1024  # KB
        print(f"  {file.name} ({file_size:.1f} KB, {file_time.strftime('%Y-%m-%d %H:%M:%S')})")


def check_log_conflicts():
    """检查是否存在日志文件冲突"""
    print("\n=== 检查日志文件冲突 ===")
    
    log_path = Path("logs")
    if not log_path.exists():
        print("日志目录不存在")
        return
    
    # 检查是否有被锁定的文件
    locked_files = []
    
    for log_file in log_path.glob("*.log"):
        try:
            # 尝试以写模式打开文件
            with open(log_file, 'a', encoding='utf-8') as f:
                pass  # 如果能打开就没问题
        except PermissionError:
            locked_files.append(log_file)
        except Exception as e:
            print(f"检查文件 {log_file} 时出错: {e}")
    
    if locked_files:
        print(f"发现 {len(locked_files)} 个被锁定的日志文件:")
        for file in locked_files:
            print(f"  {file.name}")
        print("\n建议:")
        print("1. 停止所有相关进程")
        print("2. 重新启动应用")
        print("3. 或者重命名被锁定的文件")
    else:
        print("没有发现被锁定的日志文件")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='日志清理工具')
    parser.add_argument('--days', type=int, default=7, help='保留的天数 (默认: 7)')
    parser.add_argument('--log-dir', default='logs', help='日志目录 (默认: logs)')
    parser.add_argument('--check-only', action='store_true', help='只检查冲突，不清理')
    parser.add_argument('--merge-only', action='store_true', help='只合并日志，不删除旧文件')
    
    args = parser.parse_args()
    
    if args.check_only:
        check_log_conflicts()
    elif args.merge_only:
        log_path = Path(args.log_dir)
        if log_path.exists():
            merge_daily_logs(log_path)
            show_log_status(log_path)
        else:
            print(f"日志目录不存在: {args.log_dir}")
    else:
        cleanup_process_logs(args.log_dir, args.days)


if __name__ == "__main__":
    main()
