#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import numpy as np
from logger import LOGGER


class CrossLineCounter:
    """跨线计数器 - 实现真正的跨线计数逻辑"""
    
    def __init__(self, config_path=None):
        self.config = self._load_config(config_path)
        self.counting_lines = self._parse_lines()
        self.counting_areas = self._parse_polygons()
        self.strategy = self.config.get('basicParams', {}).get('reserved_args', {}).get('strategy', 'center')
        
        # 跟踪数据
        self.tracked_objects = {}  # {track_id: {'prev_pos': (x,y), 'crossed_lines': set()}}
        self.count_stats = {'total': 0, 'in': 0, 'out': 0}
        
        LOGGER.info(f"跨线计数器初始化完成，策略: {self.strategy}")
        LOGGER.info(f"计数线数量: {len(self.counting_lines)}")
        LOGGER.info(f"计数区域数量: {len(self.counting_areas)}")
    
    def _load_config(self, config_path):
        """加载配置文件"""
        if config_path is None:
            config_path = "person_counting.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            LOGGER.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self):
        """默认配置"""
        return {
            "basicParams": {
                "bbox": {
                    "polygons": [
                        {
                            "id": "counting_area",
                            "name": "人员计数区域",
                            "points": [[100, 100], [500, 100], [500, 400], [100, 400]],
                            "color": [0, 255, 0, 100]
                        }
                    ],
                    "lines": [
                        {
                            "id": "counting_line",
                            "name": "人员计数线",
                            "points": [[200, 50], [200, 450]],
                            "direction": "both",
                            "color": [255, 0, 0, 255],
                            "thickness": 3
                        }
                    ]
                },
                "reserved_args": {
                    "strategy": "center"
                }
            }
        }
    
    def _parse_lines(self):
        """解析计数线配置"""
        lines = {}
        bbox_config = self.config.get('basicParams', {}).get('bbox', {})
        for line_config in bbox_config.get('lines', []):
            line_id = line_config['id']
            points = line_config['points']
            if len(points) >= 2:
                lines[line_id] = {
                    'start': tuple(points[0]),
                    'end': tuple(points[1]),
                    'direction': line_config.get('direction', 'both'),
                    'name': line_config.get('name', f'Line_{line_id}'),
                    'crossed_objects': set()
                }
        return lines
    
    def _parse_polygons(self):
        """解析多边形区域配置"""
        areas = {}
        bbox_config = self.config.get('basicParams', {}).get('bbox', {})
        for area_config in bbox_config.get('polygons', []):
            area_id = area_config['id']
            points = area_config['points']
            if len(points) >= 3:
                areas[area_id] = {
                    'points': points,
                    'name': area_config.get('name', f'Area_{area_id}')
                }
        return areas
    
    def get_detection_point(self, bbox, strategy):
        """根据策略获取检测框的关键点"""
        x1, y1, x2, y2 = bbox
        
        if strategy == 'top':
            return ((x1 + x2) / 2, y1)
        elif strategy == 'bottom':
            return ((x1 + x2) / 2, y2)
        elif strategy == 'left':
            return (x1, (y1 + y2) / 2)
        elif strategy == 'right':
            return (x2, (y1 + y2) / 2)
        else:  # center
            return ((x1 + x2) / 2, (y1 + y2) / 2)
    
    def point_line_side(self, point, line_start, line_end):
        """判断点在直线的哪一侧"""
        x, y = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 使用叉积判断点在直线的哪一侧
        # 正值表示在左侧，负值表示在右侧，0表示在直线上
        return (x2 - x1) * (y - y1) - (y2 - y1) * (x - x1)
    
    def line_intersect(self, p1, p2, line_start, line_end):
        """判断线段p1-p2是否与计数线相交"""
        x1, y1 = p1
        x2, y2 = p2
        x3, y3 = line_start
        x4, y4 = line_end
        
        # 计算线段相交
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return False  # 平行线
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom
        
        # 检查交点是否在两条线段上
        return 0 <= t <= 1 and 0 <= u <= 1
    
    def check_cross_line(self, track_id, current_bbox):
        """检查目标是否跨越计数线"""
        current_point = self.get_detection_point(current_bbox, self.strategy)
        
        if track_id not in self.tracked_objects:
            # 新目标，记录初始位置
            self.tracked_objects[track_id] = {
                'prev_pos': current_point,
                'crossed_lines': set()
            }
            return None
        
        prev_point = self.tracked_objects[track_id]['prev_pos']
        crossed_lines = self.tracked_objects[track_id]['crossed_lines']
        
        cross_results = []
        
        for line_id, line_info in self.counting_lines.items():
            line_start = line_info['start']
            line_end = line_info['end']
            direction = line_info['direction']
            
            # 检查是否跨越计数线
            if self.line_intersect(prev_point, current_point, line_start, line_end):
                if line_id not in crossed_lines:
                    # 判断跨越方向
                    prev_side = self.point_line_side(prev_point, line_start, line_end)
                    curr_side = self.point_line_side(current_point, line_start, line_end)
                    
                    cross_direction = None
                    if prev_side > 0 and curr_side <= 0:
                        cross_direction = 'left_to_right'
                    elif prev_side < 0 and curr_side >= 0:
                        cross_direction = 'right_to_left'
                    
                    if cross_direction and (direction == 'both' or direction == cross_direction):
                        crossed_lines.add(line_id)
                        cross_results.append({
                            'line_id': line_id,
                            'line_name': line_info['name'],
                            'direction': cross_direction,
                            'point': current_point
                        })
                        
                        # 更新计数统计
                        self.count_stats['total'] += 1
                        if cross_direction == 'left_to_right':
                            self.count_stats['in'] += 1
                        else:
                            self.count_stats['out'] += 1
                        
                        LOGGER.info(f"目标 {track_id} 跨越计数线 {line_info['name']}, 方向: {cross_direction}")
        
        # 更新位置
        self.tracked_objects[track_id]['prev_pos'] = current_point
        
        return cross_results if cross_results else None
    
    def point_in_polygon(self, point, polygon_points):
        """判断点是否在多边形内（射线法）"""
        x, y = point
        n = len(polygon_points)
        inside = False
        
        p1x, p1y = polygon_points[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_points[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def check_in_area(self, bbox):
        """检查目标是否在计数区域内"""
        point = self.get_detection_point(bbox, self.strategy)
        
        for area_id, area_info in self.counting_areas.items():
            if self.point_in_polygon(point, area_info['points']):
                return {
                    'area_id': area_id,
                    'area_name': area_info['name'],
                    'point': point
                }
        return None
    
    def process_detections(self, detections):
        """处理检测结果，返回计数信息"""
        results = {
            'cross_line_events': [],
            'in_area_objects': [],
            'count_stats': self.count_stats.copy()
        }
        
        for i, detection in enumerate(detections):
            track_id = detection.get('track_id', f'obj_{i}')
            bbox = detection['xyxy']
            
            # 检查跨线
            cross_result = self.check_cross_line(track_id, bbox)
            if cross_result:
                results['cross_line_events'].extend(cross_result)
            
            # 检查是否在区域内
            area_result = self.check_in_area(bbox)
            if area_result:
                area_result['track_id'] = track_id
                area_result['bbox'] = bbox
                results['in_area_objects'].append(area_result)
        
        return results
    
    def get_visualization_data(self):
        """获取可视化数据"""
        return {
            'lines': self.counting_lines,
            'areas': self.counting_areas,
            'strategy': self.strategy,
            'count_stats': self.count_stats
        }
