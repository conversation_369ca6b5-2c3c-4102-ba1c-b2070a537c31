from sqlalchemy import String, SmallInteger, CHAR, BigInteger, Column, JSON, DateTime
from config.database import Base


class SurveillanceStream(Base):
    """
    视频流管理表
    """

    __tablename__ = 'surveillance_stream'

    stream_id = Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment='流ID')
    user_id = Column(BigInteger, nullable=True, comment='用户ID')
    stream_name = Column(String(100), nullable=False, comment='流名称')
    rtsp_url = Column(String(500), nullable=False, comment='RTSP地址')
    location = Column(String(200), nullable=True, comment='安装位置')
    stream_config = Column(JSON, nullable=True, comment='流配置参数')
    status = Column(CHAR(1), nullable=True, comment='状态(0停用 1启用)')
    is_recording = Column(SmallInteger, nullable=True, comment='是否录制')
    del_flag = Column(CHAR(1), nullable=True, comment='删除标志（0代表存在 2代表删除）')
    create_by = Column(String(64), nullable=True, comment='创建者')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(64), nullable=True, comment='更新者')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
    remark = Column(String(500), nullable=True, comment='备注')



