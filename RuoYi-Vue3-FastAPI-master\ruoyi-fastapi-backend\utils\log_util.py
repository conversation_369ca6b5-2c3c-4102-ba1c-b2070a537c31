import os
import sys
import time
import platform
from loguru import logger as _logger
from typing import Dict
from middlewares.trace_middleware import TraceCtx


class LoggerInitializer:
    def __init__(self):
        self.log_path = os.path.join(os.getcwd(), 'logs')
        self.__ensure_log_directory_exists()

        # 解决多进程日志文件冲突：为每个进程创建独立的日志文件
        process_id = os.getpid()
        date_str = time.strftime("%Y-%m-%d")
        self.log_path_error = os.path.join(self.log_path, f'{date_str}_error_pid{process_id}.log')

    def __ensure_log_directory_exists(self):
        """
        确保日志目录存在，如果不存在则创建
        """
        if not os.path.exists(self.log_path):
            os.mkdir(self.log_path)

    @staticmethod
    def __filter(log: Dict):
        """
        自定义日志过滤器，添加trace_id
        """
        try:
            log['trace_id'] = TraceCtx.get_id()
        except Exception:
            # 如果获取trace_id失败，使用默认值
            log['trace_id'] = ''
        return log

    def init_log(self):
        """
        初始化日志配置 - 修复多进程文件冲突问题
        """
        # 自定义日志格式
        format_str = (
            '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
            '<cyan>{trace_id}</cyan> | '
            '<level>{level: <8}</level> | '
            '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - '
            '<level>{message}</level>'
        )

        # 清除所有现有的处理器
        _logger.remove()

        # 添加控制台输出
        _logger.add(
            sys.stderr,
            filter=self.__filter,
            format=format_str,
            enqueue=True,
            level="INFO"
        )

        # 添加文件输出 - 使用进程独立的文件名和更安全的配置
        _logger.add(
            self.log_path_error,
            filter=self.__filter,
            format=format_str,
            rotation='10MB',  # 减小轮转大小，减少冲突概率
            encoding='utf-8',
            enqueue=True,
            retention='7 days',  # 保留7天的日志
            compression='zip',
            delay=True,  # 延迟创建文件，避免启动时的文件冲突
            catch=True,  # 捕获日志记录过程中的异常，避免程序崩溃
        )

        return _logger


# 初始化日志处理器
log_initializer = LoggerInitializer()
logger = log_initializer.init_log()
