"""
智驱力算法包后处理器基类模板
"""

import json
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional


class Postprocessor(ABC):
    """后处理器基类"""
    def __init__(self, source_id: int = 0, alg_name: str = ""):
        self.source_id = source_id
        self.alg_name = alg_name
        self.last_alert_time = 0
        self.alert_interval = 5 
        
    @abstractmethod
    def _process(self, result: Dict, filter_result: Dict, config: Dict = None) -> Dict:
        """
        处理检测结果的抽象方法
        Args:
            result: 检测结果
            filter_result: 过滤后的结果
            config: 配置参数
        Returns:
            处理后的结果字典
        """
        pass
    
    def process(self, result: Dict, image: Any, config: Dict) -> Dict:
        """
        主处理方法
        Args:
            result: 检测结果
            image: 图像数据
            config: 配置参数
        Returns:
            处理结果
        """
        try:
            self._extract_config(config)
            filter_result = self._filter_detections(result)
            processed_result = self._process(result, filter_result, config)
            processed_result['timestamp'] = time.time()
            return processed_result
        except Exception as e:
            print(f"后处理失败: {e}")
            return {
                'hit': False,
                'message': f'后处理错误: {str(e)}',
                'details': {},
                'timestamp': time.time()
            }
    
    def _extract_config(self, config: Dict):
        """提取配置参数"""
        if 'alert_window' in config:
            alert_window = config['alert_window']
            if 'interval' in alert_window:
                self.alert_interval = alert_window['interval']
    
    def _filter_detections(self, result: Dict) -> Dict:
        """过滤检测结果"""
        return result
    
    def _check_alert_interval(self) -> bool:
        """检查告警间隔"""
        current_time = time.time()
        if current_time - self.last_alert_time >= self.alert_interval:
            self.last_alert_time = current_time
            return True
        return False
    
    def _format_result(self, hit: bool, message: str = "", details: Dict = None) -> Dict:
        """格式化结果"""
        if details is None:
            details = {}
            
        return {
            'hit': hit,
            'message': message,
            'details': details,
            'timestamp': time.time(),
            'source_id': self.source_id,
            'algorithm': self.alg_name
        }
