import request from '@/utils/request'

// 查询警告记录列表
export function listAlert(query) {
  return request({
    url: '/surveillance/alert/list',
    method: 'get',
    params: query
  })
}

// 查询警告记录详细
export function getAlert(alertId) {
  return request({
    url: '/surveillance/alert/' + alertId,
    method: 'get'
  })
}

// 新增警告记录
export function addAlert(data) {
  return request({
    url: '/surveillance/alert',
    method: 'post',
    data: data
  })
}

// 修改警告记录
export function updateAlert(data) {
  return request({
    url: '/surveillance/alert',
    method: 'put',
    data: data
  })
}

// 删除警告记录
export function delAlert(alertId) {
  return request({
    url: '/surveillance/alert/' + alertId,
    method: 'delete'
  })
}

// 更新告警状态
export function updateAlertStatus(data) {
  return request({
    url: '/surveillance/alert/status',
    method: 'put',
    data: data
  })
}
