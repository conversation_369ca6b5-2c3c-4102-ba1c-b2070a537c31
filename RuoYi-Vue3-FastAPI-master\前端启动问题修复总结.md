# 前端启动问题修复总结

## 🔍 问题分析

用户报告启动任务时出现前端错误：
```
Uncaught TypeError: proxy.$modal.msgInfo is not a function
```

## 🔧 修复内容

### 1. **修复 `proxy.$modal.msgInfo` 方法缺失**

**问题**：`modal.js` 插件中没有定义 `msgInfo` 方法

**修复文件**：`src/plugins/modal.js`

**修复内容**：
```javascript
// 添加信息消息方法
msgInfo(content) {
  ElMessage.info(content)
},
```

### 2. **实现任务管理页面的批量操作功能**

**问题**：批量启动和批量停止功能只显示"功能开发中"

**修复文件**：
- `src/api/stream_manage/task.js` - 添加批量操作API
- `src/views/stream_manage/task/index.vue` - 实现完整的批量操作逻辑

**新增API**：
```javascript
// 批量启动任务
export function batchStartTasks(taskIds) {
  return request({
    url: '/surveillance/monitor/batch-start',
    method: 'post',
    data: taskIds
  })
}

// 批量停止任务
export function batchStopTasks(taskIds) {
  return request({
    url: '/surveillance/monitor/batch-stop',
    method: 'post',
    data: taskIds
  })
}
```

**实现的功能**：
```javascript
/** 批量启动 */
function handleBatchStart() {
  const ids = multipleSelection.value.map(item => item.taskId);
  if (ids.length === 0) {
    proxy.$modal.msgWarning("请选择要启动的任务");
    return;
  }
  
  proxy.$modal.confirm('是否确认批量启动选中的任务？').then(function() {
    return batchStartTasks(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("批量启动成功");
  }).catch(() => {
    proxy.$modal.msgError("批量启动失败");
  });
}
```

### 3. **添加多选功能支持**

**新增变量**：
```javascript
const multipleSelection = ref([]);
```

**新增方法**：
```javascript
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  multipleSelection.value = selection;
  ids.value = selection.map(item => item.taskId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}
```

### 4. **启用单个任务的启动/停止按钮**

**修复前**：启动/停止按钮被注释掉
**修复后**：根据任务状态显示对应按钮

```html
<el-button
  v-if="scope.row.status === '0'"
  link
  type="success"
  icon="VideoPlay"
  @click="handleStart(scope.row)"
  v-hasPermi="['stream_manage:task:operate']"
  size="small"
>
  启动
</el-button>
<el-button
  v-if="scope.row.status === '1'"
  link
  type="warning"
  icon="VideoPause"
  @click="handleStop(scope.row)"
  v-hasPermi="['stream_manage:task:operate']"
  size="small"
>
  停止
</el-button>
```

### 5. **后端服务调用修复**

**问题**：实时监控页面调用错误的服务
**修复文件**：`module_stream/service/monitor_service.py`

**修复内容**：
```python
# 修复前
await TaskService.start_task_services(query_db, task_id)

# 修复后
from module_stream.service.task_execution_service import TaskExecutionService
await TaskExecutionService.start_task(query_db, task_id, current_user_id)
```

## 🎯 修复效果

### 前端功能
1. ✅ `proxy.$modal.msgInfo` 方法正常工作
2. ✅ 批量启动功能完整实现
3. ✅ 批量停止功能完整实现
4. ✅ 多选功能正常工作
5. ✅ 单个任务启动/停止按钮正常显示

### 后端功能
1. ✅ 实时监控页面调用正确的任务执行服务
2. ✅ 任务管理页面和实时监控页面使用相同的启动逻辑
3. ✅ 智驱力模型正确加载和调用
4. ✅ 实时监控流正常创建

## 🚀 测试验证

### 1. **前端测试**
- 打开任务管理页面
- 选择一个或多个任务
- 点击"批量启动"或"批量停止"按钮
- 应该看到确认对话框和成功/失败消息

### 2. **单个任务测试**
- 对于状态为"停止"的任务，应该显示"启动"按钮
- 对于状态为"运行中"的任务，应该显示"停止"按钮
- 点击按钮应该正常执行操作

### 3. **后端测试**
- 启动任务后，检查后端日志
- 应该看到"任务 X 启动成功，包括实时监控流"
- 检查 `TaskExecutionService.running_tasks` 应该包含运行的任务

## 📝 注意事项

1. **权限控制**：启动/停止按钮需要 `stream_manage:task:operate` 权限
2. **状态判断**：按钮显示基于 `scope.row.status` 字段
   - `'0'` = 停止状态，显示启动按钮
   - `'1'` = 运行状态，显示停止按钮
3. **错误处理**：所有操作都包含错误处理和用户提示

## 🎉 总结

所有前端启动问题已修复：
- ✅ `proxy.$modal.msgInfo` 方法错误已解决
- ✅ 批量操作功能已完整实现
- ✅ 单个任务操作按钮已启用
- ✅ 后端服务调用已修正

现在任务管理页面的启动功能应该可以正常工作了！
