from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_stream.entity.do.stream_do import SurveillanceStream
from module_stream.entity.vo.stream_vo import StreamModel, StreamPageQueryModel
from utils.page_util import PageUtil


class StreamDao:
    """
    视频流管理模块数据库操作层
    """

    @classmethod
    async def get_stream_detail_by_id(cls, db: AsyncSession, stream_id: int):
        """
        根据流ID获取视频流管理详细信息

        :param db: orm对象
        :param stream_id: 流ID
        :return: 视频流管理信息对象
        """
        stream_info = (
            (
                await db.execute(
                    select(SurveillanceStream)
                    .where(SurveillanceStream.stream_id == stream_id)
                )
            )
            .scalars()
            .first()
        )

        return stream_info

    @classmethod
    async def get_stream_detail_by_info(cls, db: AsyncSession, stream: StreamModel):
        """
        根据视频流管理参数获取视频流管理信息（基于用户维度的唯一性检查）

        :param db: orm对象
        :param stream: 视频流管理参数对象
        :return: 视频流管理信息对象
        """
        conditions = [
            SurveillanceStream.user_id == stream.user_id  # 只在当前用户范围内检查
        ]

        if stream.stream_name:
            conditions.append(SurveillanceStream.stream_name == stream.stream_name)
        if stream.rtsp_url:
            conditions.append(SurveillanceStream.rtsp_url == stream.rtsp_url)
        if stream.stream_id:
            conditions.append(SurveillanceStream.stream_id != stream.stream_id)  # 排除自己

        stream_info = (
            (
                await db.execute(
                    select(SurveillanceStream).where(*conditions)
                )
            )
            .scalars()
            .first()
        )

        return stream_info

    @classmethod
    async def get_stream_list(cls, db: AsyncSession, query_object: StreamPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取视频流管理列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 视频流管理列表信息对象
        """
        # 构建查询条件
        conditions = []

        # 只查询当前用户的记录
        if query_object.user_id is not None:
            conditions.append(SurveillanceStream.user_id == query_object.user_id)

        # 流名称模糊查询
        if query_object.stream_name:
            conditions.append(SurveillanceStream.stream_name.like(f'%{query_object.stream_name}%'))

        # RTSP地址模糊查询
        if query_object.rtsp_url:
            conditions.append(SurveillanceStream.rtsp_url.like(f'%{query_object.rtsp_url}%'))

        # 安装位置模糊查询
        if query_object.location:
            conditions.append(SurveillanceStream.location.like(f'%{query_object.location}%'))

        # 状态精确查询
        if query_object.status is not None:
            conditions.append(SurveillanceStream.status == query_object.status)

        # 是否录制精确查询
        if query_object.is_recording is not None:
            conditions.append(SurveillanceStream.is_recording == query_object.is_recording)

        # 构建查询
        query = select(SurveillanceStream)
        if conditions:
            query = query.where(*conditions)
        query = query.order_by(SurveillanceStream.stream_id.desc()).distinct()
        stream_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return stream_list

    @classmethod
    async def add_stream_dao(cls, db: AsyncSession, stream: StreamModel):
        """
        新增视频流管理数据库操作

        :param db: orm对象
        :param stream: 视频流管理对象
        :return:
        """
        db_stream = SurveillanceStream(**stream.model_dump(exclude={}))
        db.add(db_stream)
        await db.flush()

        return db_stream

    @classmethod
    async def edit_stream_dao(cls, db: AsyncSession, stream: dict):
        """
        编辑视频流管理数据库操作

        :param db: orm对象
        :param stream: 需要更新的视频流管理字典
        :return:
        """
        await db.execute(
            update(SurveillanceStream)
            .where(SurveillanceStream.stream_id == stream.get('stream_id'))
            .values(**stream)
        )

    @classmethod
    async def delete_stream_dao(cls, db: AsyncSession, stream_id: int):
        """
        删除视频流管理数据库操作（真删除）

        :param db: orm对象
        :param stream_id: 视频流ID
        :return: 受影响的行数
        """
        result = await db.execute(
            delete(SurveillanceStream)
            .where(SurveillanceStream.stream_id == stream_id)
        )
        return result.rowcount

