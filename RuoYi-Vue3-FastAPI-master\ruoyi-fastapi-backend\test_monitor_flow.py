#!/usr/bin/env python3
"""
实时监控流程测试
测试从检测到WebSocket推送的完整流程
"""

import cv2
import numpy as np
import time
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class MonitorFlowTester:
    """实时监控流程测试器"""
    
    def __init__(self):
        self.test_task_id = 999  # 测试任务ID
    
    def create_test_frame_with_detection(self):
        """创建带检测结果的测试帧"""
        # 创建测试帧
        frame = np.ones((480, 640, 3), dtype=np.uint8) * 128
        
        # 添加一些内容
        cv2.putText(frame, "Monitor Flow Test", (200, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 手动绘制一些检测框
        cv2.rectangle(frame, (100, 100), (200, 200), (0, 255, 0), 2)
        cv2.putText(frame, "Car 0.85", (100, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        cv2.rectangle(frame, (300, 150), (400, 250), (255, 0, 255), 3)  # 告警框
        cv2.putText(frame, "Alert Vehicle 0.92", (300, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
        
        return frame
    
    def create_detection_result(self):
        """创建检测结果"""
        return {
            'boxes': [[100, 100, 200, 200], [300, 150, 400, 250]],
            'scores': [0.85, 0.92],
            'labels': [0, 0],
            'detections': [
                {
                    'xyxy': [100, 100, 200, 200],
                    'conf': 0.85,
                    'label': 'car'
                },
                {
                    'xyxy': [300, 150, 400, 250],
                    'conf': 0.92,
                    'label': 'car'
                }
            ]
        }
    
    def create_alert_result(self, has_alert=True):
        """创建告警结果"""
        if has_alert:
            return {
                'hit': True,
                'message': '检测到告警目标',
                'details': {
                    'alert_targets': [
                        {
                            'box': [300, 150, 400, 250],
                            'score': 0.92,
                            'label': 'car',
                            'alert_type': 'vehicle_counting'
                        }
                    ],
                    'total_detections': 2
                }
            }
        else:
            return {
                'hit': False,
                'message': '未检测到告警',
                'details': {
                    'total_detections': 2
                }
            }
    
    def test_monitor_stream_setup(self):
        """测试监控流设置"""
        print(f"\n🔍 测试监控流设置")
        
        try:
            # 启动监控流
            success = TaskExecutionService.start_monitor_stream(self.test_task_id)
            print(f"✅ 监控流启动: {success}")
            
            # 检查监控流状态
            monitor_streams = TaskExecutionService.monitor_streams
            if self.test_task_id in monitor_streams:
                stream_info = monitor_streams[self.test_task_id]
                print(f"✅ 监控流信息:")
                print(f"  - 帧队列: {stream_info['frame_queue']}")
                print(f"  - 客户端数量: {len(stream_info['clients'])}")
                print(f"  - 创建时间: {stream_info['created_at']}")
                return True
            else:
                print(f"❌ 监控流未创建")
                return False
                
        except Exception as e:
            print(f"❌ 监控流设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_frame_pushing(self):
        """测试帧推送"""
        print(f"\n🔍 测试帧推送")
        
        try:
            # 创建测试数据
            frame = self.create_test_frame_with_detection()
            detection_result = self.create_detection_result()
            
            # 测试无告警推送
            alert_result_no_alert = self.create_alert_result(has_alert=False)
            print(f"📤 推送无告警帧...")
            TaskExecutionService._push_monitor_frame(
                self.test_task_id, frame, detection_result, alert_result_no_alert
            )
            
            # 测试有告警推送
            alert_result_with_alert = self.create_alert_result(has_alert=True)
            print(f"📤 推送告警帧...")
            TaskExecutionService._push_monitor_frame(
                self.test_task_id, frame, detection_result, alert_result_with_alert
            )
            
            print(f"✅ 帧推送完成")
            return True
            
        except Exception as e:
            print(f"❌ 帧推送失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_frame_retrieval(self):
        """测试帧获取"""
        print(f"\n🔍 测试帧获取")
        
        try:
            frames_retrieved = 0
            
            # 尝试获取多个帧
            for i in range(5):
                frame_data = TaskExecutionService.get_monitor_frame(self.test_task_id, timeout=1.0)
                
                if frame_data:
                    frames_retrieved += 1
                    print(f"✅ 获取帧{i+1}:")
                    print(f"  - 数据大小: {len(frame_data['frame_data'])}字节")
                    print(f"  - 时间戳: {frame_data['timestamp']}")
                    print(f"  - 有检测: {frame_data['has_detection']}")
                    print(f"  - 有告警: {frame_data['has_alert']}")
                    
                    # 保存第一帧
                    if i == 0:
                        with open("test_monitor_retrieved_frame.jpg", "wb") as f:
                            f.write(frame_data['frame_data'])
                        print(f"  📸 保存帧: test_monitor_retrieved_frame.jpg")
                else:
                    print(f"⚠️  无法获取帧{i+1}")
                
                time.sleep(0.1)
            
            print(f"\n📊 获取结果: {frames_retrieved}/5 帧")
            return frames_retrieved > 0
            
        except Exception as e:
            print(f"❌ 帧获取失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_client_management(self):
        """测试客户端管理"""
        print(f"\n🔍 测试客户端管理")
        
        try:
            # 添加测试客户端
            test_client_id = "test_client_123"
            TaskExecutionService.add_monitor_client(self.test_task_id, test_client_id)
            
            # 检查客户端是否添加成功
            monitor_streams = TaskExecutionService.monitor_streams
            if self.test_task_id in monitor_streams:
                clients = monitor_streams[self.test_task_id]['clients']
                if test_client_id in clients:
                    print(f"✅ 客户端添加成功: {test_client_id}")
                    print(f"✅ 当前客户端数量: {len(clients)}")
                    return True
                else:
                    print(f"❌ 客户端未添加成功")
                    return False
            else:
                print(f"❌ 监控流不存在")
                return False
                
        except Exception as e:
            print(f"❌ 客户端管理测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup(self):
        """清理测试资源"""
        print(f"\n🧹 清理测试资源")
        
        try:
            # 停止监控流
            TaskExecutionService.stop_monitor_stream(self.test_task_id)
            print(f"✅ 监控流已停止")
            
        except Exception as e:
            print(f"⚠️  清理失败: {e}")


def main():
    """主测试函数"""
    print("🚀 实时监控流程测试开始")
    
    tester = MonitorFlowTester()
    
    try:
        # 运行测试
        test_results = []
        
        print(f"\n🧪 第一阶段：监控流设置测试")
        result1 = tester.test_monitor_stream_setup()
        test_results.append(("监控流设置", result1))
        
        if result1:  # 只有设置成功才继续
            print(f"\n🧪 第二阶段：帧推送测试")
            result2 = tester.test_frame_pushing()
            test_results.append(("帧推送", result2))
            
            print(f"\n🧪 第三阶段：帧获取测试")
            result3 = tester.test_frame_retrieval()
            test_results.append(("帧获取", result3))
            
            print(f"\n🧪 第四阶段：客户端管理测试")
            result4 = tester.test_client_management()
            test_results.append(("客户端管理", result4))
        
        # 汇总结果
        print(f"\n{'='*60}")
        print("🏁 实时监控流程测试结果汇总")
        print(f"{'='*60}")
        
        print(f"📊 测试结果:")
        success_count = 0
        for name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {name}: {status}")
            if result:
                success_count += 1
        
        print(f"\n🎯 总体评估:")
        if success_count == len(test_results):
            print("✅ 所有测试通过！实时监控流程正常")
            print("🔧 流程验证:")
            print("  1. ✅ 监控流正确启动")
            print("  2. ✅ 帧数据正确推送")
            print("  3. ✅ 告警状态正确传递")
            print("  4. ✅ 客户端管理正常")
        elif success_count > 0:
            print(f"⚠️  部分测试通过 ({success_count}/{len(test_results)})")
            print("🔍 需要检查失败的测试项")
        else:
            print("❌ 所有测试失败，监控流程存在问题")
        
        return success_count == len(test_results)
        
    finally:
        # 清理资源
        tester.cleanup()


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
