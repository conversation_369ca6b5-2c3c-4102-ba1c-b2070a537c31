<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑检测线段"
    width="80%"
    :before-close="handleClose"
    class="line-editor-dialog"
  >
    <div class="line-editor">
      <div class="editor-toolbar">
        <el-button
          type="success"
          @click="refreshFrame"
          :loading="loadingFrame"
          icon="Refresh"
        >
          {{ loadingFrame ? '获取中...' : '刷新画面' }}
        </el-button>
        <el-button
          type="primary"
          @click="startDrawing"
          :disabled="isDrawing"
          icon="Edit"
        >
          {{ isDrawing ? '绘制中...' : '开始绘制' }}
        </el-button>
        <el-button
          @click="finishDrawing"
          :disabled="!isDrawing || currentLine.length < 2"
          icon="Check"
        >
          完成绘制
        </el-button>
        <el-button
          @click="cancelDrawing"
          :disabled="!isDrawing"
          icon="Close"
        >
          取消绘制
        </el-button>
        <el-button
          type="danger"
          @click="clearAll"
          icon="Delete"
        >
          清空所有
        </el-button>
      </div>

      <div class="editor-content">
        <div class="video-container">
          <video
            ref="videoRef"
            :src="videoUrl"
            @loadedmetadata="onVideoLoaded"
            controls
            muted
            style="width: 100%; height: auto; display: block;"
          />
          <canvas
            ref="canvasRef"
            @click="onCanvasClick"
            @mousemove="onCanvasMouseMove"
            style="position: absolute; top: 0; left: 0; cursor: crosshair;"
          />
        </div>

        <div class="line-list">
          <h4>已绘制线段 ({{ localLines.length }}条)</h4>
          <div v-if="localLines.length === 0" class="empty-hint">
            暂无线段，点击"开始绘制"添加检测线段
          </div>
          <div v-else class="line-items">
            <div
              v-for="(line, index) in localLines"
              :key="index"
              class="line-item"
              :class="{ active: selectedLineIndex === index }"
              @click="selectLine(index)"
            >
              <span>线段 {{ index + 1 }} ({{ line.length }}个点)</span>
              <el-button
                type="danger"
                size="small"
                @click.stop="removeLine(index)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="editor-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveLines">保存线段</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  videoUrl: {
    type: String,
    default: ''
  },
  lines: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'update:lines'])

// 响应式数据
const dialogVisible = ref(false)
const videoRef = ref(null)
const canvasRef = ref(null)
const loadingFrame = ref(false)
const isDrawing = ref(false)
const currentLine = ref([])
const localLines = ref([])
const selectedLineIndex = ref(-1)

const canvasSize = reactive({
  width: 0,
  height: 0
})

// 监听visible变化
watch(() => props.visible, (visible) => {
  dialogVisible.value = visible
  if (visible) {
    // 复制传入的线段数据
    localLines.value = props.lines.map(line => [...line])
    nextTick(() => {
      initCanvas()
    })
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (visible) => {
  emit('update:visible', visible)
})

// 初始化画布
const initCanvas = () => {
  const video = videoRef.value
  const canvas = canvasRef.value
  
  if (!video || !canvas) return
  
  // 设置画布尺寸与视频一致
  const rect = video.getBoundingClientRect()
  canvas.width = rect.width
  canvas.height = rect.height
  canvas.style.width = rect.width + 'px'
  canvas.style.height = rect.height + 'px'
  
  canvasSize.width = rect.width
  canvasSize.height = rect.height
  
  drawCanvas()
}

// 绘制画布
const drawCanvas = () => {
  const canvas = canvasRef.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制已保存的线段
  localLines.value.forEach((line, lineIndex) => {
    if (line.length >= 2) {
      ctx.strokeStyle = lineIndex === selectedLineIndex.value ? '#ff4500' : '#00ff00'
      ctx.lineWidth = 3
      ctx.beginPath()
      ctx.moveTo(line[0].x, line[0].y)
      for (let i = 1; i < line.length; i++) {
        ctx.lineTo(line[i].x, line[i].y)
      }
      ctx.stroke()
      
      // 绘制端点
      line.forEach((point, pointIndex) => {
        ctx.fillStyle = lineIndex === selectedLineIndex.value ? '#ff4500' : '#00ff00'
        ctx.beginPath()
        ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
        ctx.fill()
        
        // 标注点序号
        ctx.fillStyle = '#ffffff'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(pointIndex + 1, point.x, point.y + 4)
      })
    }
  })
  
  // 绘制当前正在绘制的线段
  if (currentLine.value.length >= 1) {
    ctx.strokeStyle = '#ff0000'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    
    if (currentLine.value.length >= 2) {
      ctx.beginPath()
      ctx.moveTo(currentLine.value[0].x, currentLine.value[0].y)
      for (let i = 1; i < currentLine.value.length; i++) {
        ctx.lineTo(currentLine.value[i].x, currentLine.value[i].y)
      }
      ctx.stroke()
    }
    
    // 绘制当前线段的点
    currentLine.value.forEach((point, index) => {
      ctx.fillStyle = '#ff0000'
      ctx.beginPath()
      ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
      ctx.fill()
      
      ctx.fillStyle = '#ffffff'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(index + 1, point.x, point.y + 4)
    })
    
    ctx.setLineDash([])
  }
}

// 画布点击事件
const onCanvasClick = (event) => {
  if (!isDrawing.value) return
  
  const canvas = canvasRef.value
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  currentLine.value.push({ x, y })
  drawCanvas()
}

// 鼠标移动事件
const onCanvasMouseMove = (event) => {
  // 可以在这里添加鼠标跟随效果
}

// 刷新画面
const refreshFrame = async () => {
  loadingFrame.value = true
  try {
    // 重新加载视频
    if (videoRef.value) {
      videoRef.value.load()
    }
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('画面刷新成功')
  } catch (error) {
    ElMessage.error('画面刷新失败')
  } finally {
    loadingFrame.value = false
  }
}

// 开始绘制
const startDrawing = () => {
  isDrawing.value = true
  currentLine.value = []
  selectedLineIndex.value = -1
  const canvas = canvasRef.value
  if (canvas) {
    canvas.style.cursor = 'crosshair'
  }
}

// 完成绘制
const finishDrawing = () => {
  if (currentLine.value.length >= 2) {
    localLines.value.push([...currentLine.value])
    currentLine.value = []
    isDrawing.value = false
    drawCanvas()
    ElMessage.success('线段绘制完成')
  }
}

// 取消绘制
const cancelDrawing = () => {
  currentLine.value = []
  isDrawing.value = false
  drawCanvas()
}

// 清空所有
const clearAll = () => {
  localLines.value = []
  currentLine.value = []
  isDrawing.value = false
  selectedLineIndex.value = -1
  drawCanvas()
}

// 选择线段
const selectLine = (index) => {
  selectedLineIndex.value = index
  drawCanvas()
}

// 删除线段
const removeLine = (index) => {
  localLines.value.splice(index, 1)
  if (selectedLineIndex.value === index) {
    selectedLineIndex.value = -1
  } else if (selectedLineIndex.value > index) {
    selectedLineIndex.value--
  }
  drawCanvas()
}

// 视频加载完成
const onVideoLoaded = () => {
  nextTick(() => {
    initCanvas()
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 保存线段
const saveLines = () => {
  // 转换坐标为相对坐标 (0-1)
  const normalizedLines = localLines.value.map(line => 
    line.map(point => ({
      x: point.x / canvasSize.width,
      y: point.y / canvasSize.height
    }))
  )
  
  emit('update:lines', normalizedLines)
  dialogVisible.value = false
  
  // 更清楚的保存反馈
  if (normalizedLines.length > 0) {
    ElMessage.success(`线段配置已更新，共${normalizedLines.length}条线段。请点击"保存配置"完成最终保存。`)
  } else {
    ElMessage.info('已清空所有检测线段。请点击"保存配置"完成最终保存。')
  }
}
</script>

<style scoped>
.line-editor-dialog {
  --el-dialog-width: 80%;
}

.line-editor {
  display: flex;
  flex-direction: column;
  height: 70vh;
}

.editor-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.editor-content {
  display: flex;
  flex: 1;
  gap: 16px;
  min-height: 0;
}

.video-container {
  flex: 1;
  position: relative;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.line-list {
  width: 300px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow-y: auto;
}

.line-list h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.empty-hint {
  color: #909399;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}

.line-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.line-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.line-item:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.line-item.active {
  border-color: #409eff;
  background-color: #409eff;
  color: #ffffff;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}
</style>
