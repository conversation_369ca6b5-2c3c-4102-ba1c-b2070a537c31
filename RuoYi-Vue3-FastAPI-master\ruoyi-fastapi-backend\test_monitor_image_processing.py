#!/usr/bin/env python3
"""
实时监控图片处理测试脚本
测试图片编码、检测框绘制、WebSocket推送等功能
"""

import asyncio
import base64
import cv2
import json
import numpy as np
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class MonitorImageTester:
    """实时监控图片处理测试器"""
    
    def __init__(self):
        self.test_task_id = 8888
        self.test_frames_saved = 0
        
    def create_test_frame(self, width=640, height=480):
        """创建测试帧"""
        # 创建一个彩色测试图像
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(255 * x / width),      # 红色通道
                    int(255 * y / height),     # 绿色通道
                    128                        # 蓝色通道
                ]
        
        # 添加一些几何图形
        cv2.rectangle(frame, (50, 50), (150, 150), (255, 255, 255), 2)
        cv2.circle(frame, (width//2, height//2), 50, (0, 255, 255), -1)
        cv2.putText(frame, "TEST FRAME", (width//4, height//4), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return frame
    
    def create_mock_detection_result(self):
        """创建模拟检测结果"""
        return [
            {
                'bbox': [100, 100, 200, 200],
                'confidence': 0.85,
                'class_id': 0,
                'label': 'person'
            },
            {
                'bbox': [300, 150, 400, 250],
                'confidence': 0.72,
                'class_id': 0,
                'label': 'person'
            }
        ]
    
    def create_mock_alert_result(self, width=640, height=480):
        """创建模拟告警结果"""
        return {
            'hit': True,
            'message': '检测到2个人员入侵',
            'data': {
                'detections': self.create_mock_detection_result(),
                'bbox': {
                    'rectangles': [[100, 100, 200, 200], [300, 150, 400, 250]],
                    'polygons': {}
                }
            },
            'details': {
                'configured_areas': [
                    {
                        'id': 'area_1',
                        'name': '测试区域',
                        'points': [
                            {'x': 50, 'y': 50},
                            {'x': 350, 'y': 50},
                            {'x': 350, 'y': 300},
                            {'x': 50, 'y': 300}
                        ],
                        'type': 'polygon'
                    }
                ],
                'configured_lines': [
                    {
                        'id': 'line_1',
                        'name': '计数线',
                        'points': [
                            {'x': 200, 'y': 0},
                            {'x': 200, 'y': height}
                        ],
                        'type': 'line'
                    }
                ],
                'image_size': {'width': width, 'height': height},
                'total_detections': 2,
                'in_area_detections': [
                    {
                        'box': [100, 100, 200, 200],
                        'area_name': '测试区域',
                        'score': 0.85
                    }
                ]
            }
        }
    
    def test_frame_encoding(self):
        """测试帧编码功能"""
        print("=" * 60)
        print("测试帧编码功能")
        print("=" * 60)
        
        try:
            # 创建测试帧
            frame = self.create_test_frame()
            print(f"✅ 创建测试帧: {frame.shape}")
            
            # 测试JPEG编码
            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            frame_data = buffer.tobytes()
            print(f"✅ JPEG编码成功: {len(frame_data)} 字节")
            
            # 测试base64编码
            frame_base64 = base64.b64encode(frame_data).decode('utf-8')
            print(f"✅ Base64编码成功: {len(frame_base64)} 字符")
            
            # 测试解码
            decoded_data = base64.b64decode(frame_base64)
            decoded_frame = cv2.imdecode(np.frombuffer(decoded_data, np.uint8), cv2.IMREAD_COLOR)
            print(f"✅ 解码成功: {decoded_frame.shape}")
            
            # 保存测试图片
            cv2.imwrite("test_original_frame.jpg", frame)
            cv2.imwrite("test_decoded_frame.jpg", decoded_frame)
            print("✅ 测试图片已保存: test_original_frame.jpg, test_decoded_frame.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ 帧编码测试失败: {e}")
            return False
    
    def test_detection_drawing(self):
        """测试检测框绘制功能"""
        print("\n" + "=" * 60)
        print("测试检测框绘制功能")
        print("=" * 60)
        
        try:
            # 创建测试帧
            frame = self.create_test_frame()
            alert_result = self.create_mock_alert_result()
            
            print(f"✅ 创建测试数据")
            print(f"  - 帧尺寸: {frame.shape}")
            print(f"  - 检测结果: {len(alert_result['data']['detections'])}个")
            print(f"  - 配置区域: {len(alert_result['details']['configured_areas'])}个")
            print(f"  - 配置线段: {len(alert_result['details']['configured_lines'])}个")
            
            # 绘制检测框
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, alert_result)
            print("✅ 检测框绘制完成")
            
            # 保存带检测框的图片
            cv2.imwrite("test_annotated_frame.jpg", annotated_frame)
            print("✅ 带检测框的图片已保存: test_annotated_frame.jpg")
            
            # 编码为JPEG
            _, buffer = cv2.imencode('.jpg', annotated_frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            frame_data = buffer.tobytes()
            print(f"✅ 带检测框的帧编码成功: {len(frame_data)} 字节")
            
            return True
            
        except Exception as e:
            print(f"❌ 检测框绘制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_monitor_stream_pipeline(self):
        """测试监控流管道"""
        print("\n" + "=" * 60)
        print("测试监控流管道")
        print("=" * 60)
        
        try:
            # 启动监控流
            success = TaskExecutionService.start_monitor_stream(self.test_task_id)
            if not success:
                print("❌ 启动监控流失败")
                return False
            print("✅ 监控流启动成功")
            
            # 添加测试客户端
            client_id = "test_client_image"
            TaskExecutionService.add_monitor_client(self.test_task_id, client_id)
            print(f"✅ 添加测试客户端: {client_id}")
            
            # 推送测试帧
            for i in range(5):
                frame = self.create_test_frame()
                detection_result = self.create_mock_detection_result()
                alert_result = self.create_mock_alert_result()
                
                # 修改每帧的内容
                cv2.putText(frame, f"Frame {i+1}", (10, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                # 推送帧
                TaskExecutionService._push_monitor_frame(
                    self.test_task_id, frame, detection_result, alert_result
                )
                print(f"✅ 推送第{i+1}帧")
                
                time.sleep(0.1)
            
            # 测试获取帧
            print("\n🔍 测试获取监控帧:")
            for i in range(3):
                frame_data = TaskExecutionService.get_monitor_frame(self.test_task_id, timeout=1.0)
                if frame_data:
                    print(f"  ✅ 获取帧{i+1}: {len(frame_data['frame_data'])}字节, "
                          f"检测: {frame_data['has_detection']}, 告警: {frame_data['has_alert']}")
                    
                    # 保存一帧作为测试
                    if i == 0:
                        with open(f"test_monitor_frame_{i+1}.jpg", "wb") as f:
                            f.write(frame_data['frame_data'])
                        print(f"  📸 保存测试帧: test_monitor_frame_{i+1}.jpg")
                else:
                    print(f"  ⚠️  无法获取帧{i+1}")
            
            return True
            
        except Exception as e:
            print(f"❌ 监控流管道测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # 清理
            TaskExecutionService.stop_monitor_stream(self.test_task_id)
            print("🧹 清理监控流完成")
    
    def test_websocket_message_format(self):
        """测试WebSocket消息格式"""
        print("\n" + "=" * 60)
        print("测试WebSocket消息格式")
        print("=" * 60)
        
        try:
            # 创建测试帧数据
            frame = self.create_test_frame()
            alert_result = self.create_mock_alert_result()
            
            # 绘制检测框
            annotated_frame = frame.copy()
            TaskExecutionService._draw_detection_boxes(annotated_frame, alert_result)
            
            # 编码为JPEG
            _, buffer = cv2.imencode('.jpg', annotated_frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            frame_data = buffer.tobytes()
            
            # 编码为base64
            frame_base64 = base64.b64encode(frame_data).decode('utf-8')
            
            # 创建WebSocket消息
            message = {
                'type': 'video_frame',
                'task_id': self.test_task_id,
                'frame': frame_base64,
                'timestamp': time.time(),
                'has_detection': True,
                'has_alert': True
            }
            
            # 序列化为JSON
            message_json = json.dumps(message)
            print(f"✅ WebSocket消息创建成功")
            print(f"  - 消息类型: {message['type']}")
            print(f"  - 任务ID: {message['task_id']}")
            print(f"  - 帧数据大小: {len(frame_base64)} 字符")
            print(f"  - JSON大小: {len(message_json)} 字符")
            print(f"  - 有检测: {message['has_detection']}")
            print(f"  - 有告警: {message['has_alert']}")
            
            # 测试解析
            parsed_message = json.loads(message_json)
            decoded_frame_data = base64.b64decode(parsed_message['frame'])
            decoded_frame = cv2.imdecode(np.frombuffer(decoded_frame_data, np.uint8), cv2.IMREAD_COLOR)
            
            print(f"✅ 消息解析成功")
            print(f"  - 解码帧尺寸: {decoded_frame.shape}")
            
            # 保存解码后的帧
            cv2.imwrite("test_websocket_frame.jpg", decoded_frame)
            print("✅ WebSocket帧已保存: test_websocket_frame.jpg")
            
            return True
            
        except Exception as e:
            print(f"❌ WebSocket消息格式测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主测试函数"""
    print("🎯 实时监控图片处理测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = MonitorImageTester()
    
    # 运行测试
    tests = [
        ("帧编码功能", tester.test_frame_encoding),
        ("检测框绘制功能", tester.test_detection_drawing),
        ("监控流管道", tester.test_monitor_stream_pipeline),
        ("WebSocket消息格式", tester.test_websocket_message_format)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🏁 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 实时监控图片处理功能正常")
        print("\n📋 功能确认:")
        print("  ✅ 图片编码/解码正常")
        print("  ✅ 检测框绘制正常")
        print("  ✅ 监控流推送正常")
        print("  ✅ WebSocket消息格式正确")
        print("  ✅ 前端可以正常接收和显示图片")
        return True
    elif passed > 0:
        print("⚠️  部分测试通过，功能可能存在问题")
        return True
    else:
        print("❌ 所有测试失败，功能无法使用")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
