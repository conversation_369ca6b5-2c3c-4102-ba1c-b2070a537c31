#!/usr/bin/env python3
"""
静态文件访问测试脚本
测试告警截图的静态文件访问功能
"""

import asyncio
import cv2
import httpx
import numpy as np
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService
from utils.log_util import logger


class StaticFileAccessTester:
    """静态文件访问测试器"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"  # FastAPI服务地址
        self.test_screenshots = []
        
    def create_test_frame(self, width=640, height=480):
        """创建测试帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加彩色背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(128 + 127 * np.sin(x * 0.02)),
                    int(128 + 127 * np.sin(y * 0.02)),
                    int(128 + 127 * np.sin((x + y) * 0.01))
                ]
        
        # 添加文字
        cv2.putText(frame, "Static File Test", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return frame
    
    def create_test_alert_result(self):
        """创建测试告警结果"""
        return {
            'hit': True,
            'message': '静态文件测试告警',
            'data': {
                'bbox': {
                    'rectangles': [[200, 200, 300, 300]],
                    'polygons': {}
                }
            },
            'details': {
                'alert_targets': [
                    {
                        'bbox': [200, 200, 300, 300],
                        'confidence': 0.95,
                        'alert_type': 'area_intrusion',
                        'alert_reason': '静态文件测试'
                    }
                ],
                'configured_areas': [
                    {
                        'id': 'test_area',
                        'name': '测试区域',
                        'points': [
                            {'x': 150, 'y': 150},
                            {'x': 350, 'y': 150},
                            {'x': 350, 'y': 350},
                            {'x': 150, 'y': 350}
                        ]
                    }
                ],
                'configured_lines': [],
                'image_size': {'width': 640, 'height': 480}
            }
        }
    
    async def test_screenshot_saving_and_url(self):
        """测试截图保存和URL生成"""
        print("=" * 60)
        print("测试截图保存和URL生成")
        print("=" * 60)
        
        try:
            # 创建测试数据
            frame = self.create_test_frame()
            alert_result = self.create_test_alert_result()
            
            task_id = 12345
            stream_id = 67890
            
            print(f"✅ 创建测试数据")
            print(f"  - 任务ID: {task_id}")
            print(f"  - 流ID: {stream_id}")
            
            # 保存告警截图
            screenshot_url = await TaskExecutionService._save_alert_screenshot(
                task_id, stream_id, frame, alert_result
            )
            
            if screenshot_url:
                print(f"✅ 告警截图保存成功")
                print(f"  - URL路径: {screenshot_url}")
                
                # 检查文件是否存在
                if screenshot_url.startswith('/uploads/'):
                    file_path = Path("uploads") / screenshot_url[9:]
                else:
                    file_path = Path("uploads") / screenshot_url
                
                if file_path.exists():
                    print(f"  - 文件存在: {file_path}")
                    print(f"  - 文件大小: {file_path.stat().st_size} 字节")
                    self.test_screenshots.append(screenshot_url)
                    return screenshot_url
                else:
                    print(f"  ❌ 文件不存在: {file_path}")
                    return None
            else:
                print("❌ 告警截图保存失败")
                return None
                
        except Exception as e:
            print(f"❌ 截图保存测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_http_access(self, screenshot_url):
        """测试HTTP访问"""
        print("\n" + "=" * 60)
        print("测试HTTP访问")
        print("=" * 60)
        
        try:
            if not screenshot_url:
                print("❌ 没有可测试的截图URL")
                return False
            
            # 构建完整URL
            full_url = f"{self.base_url}{screenshot_url}"
            print(f"🌐 测试URL: {full_url}")
            
            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(full_url)
                
                print(f"📊 HTTP响应:")
                print(f"  - 状态码: {response.status_code}")
                print(f"  - Content-Type: {response.headers.get('content-type', 'N/A')}")
                print(f"  - Content-Length: {response.headers.get('content-length', 'N/A')}")
                
                if response.status_code == 200:
                    print(f"✅ HTTP访问成功")
                    
                    # 检查内容类型
                    content_type = response.headers.get('content-type', '')
                    if 'image' in content_type:
                        print(f"  ✅ 正确的图片类型: {content_type}")
                        
                        # 保存响应内容进行验证
                        test_download_path = "test_downloaded_image.jpg"
                        with open(test_download_path, "wb") as f:
                            f.write(response.content)
                        print(f"  📸 下载图片保存为: {test_download_path}")
                        
                        return True
                    else:
                        print(f"  ⚠️  内容类型不是图片: {content_type}")
                        return False
                else:
                    print(f"❌ HTTP访问失败")
                    print(f"  - 响应内容: {response.text[:200]}...")
                    return False
                    
        except Exception as e:
            print(f"❌ HTTP访问测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_multiple_urls(self):
        """测试多个URL访问"""
        print("\n" + "=" * 60)
        print("测试多个URL访问")
        print("=" * 60)
        
        try:
            # 创建多个测试截图
            screenshot_urls = []
            
            for i in range(3):
                frame = self.create_test_frame()
                alert_result = self.create_test_alert_result()
                alert_result['message'] = f'测试告警 {i+1}'
                
                task_id = 12345 + i
                stream_id = 67890 + i
                
                screenshot_url = await TaskExecutionService._save_alert_screenshot(
                    task_id, stream_id, frame, alert_result
                )
                
                if screenshot_url:
                    screenshot_urls.append(screenshot_url)
                    print(f"✅ 截图 {i+1} 保存成功: {screenshot_url}")
                
                # 短暂延迟确保文件名不同
                await asyncio.sleep(0.1)
            
            print(f"\n📊 保存结果: {len(screenshot_urls)}/3 个截图保存成功")
            
            # 测试访问所有URL
            success_count = 0
            for i, url in enumerate(screenshot_urls):
                print(f"\n🌐 测试访问截图 {i+1}: {url}")
                full_url = f"{self.base_url}{url}"
                
                try:
                    async with httpx.AsyncClient(timeout=5.0) as client:
                        response = await client.get(full_url)
                        
                        if response.status_code == 200:
                            print(f"  ✅ 访问成功 (状态码: {response.status_code})")
                            success_count += 1
                        else:
                            print(f"  ❌ 访问失败 (状态码: {response.status_code})")
                            
                except Exception as e:
                    print(f"  ❌ 访问异常: {e}")
            
            print(f"\n📊 访问结果: {success_count}/{len(screenshot_urls)} 个URL访问成功")
            
            return success_count == len(screenshot_urls)
            
        except Exception as e:
            print(f"❌ 多URL测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_static_mount_config(self):
        """测试静态文件挂载配置"""
        print("\n" + "=" * 60)
        print("测试静态文件挂载配置")
        print("=" * 60)
        
        try:
            # 检查uploads目录是否存在
            uploads_dir = Path("uploads")
            if uploads_dir.exists():
                print(f"✅ uploads目录存在: {uploads_dir.absolute()}")
            else:
                print(f"❌ uploads目录不存在: {uploads_dir.absolute()}")
                return False
            
            # 检查alerts子目录
            alerts_dir = uploads_dir / "alerts"
            if alerts_dir.exists():
                print(f"✅ alerts目录存在: {alerts_dir.absolute()}")
                
                # 列出日期目录
                date_dirs = [d for d in alerts_dir.iterdir() if d.is_dir()]
                print(f"  - 日期目录数量: {len(date_dirs)}")
                for date_dir in date_dirs:
                    file_count = len(list(date_dir.glob("*.jpg")))
                    print(f"    - {date_dir.name}: {file_count} 个图片文件")
            else:
                print(f"⚠️  alerts目录不存在: {alerts_dir.absolute()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 静态文件配置测试失败: {e}")
            return False


async def main():
    """主测试函数"""
    print("🎯 静态文件访问测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = StaticFileAccessTester()
    
    # 运行测试
    tests = [
        ("静态文件配置", lambda: tester.test_static_mount_config()),
        ("截图保存和URL", tester.test_screenshot_saving_and_url),
        ("多URL访问", tester.test_multiple_urls)
    ]
    
    results = {}
    screenshot_url = None
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            if test_name == "截图保存和URL":
                screenshot_url = await test_func()
                results[test_name] = screenshot_url is not None
            else:
                results[test_name] = await test_func() if asyncio.iscoroutinefunction(test_func) else test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 如果有截图URL，测试HTTP访问
    if screenshot_url:
        print(f"\n🧪 开始 HTTP访问测试...")
        try:
            results["HTTP访问"] = await tester.test_http_access(screenshot_url)
        except Exception as e:
            print(f"❌ HTTP访问测试异常: {e}")
            results["HTTP访问"] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🏁 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 静态文件访问功能正常")
        print("\n📋 解决方案:")
        print("  ✅ 添加了 /uploads 静态文件挂载")
        print("  ✅ 修正了截图URL格式")
        print("  ✅ 告警截图现在可以正常访问")
        return True
    else:
        print("❌ 部分测试失败")
        print("\n🔧 可能的解决方案:")
        print("  1. 确保FastAPI服务正在运行")
        print("  2. 检查静态文件挂载配置")
        print("  3. 验证uploads目录权限")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
