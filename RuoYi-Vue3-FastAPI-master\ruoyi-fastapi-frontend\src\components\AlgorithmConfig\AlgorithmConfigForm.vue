<template>
  <div class="algorithm-config-form">
    <!-- 基本信息表单 -->
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="任务名称" prop="taskName" required>
        <el-input
          v-model="formData.taskName"
          placeholder="请输入任务名称（必填）"
          maxlength="50"
          show-word-limit
          clearable
        />
      </el-form-item>
      
      <el-form-item label="算法选择" prop="algorithmId" required>
        <el-select
          v-model="formData.algorithmId"
          placeholder="请选择算法"
          style="width: 100%"
          @change="onAlgorithmChange"
        >
          <el-option
            v-for="algorithm in algorithms"
            :key="algorithm.algorithm_id"
            :label="algorithm.algorithm_name"
            :value="algorithm.algorithm_id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <!-- 算法配置区域 -->
    <div v-if="selectedAlgorithm" class="algorithm-config-section">
      <h4>算法配置</h4>
      <slot 
        name="algorithm-config" 
        :algorithm="selectedAlgorithm"
        :config="algorithmConfig"
        @update:config="updateAlgorithmConfig"
      />
    </div>
    
    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        保存配置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAlgorithmCapabilities } from '@/api/stream_manage/algorithm'
import { 
  validateTaskName, 
  generateDefaultTaskName,
  formatValidationErrors 
} from '@/utils/algorithmValidation'

const props = defineProps({
  streamId: {
    type: Number,
    required: true
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'reset'])

// 响应式数据
const formRef = ref()
const algorithms = ref([])
const submitting = ref(false)

// 表单数据
const formData = reactive({
  taskName: '',
  algorithmId: '',
  remark: '',
  ...props.initialData
})

// 算法配置数据
const algorithmConfig = ref({})

// 表单验证规则
const formRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        const result = validateTaskName(value)
        if (result.isValid) {
          callback()
        } else {
          callback(new Error(result.error))
        }
      }, 
      trigger: 'blur' 
    }
  ],
  algorithmId: [
    { required: true, message: '请选择算法', trigger: 'change' }
  ]
}

// 计算属性
const selectedAlgorithm = computed(() => {
  return algorithms.value.find(alg => alg.algorithm_id === formData.algorithmId)
})

// 方法
const loadAlgorithms = async () => {
  try {
    const response = await getAlgorithmCapabilities()
    if (response.code === 200) {
      algorithms.value = response.data
    }
  } catch (error) {
    console.error('加载算法列表失败:', error)
    ElMessage.error('加载算法列表失败')
  }
}

const onAlgorithmChange = () => {
  if (selectedAlgorithm.value) {
    // 重置算法配置为默认值
    algorithmConfig.value = { ...selectedAlgorithm.value.default_config }
    
    // 如果任务名称为空，生成默认名称
    if (!formData.taskName) {
      formData.taskName = generateDefaultTaskName(
        selectedAlgorithm.value.algorithm_name,
        selectedAlgorithm.value.algorithm_id
      )
    }
  }
}

const updateAlgorithmConfig = (newConfig) => {
  algorithmConfig.value = { ...newConfig }
}

const validateForm = async () => {
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

const handleSubmit = async () => {
  // 验证表单
  const isFormValid = await validateForm()
  if (!isFormValid) {
    return
  }
  
  submitting.value = true
  try {
    const submitData = {
      stream_id: props.streamId,
      task_name: formData.taskName,
      config: {
        algorithm_id: formData.algorithmId,
        algorithm_name: selectedAlgorithm.value?.algorithm_name || '',
        algorithm_version: selectedAlgorithm.value?.algorithm_version || '1.0.0',
        ...algorithmConfig.value
      },
      remark: formData.remark
    }
    
    emit('submit', submitData)
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  algorithmConfig.value = {}
  emit('reset')
}

// 监听器
watch(() => props.initialData, (newData) => {
  Object.assign(formData, newData)
}, { deep: true })

// 生命周期
onMounted(() => {
  loadAlgorithms()
})

// 暴露方法给父组件
defineExpose({
  validateForm,
  resetForm: handleReset,
  getFormData: () => formData,
  getAlgorithmConfig: () => algorithmConfig.value
})
</script>

<style scoped>
.algorithm-config-form {
  padding: 20px;
}

.algorithm-config-section {
  margin-top: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.algorithm-config-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.form-actions {
  margin-top: 24px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.form-actions .el-button {
  margin: 0 8px;
}
</style>
