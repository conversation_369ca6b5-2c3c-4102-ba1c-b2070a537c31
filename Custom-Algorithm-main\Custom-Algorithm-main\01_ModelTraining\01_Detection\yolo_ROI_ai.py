import warnings
# 屏蔽 FutureWarning
warnings.filterwarnings("ignore", category=FutureWarning)
import re

# 自定义过滤函数，忽略特定的 h264 警告
def ignore_h264_warnings(message, category, *args, **kwargs):
    # 使用正则表达式匹配特定的警告信息
    if re.search(r'\[h264 @ 0000[0-9a-f]+] (co located POCs unavailable|mmco: unref short failure)', str(message)):
        return
    warnings._showwarning(message, category, *args, **kwargs)
import torch
import cv2
import os
import numpy as np
import threading
import time
from datetime import datetime
import queue
from tkinter import *
from tkinter import ttk, messagebox, filedialog, simpledialog  # 添加simpledialog导入
from PIL import Image, ImageTk

# 全局变量
output_dir = 'intrusion_resultdir'
intrusion_dir = os.path.join(output_dir, '区域入侵检测')  # 创建专门的文件夹保存入侵事件图片
model_name = 'yolov5s'

# 创建输出目录和入侵检测目录
os.makedirs(output_dir, exist_ok=True)
os.makedirs(intrusion_dir, exist_ok=True)

# 入侵检测类
class IntrusionDetector:
    def __init__(self, location="监控区域1"):
        # 加载YOLOv5模型 - 使用本地模型，避免网络下载
        try:
            model_path = './yolov5s.pt'
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"本地模型文件不存在: {model_path}")
            print(f"正在加载本地YOLOv5模型: {model_path}")
            # 尝试多种加载方式
            model_loaded = False
            try:
                import torch.hub
                # 设置离线模式
                torch.hub.set_dir('./yolov5-master')
                self.model = torch.hub.load('./yolov5-master', 'custom', path=model_path, source='local', force_reload=False)
                print("成功使用torch.hub本地模式加载模型")
                model_loaded = True
            except Exception as e1:
                print(f"torch.hub本地模式加载失败: {e1}")
        except Exception as e:
            error_msg = str(e).lower()
            if ('timeout' in error_msg or 'timed out' in error_msg or 'signal' in error_msg or
                '超时' in error_msg or '网络超时' in error_msg or '连接超时' in error_msg or
                'remote end closed' in error_msg):
                print("网络连接问题，请确保使用本地模型")
                raise Exception("网络连接问题，请确保使用本地模型")
            else:
                print(f"模型加载失败: {e}")
                raise e
        self.running = False
        self.current_frame = None
        self.location = location
        self.tracking_objects = {}
        self.track_id = 0
        self.intrusion_events = []
        self.event_queue = queue.Queue()
        self.roi = None
        self.roi_mask = None
        self.roi_shape = 'rectangle'
        self.polygon_points = []
        self.conf_threshold = 0.25
        self.iou_threshold = 0.45
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(intrusion_dir, exist_ok=True)
    
    def start_detection(self, video_source):
        """启动检测线程"""
        self.running = True
        self.cap = cv2.VideoCapture(video_source)
        if not self.cap.isOpened():
            raise ValueError("无法打开视频源")
        self.thread = threading.Thread(target=self._detection_loop)
        self.thread.daemon = True
        self.thread.start()
    
    def _detection_loop(self):
        """检测主循环（仅适配 torch.hub.load 方式）"""
        real_fps = self.cap.get(cv2.CAP_PROP_FPS)
        print("RTSP 流真实帧率:", real_fps if real_fps > 0 else "无法获取，按帧间隔读取")

        frame_number = 0
        while self.running and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                break

            # 执行检测
            try:
                results = self.model(frame)
                detections = results.pandas().xyxy[0]
            except Exception as e:
                print("检测异常:", e)
                import pandas as pd
                detections = pd.DataFrame(columns=['xmin', 'ymin', 'xmax', 'ymax', 'confidence', 'class', 'name'])

            # 复制原始帧用于绘制
            processed_frame = frame.copy()

            # 绘制ROI边界
            if self.roi is not None and self.roi_mask is not None:
                self._draw_roi_boundary(processed_frame)

            # 目标跟踪与入侵检测
            current_objects = {}
            for _, det in detections.iterrows():
                if det['confidence'] < self.conf_threshold:
                    continue

                x1, y1, x2, y2 = int(det['xmin']), int(det['ymin']), int(det['xmax']), int(det['ymax'])
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                obj_class = det['name']

                # 判断是否在ROI内
                in_roi = False
                if self.roi_mask is not None and self.roi_mask[center_y, center_x] == 255:
                    in_roi = True
                if not in_roi:
                    continue

                # 绘制检测框
                cv2.rectangle(processed_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(processed_frame, f"{obj_class} {det['confidence']:.2f}",
                            (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                # 目标跟踪
                object_found = False
                for obj_id, pt in self.tracking_objects.items():
                    dist = np.sqrt((center_x - pt[0]) ** 2 + (center_y - pt[1]) ** 2)
                    if dist < 25:
                        self.tracking_objects[obj_id] = (center_x, center_y, obj_class)
                        current_objects[obj_id] = (center_x, center_y, obj_class)
                        object_found = True
                        break

                if not object_found:
                    self.track_id += 1
                    self.tracking_objects[self.track_id] = (center_x, center_y, obj_class)
                    current_objects[self.track_id] = (center_x, center_y, obj_class)
                    self.record_intrusion(self.track_id, obj_class, det['confidence'], processed_frame)

            self.tracking_objects = current_objects
            self.current_frame = processed_frame

            frame_number += 1
            if frame_number % 10 == 0:
                self.event_queue.put({'type': 'status', 'frame': frame_number, 'total_frames': -1})

        self.cap.release()
    
    def record_intrusion(self, obj_id, obj_class, confidence, frame):
        """记录入侵事件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = f"intrusion_{obj_class}_{timestamp}.jpg"
        # 保存到“区域入侵检测”文件夹下
        image_path = os.path.join(intrusion_dir, filename)

        if frame is not None:
            cv2.imwrite(image_path, frame)
        else:
            image_path = None

        event_data = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'object_class': obj_class,
            'confidence': confidence,
            'location': self.location,
            'image_path': image_path
        }
        self.intrusion_events.append(event_data)
        self.event_queue.put({'type': 'event', 'data': event_data})
        
        print(f"入侵事件 #{len(self.intrusion_events)} 检测到! 类别: {obj_class}, ID: {obj_id}")

    def detect_single_frame(self, frame, return_processed_frame=True):
        """
        WebSocket适配方法：处理单帧图像进行入侵检测（仅适配 torch.hub 加载方式）

        Args:
            frame: 输入的视频帧
            return_processed_frame: 是否返回处理后的帧（包含检测框和ROI）

        Returns:
            tuple: (processed_frame, detection_results, intrusion_detected)
        """
        if frame is None:
            return None, [], False

        try:
            # 执行YOLO检测（torch.hub 方式）
            results = self.model(frame)
            detections = results.pandas().xyxy[0]

            # 复制原始帧用于绘制
            processed_frame = frame.copy() if return_processed_frame else None
            detection_results = []
            intrusion_detected = False

            # 绘制ROI边界
            if return_processed_frame and self.roi is not None and self.roi_mask is not None:
                self._draw_roi_boundary(processed_frame)

            # 目标跟踪与入侵检测
            current_objects = {}
            for _, det in detections.iterrows():
                if det['confidence'] < self.conf_threshold:
                    continue

                x1, y1, x2, y2 = int(det['xmin']), int(det['ymin']), int(det['xmax']), int(det['ymax'])
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                obj_class = det['name']

                # 是否在ROI
                if self.roi_mask is None or self.roi_mask[center_y, center_x] != 255:
                    continue

                # 绘制框
                if return_processed_frame and processed_frame is not None:
                    cv2.rectangle(processed_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(processed_frame, f"{obj_class} {det['confidence']:.2f}",
                                (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                # 目标跟踪
                object_found = False
                for obj_id, pt in self.tracking_objects.items():
                    if np.linalg.norm([center_x - pt[0], center_y - pt[1]]) < 25:
                        self.tracking_objects[obj_id] = (center_x, center_y, obj_class)
                        current_objects[obj_id] = (center_x, center_y, obj_class)
                        object_found = True
                        break

                if not object_found:
                    self.track_id += 1
                    self.tracking_objects[self.track_id] = (center_x, center_y, obj_class)
                    current_objects[self.track_id] = (center_x, center_y, obj_class)
                    self.record_intrusion(self.track_id, obj_class, det['confidence'], processed_frame)
                    intrusion_detected = True

                detection_results.append({
                    'object_id': self.track_id if not object_found else None,
                    'class': obj_class,
                    'confidence': det['confidence'],
                    'bbox': [x1, y1, x2, y2],
                    'center': [center_x, center_y],
                    'in_roi': True,
                    'is_intrusion': not object_found
                })

            self.tracking_objects = current_objects
            if return_processed_frame:
                self.current_frame = processed_frame

            return processed_frame, detection_results, intrusion_detected

        except Exception as e:
            print("单帧检测异常:", e)
            return (frame if return_processed_frame else None), [], False

    def _draw_roi_boundary(self, frame):
        """绘制ROI边界"""
        if self.roi_shape == 'rectangle':
            x, y, w, h = self.roi
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
            cv2.putText(frame, 'ROI - Rectangle', (x, y - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        elif self.roi_shape == 'circle':
            center_x, center_y, radius = self.roi
            cv2.circle(frame, (center_x, center_y), radius, (0, 255, 0), 2)
            cv2.putText(frame, 'ROI - Circle', (center_x - 50, center_y - radius - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        elif self.roi_shape == 'ellipse':
            center_x, center_y, major_axis, minor_axis = self.roi
            cv2.ellipse(frame, (center_x, center_y), (major_axis, minor_axis), 0, 0, 360, (0, 255, 0), 2)
            cv2.putText(frame, 'ROI - Ellipse', (center_x - 70, center_y - major_axis - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        elif self.roi_shape == 'polygon':
            points = np.array(self.roi, np.int32)
            cv2.polylines(frame, [points], True, (0, 255, 0), 2)

    def set_roi_from_coordinates(self, roi_points, frame_width, frame_height, roi_shape='polygon'):
        """
        WebSocket适配方法：从坐标点设置ROI区域

        Args:
            roi_points: ROI坐标点列表 [[x1,y1], [x2,y2], ...]
            frame_width: 视频帧宽度
            frame_height: 视频帧高度
            roi_shape: ROI形状类型 ('rectangle', 'circle', 'ellipse', 'polygon')

        Returns:
            bool: 设置是否成功
        """
        try:
            print(f"[ROI设置] 开始设置ROI区域")
            print(f"[ROI设置] 视频尺寸: {frame_width}x{frame_height}")
            print(f"[ROI设置] ROI形状: {roi_shape}")
            print(f"[ROI设置] ROI点数: {len(roi_points) if roi_points else 0}")
            print(f"[ROI设置] ROI坐标: {roi_points}")

            if not roi_points or len(roi_points) < 2:
                print("ROI坐标点不足，至少需要2个点")
                return False

            self.roi_shape = roi_shape
            roi_mask = np.zeros((frame_height, frame_width), dtype=np.uint8)
            print(f"[ROI设置] 创建ROI掩码: {roi_mask.shape}")

            if roi_shape == 'rectangle' and len(roi_points) >= 2:
                # 矩形：使用前两个点确定矩形
                x1, y1 = roi_points[0]
                x2, y2 = roi_points[1]
                x, y = min(x1, x2), min(y1, y2)
                w, h = abs(x2 - x1), abs(y2 - y1)

                roi_mask[y:y+h, x:x+w] = 255
                self.roi = (x, y, w, h)
                print(f"设置矩形ROI: ({x}, {y}, {w}, {h})")

            elif roi_shape == 'circle' and len(roi_points) >= 2:
                # 圆形：第一个点为圆心，第二个点确定半径
                center_x, center_y = roi_points[0]
                edge_x, edge_y = roi_points[1]
                radius = int(np.sqrt((edge_x - center_x)**2 + (edge_y - center_y)**2))

                cv2.circle(roi_mask, (center_x, center_y), radius, 255, -1)
                self.roi = (center_x, center_y, radius)
                print(f"设置圆形ROI: 圆心({center_x}, {center_y}), 半径{radius}")

            elif roi_shape == 'ellipse' and len(roi_points) >= 2:
                # 椭圆：第一个点为中心，第二个点确定轴长
                center_x, center_y = roi_points[0]
                edge_x, edge_y = roi_points[1]
                major_axis = abs(edge_x - center_x)
                minor_axis = abs(edge_y - center_y)

                cv2.ellipse(roi_mask, (center_x, center_y), (major_axis, minor_axis), 0, 0, 360, 255, -1)
                self.roi = (center_x, center_y, major_axis, minor_axis)
                print(f"设置椭圆ROI: 中心({center_x}, {center_y}), 轴长({major_axis}, {minor_axis})")

            elif roi_shape == 'polygon':
                # 多边形：使用所有点
                points = np.array(roi_points, np.int32)
                print(f"[ROI设置] 多边形点数组: {points}")
                print(f"[ROI设置] 点数组形状: {points.shape}")
                cv2.fillPoly(roi_mask, [points], 255)
                self.roi = roi_points
                print(f"设置多边形ROI: {len(roi_points)}个点")

                # 验证ROI掩码
                roi_area = np.sum(roi_mask == 255)
                print(f"[ROI设置] ROI区域像素数: {roi_area}")
                if roi_area > 0:
                    # 找到ROI区域的边界
                    y_indices, x_indices = np.where(roi_mask == 255)
                    if len(y_indices) > 0 and len(x_indices) > 0:
                        min_x, max_x = np.min(x_indices), np.max(x_indices)
                        min_y, max_y = np.min(y_indices), np.max(y_indices)
                        print(f"[ROI设置] ROI边界: x({min_x}-{max_x}), y({min_y}-{max_y})")

            else:
                print(f"不支持的ROI形状: {roi_shape}")
                return False

            self.roi_mask = roi_mask
            print(f"ROI区域设置成功，形状: {roi_shape}")
            return True

        except Exception as e:
            print(f"设置ROI区域失败: {e}")
            return False

    def get_detection_statistics(self):
        """
        WebSocket适配方法：获取检测统计信息

        Returns:
            dict: 统计信息
        """
        return {
            'total_intrusions': len(self.intrusion_events),
            'current_tracking_objects': len(self.tracking_objects),
            'roi_configured': self.roi_mask is not None,
            'roi_shape': self.roi_shape if self.roi_mask is not None else None,
            'confidence_threshold': self.conf_threshold,
            'iou_threshold': self.iou_threshold
        }

    def set_roi(self, frame):
        """设置感兴趣区域"""
        height, width = frame.shape[:2]
        self.roi = None
        self.roi_mask = None
        self.polygon_points = []
        
        if self.roi_shape == 'rectangle':
            print("请在视频帧中拖拽鼠标选择矩形区域，选择完成后按空格键确认")
            roi = cv2.selectROI("选择矩形感兴趣区域 - 拖拽选择后按空格确认", frame, False)
            cv2.destroyAllWindows()

            if roi and roi[2] > 0 and roi[3] > 0:
                roi_mask = np.zeros((height, width), dtype=np.uint8)
                x, y, w, h = roi
                roi_mask[y:y+h, x:x+w] = 255
                self.roi = (x, y, w, h)
                self.roi_mask = roi_mask
                return True

        elif self.roi_shape == 'circle':
            print("请点击圆心位置，然后拖拽到边缘确定半径")
            roi = cv2.selectROI("选择圆形感兴趣区域 - 点击圆心拖拽到边缘", frame, False)
            cv2.destroyAllWindows()

            if roi and roi[2] > 0 and roi[3] > 0:
                center_x = roi[0] + roi[2] // 2
                center_y = roi[1] + roi[3] // 2
                radius = min(roi[2], roi[3]) // 2

                roi_mask = np.zeros((height, width), dtype=np.uint8)
                cv2.circle(roi_mask, (center_x, center_y), radius, 255, -1)
                self.roi = (center_x, center_y, radius)
                self.roi_mask = roi_mask
                return True

        elif self.roi_shape == 'ellipse':
            print("请点击椭圆中心，然后拖拽以确定轴的长度")
            roi = cv2.selectROI("选择椭圆感兴趣区域 - 点击中心拖拽到轴的长度", frame, False)
            cv2.destroyAllWindows()

            if roi and roi[2] > 0 and roi[3] > 0:
                center_x = roi[0] + roi[2] // 2
                center_y = roi[1] + roi[3] // 2
                major_axis = roi[2] // 2
                minor_axis = roi[3] // 2

                roi_mask = np.zeros((height, width), dtype=np.uint8)
                cv2.ellipse(roi_mask, (center_x, center_y), (major_axis, minor_axis), 0, 0, 360, 255, -1)
                self.roi = (center_x, center_y, major_axis, minor_axis)
                self.roi_mask = roi_mask
                return True

        elif self.roi_shape == 'polygon':
            print("请依次点击多边形的顶点，按空格键完成绘制，ESC键取消")
            temp_frame = frame.copy()
            cv2.namedWindow("设置感兴趣区域 (多边形)")
            cv2.imshow("设置感兴趣区域 (多边形)", temp_frame)
            
            self.polygon_points = []
            
            def mouse_callback(event, x, y, flags, param):
                if event == cv2.EVENT_LBUTTONDOWN:
                    self.polygon_points.append((x, y))
                    cv2.circle(temp_frame, (x, y), 5, (0, 255, 0), -1)
                    if len(self.polygon_points) > 1:
                        cv2.line(temp_frame, self.polygon_points[-2], self.polygon_points[-1], (0, 255, 0), 2)
                    cv2.imshow("设置感兴趣区域 (多边形)", temp_frame)
            
            cv2.setMouseCallback("设置感兴趣区域 (多边形)", mouse_callback)
            
            while True:
                key = cv2.waitKey(1) & 0xFF
                if key == 27:  # ESC键取消
                    self.polygon_points = []
                    break
                elif key == ord(' '):  # 空格键完成
                    if len(self.polygon_points) >= 3:
                        cv2.line(temp_frame, self.polygon_points[-1], self.polygon_points[0], (0, 255, 0), 2)
                        cv2.imshow("设置感兴趣区域 (多边形)", temp_frame)
                        break
            
            cv2.destroyAllWindows()

            if len(self.polygon_points) >= 3:
                roi_mask = np.zeros((height, width), dtype=np.uint8)
                points = np.array(self.polygon_points, np.int32)
                cv2.fillPoly(roi_mask, [points], 255)
                self.roi = self.polygon_points.copy()
                self.roi_mask = roi_mask
                return True
        
        return False
    
    def get_latest_event(self):
        """从队列中获取最新事件（非阻塞）"""
        try:
            return self.event_queue.get_nowait()
        except queue.Empty:
            return None
    
    def stop_detection(self):
        """停止检测"""
        self.running = False
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()
        if hasattr(self, 'thread'):
            self.thread.join(timeout=1.0)

# Tkinter GUI 界面
class IntrusionDetectionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("区域入侵检测系统 (YOLOv5) RTSP支持版")
        self.root.geometry("1200x800")
        
        # 初始化检测器
        self.detector = IntrusionDetector(location="监控区域1")
        
        # 当前视频源
        self.video_source = None
        
        # 全屏模式状态
        self.fullscreen_mode = False
        self.original_geometry = None
        
        # 创建界面布局
        self.create_widgets()
        
        # 启动视频更新
        self.update_video()
        
        # 启动事件更新
        self.update_events()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        self.video_frame = ttk.LabelFrame(main_frame, text="实时监控")
        self.video_frame.grid(row=0, column=0, padx=5, pady=5, sticky=NSEW, rowspan=2)
        
        # 视频工具栏
        video_toolbar = ttk.Frame(self.video_frame)
        video_toolbar.pack(fill=X, padx=5, pady=(5, 0))
        
        # 全屏按钮
        self.fullscreen_btn = ttk.Button(video_toolbar, text="全屏", command=self.toggle_fullscreen)
        self.fullscreen_btn.pack(side=RIGHT, padx=5)
        
        # 缩放控制
        zoom_frame = ttk.Frame(video_toolbar)
        zoom_frame.pack(side=LEFT)
        ttk.Label(zoom_frame, text="缩放:").pack(side=LEFT)
        self.zoom_var = DoubleVar(value=1.0)
        zoom_scale = ttk.Scale(zoom_frame, from_=0.5, to=2.0, variable=self.zoom_var, 
                              orient=HORIZONTAL, length=100, command=self.update_zoom)
        zoom_scale.pack(side=LEFT, padx=5)
        
        # 视频显示标签
        self.video_label = ttk.Label(self.video_frame)
        self.video_label.pack(padx=5, pady=5, fill=BOTH, expand=True)
        
        # 右侧面板
        right_panel = ttk.Frame(main_frame)
        right_panel.grid(row=0, column=1, padx=5, pady=5, sticky=NSEW)
        
        # 事件列表区域
        event_frame = ttk.LabelFrame(right_panel, text="入侵事件记录")
        event_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 创建事件列表
        columns = ("id", "timestamp", "object_class", "confidence", "location", "image_path")
        self.event_tree = ttk.Treeview(event_frame, columns=columns, show="headings", height=8)
        
        # 设置列标题
        self.event_tree.heading("id", text="ID")
        self.event_tree.heading("timestamp", text="时间")
        self.event_tree.heading("object_class", text="类别")
        self.event_tree.heading("confidence", text="置信度")
        self.event_tree.heading("location", text="位置")
        self.event_tree.heading("image_path", text="图片路径")
        
        # 设置列宽
        self.event_tree.column("id", width=50, anchor=CENTER)
        self.event_tree.column("timestamp", width=150)
        self.event_tree.column("object_class", width=100)
        self.event_tree.column("confidence", width=80, anchor=CENTER)
        self.event_tree.column("location", width=100)
        self.event_tree.column("image_path", width=200)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(event_frame, orient="vertical", command=self.event_tree.yview)
        self.event_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.event_tree.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 事件预览区域
        preview_frame = ttk.LabelFrame(event_frame, text="事件预览")
        preview_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        self.preview_label = ttk.Label(preview_frame, text="选择事件查看详情")
        self.preview_label.pack(padx=5, pady=5, fill=BOTH, expand=True)
        
        # 控制按钮区域
        self.control_frame = ttk.Frame(main_frame)
        self.control_frame.grid(row=1, column=1, pady=10, sticky=EW)
        
        # 视频选择按钮
        self.select_video_btn = ttk.Button(self.control_frame, text="选择视频文件", command=self.select_video)
        self.select_video_btn.pack(side=LEFT, padx=5)
        
        self.select_rtsp_btn = ttk.Button(self.control_frame, text="RTSP流连接", command=self.select_rtsp)
        self.select_rtsp_btn.pack(side=LEFT, padx=5)
        
        # ROI设置按钮
        roi_frame = ttk.Frame(self.control_frame)
        roi_frame.pack(side=LEFT, padx=5)
        
        # ROI形状选择
        self.shape_var = StringVar(value="rectangle")
        shape_menu = ttk.OptionMenu(roi_frame, self.shape_var, "rectangle", 
                                    "rectangle", "circle", "ellipse", "polygon")
        shape_menu.pack(side=LEFT, padx=2)
        
        self.roi_btn = ttk.Button(roi_frame, text="设置ROI区域", command=self.set_roi)
        self.roi_btn.pack(side=LEFT, padx=2)
        
        # 检测控制按钮
        self.start_btn = ttk.Button(self.control_frame, text="开始检测", command=self.start_detection, state=DISABLED)
        self.start_btn.pack(side=LEFT, padx=5)
        
        self.stop_btn = ttk.Button(self.control_frame, text="停止检测", command=self.stop_detection, state=DISABLED)
        self.stop_btn.pack(side=LEFT, padx=5)
        
        # 其他功能按钮
        self.settings_btn = ttk.Button(self.control_frame, text="参数设置", command=self.open_settings)
        self.settings_btn.pack(side=LEFT, padx=5)
        
        
        # 状态信息区域
        stats_info_frame = ttk.Frame(main_frame)
        stats_info_frame.grid(row=2, column=0, columnspan=2, padx=10, pady=5, sticky=EW)
        
        self.status_label = ttk.Label(stats_info_frame, text="状态: 未启动")
        self.status_label.pack(side=LEFT, padx=5)
        
        self.frame_label = ttk.Label(stats_info_frame, text="帧: 0/0")
        self.frame_label.pack(side=LEFT, padx=5)
        
        # 配置网格布局权重
        main_frame.columnconfigure(0, weight=3)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=3)
        main_frame.rowconfigure(1, weight=1)
        
        # 绑定事件选择事件
        self.event_tree.bind("<<TreeviewSelect>>", self.on_event_select)
    
    def toggle_fullscreen(self):
        """切换视频全屏模式"""
        if not self.fullscreen_mode:
            self.original_geometry = self.root.geometry()
            self.root.attributes("-fullscreen", True)
            self.control_frame.grid_forget()
            self.video_frame.grid(row=0, column=0, columnspan=2, sticky=NSEW, padx=0, pady=0)
            self.fullscreen_mode = True
            self.fullscreen_btn.config(text="退出全屏")
        else:
            self.root.attributes("-fullscreen", False)
            self.root.geometry(self.original_geometry)
            self.video_frame.grid(row=0, column=0, padx=5, pady=5, sticky=NSEW)
            self.control_frame.grid(row=1, column=1, pady=10, sticky=EW)
            self.fullscreen_mode = False
            self.fullscreen_btn.config(text="全屏")
    
    def update_zoom(self, value):
        """更新缩放比例 - 占位方法，实际缩放将在视频更新时应用"""
        pass
    
    def on_event_select(self, event):
        """当选择事件时显示预览"""
        selected = self.event_tree.selection()
        if selected:
            item = self.event_tree.item(selected[0])
            values = item['values']
            image_path = values[5] if len(values) > 5 else None
            
            if image_path and os.path.exists(image_path):
                try:
                    img = Image.open(image_path)
                    img.thumbnail((300, 300))  # 调整大小
                    img_tk = ImageTk.PhotoImage(img)
                    self.preview_label.img_tk = img_tk  # 保持引用
                    self.preview_label.config(image=img_tk, text="")
                except Exception as e:
                    self.preview_label.config(image=None, text=f"无法加载图片: {str(e)}")
            else:
                self.preview_label.config(image=None, text="无可用图片预览")
    
    def select_video(self):
        """选择视频文件"""
        video_path = filedialog.askopenfilename(
            filetypes=[("视频文件", "*.mp4 *.avi *.mov *.mkv")],
            title="选择视频文件"
        )
        if video_path:
            self.video_source = video_path
            self.start_btn.config(state=NORMAL)
            self.status_label.config(text=f"已选择视频: {os.path.basename(video_path)}")
    
    def select_rtsp(self):
        """输入RTSP流地址"""
        rtsp_url = simpledialog.askstring("RTSP流连接", "请输入RTSP流地址:")
        if rtsp_url:
            # 简单验证RTSP地址
            if "rtsp://" in rtsp_url:
                self.video_source = rtsp_url
                self.start_btn.config(state=NORMAL)
                self.status_label.config(text=f"已选择RTSP流: {rtsp_url}")
            else:
                messagebox.showwarning("警告", "请输入有效的RTSP地址，格式应为: rtsp://...")
    def set_roi(self):
        """设置感兴趣区域"""
        if self.video_source is None:
            messagebox.showwarning("警告", "请先选择视频或RTSP流")
            return
        
        # 对于本地视频文件
        if self.video_source.startswith("rtsp://"):
            rtsp_url = self.video_source
            cap = cv2.VideoCapture(rtsp_url)
            if not cap.isOpened():
                messagebox.showerror("错误", f"无法打开RTSP流: {rtsp_url}")
                return
            
            ret, frame = cap.read()
            if not ret:
                messagebox.showerror("错误", f"无法读取RTSP流: {rtsp_url}")
                cap.release()
                return
            cap.release()
        # 对于已打开的视频文件
        elif hasattr(self.detector, 'cap') and self.detector.cap.isOpened():
            ret, frame = self.detector.cap.read()
            if not ret:
                messagebox.showerror("错误", "无法读取当前视频")
                return
        else:
            # 对于未检测的视频文件
            cap = cv2.VideoCapture(self.video_source)
            if not cap.isOpened():
                messagebox.showerror("错误", f"无法打开视频文件: {self.video_source}")
                return
            
            ret, frame = cap.read()
            if not ret:
                messagebox.showerror("错误", f"无法读取视频文件: {self.video_source}")
                cap.release()
                return
            cap.release()
        
        self.detector.roi_shape = self.shape_var.get()
        success = self.detector.set_roi(frame)
        
        if success:
            messagebox.showinfo("ROI设置", "感兴趣区域设置成功！")
        else:
            messagebox.showwarning("ROI设置", "未设置ROI区域")
    
    def start_detection(self):
        """启动检测"""
        if self.video_source:
            try:
                self.detector.start_detection(self.video_source)
                self.start_btn.config(state=DISABLED)
                self.stop_btn.config(state=NORMAL)
                if self.video_source.startswith("rtsp://"):
                    self.status_label.config(text=f"状态: 正在RTSP流分析 - {self.video_source}")
                else:
                    self.status_label.config(text=f"状态: 正在检测 - {os.path.basename(self.video_source)}")
                messagebox.showinfo("系统启动", "区域入侵检测已启动")
            except Exception as e:
                messagebox.showerror("错误", f"启动失败: {str(e)}")
        else:
            messagebox.showwarning("警告", "请先选择视频文件或RTSP流")
    
    def stop_detection(self):
        """停止检测"""
        self.detector.stop_detection()
        self.start_btn.config(state=NORMAL)
        self.stop_btn.config(state=DISABLED)
        self.status_label.config(text="状态: 已停止")
        messagebox.showinfo("系统停止", "区域入侵检测已停止")
    
    def open_settings(self):
        """打开设置窗口"""
        settings_win = Toplevel(self.root)
        settings_win.title("参数设置")
        settings_win.geometry("400x300")
        
        ttk.Label(settings_win, text="置信度阈值:").pack(pady=(10, 0))
        conf_var = DoubleVar(value=self.detector.conf_threshold)
        conf_scale = ttk.Scale(settings_win, from_=0.1, to=0.9, variable=conf_var, 
                              orient=HORIZONTAL, length=300)
        conf_scale.pack(padx=20, pady=5, fill=X)
        conf_value_label = ttk.Label(settings_win, text=f"当前值: {conf_var.get():.2f}")
        conf_value_label.pack()
        
        ttk.Label(settings_win, text="IoU阈值:").pack(pady=(10, 0))
        iou_var = DoubleVar(value=self.detector.iou_threshold)
        iou_scale = ttk.Scale(settings_win, from_=0.1, to=0.9, variable=iou_var, 
                             orient=HORIZONTAL, length=300)
        iou_scale.pack(padx=20, pady=5, fill=X)
        iou_value_label = ttk.Label(settings_win, text=f"当前值: {iou_var.get():.2f}")
        iou_value_label.pack()
        
        ttk.Label(settings_win, text="位置信息:").pack(pady=(10, 0))
        location_var = StringVar(value=self.detector.location)
        location_entry = ttk.Entry(settings_win, textvariable=location_var, width=30)
        location_entry.pack(padx=20, pady=5, fill=X)
        
        def save_settings():
            self.detector.conf_threshold = conf_var.get()
            self.detector.iou_threshold = iou_var.get()
            self.detector.location = location_var.get()
            messagebox.showinfo("设置保存", "参数已更新")
            settings_win.destroy()
        
        ttk.Button(settings_win, text="保存", command=save_settings).pack(pady=20)
    
    
    def update_video(self):
        """更新视频画面，添加缩放支持"""
        if hasattr(self.detector, 'current_frame') and self.detector.current_frame is not None:
            frame = self.detector.current_frame
            
            zoom_factor = self.zoom_var.get()
            if zoom_factor != 1.0:
                h, w = frame.shape[:2]
                new_w = int(w * zoom_factor)
                new_h = int(h * zoom_factor)
                frame = cv2.resize(frame, (new_w, new_h))
            
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()
            if label_width < 10 or label_height < 10:
                label_width = 800
                label_height = 600
            
            h, w = frame.shape[:2]
            if w > 0 and h > 0:
                scale = min(label_width / w, label_height / h)
                if scale < 1.0:
                    new_w = int(w * scale)
                    new_h = int(h * scale)
                    if new_w > 0 and new_h > 0:
                        frame = cv2.resize(frame, (new_w, new_h))
            
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(frame)
            img_tk = ImageTk.PhotoImage(image=img)
            
            self.video_label.img_tk = img_tk  # 保持引用
            self.video_label.config(image=img_tk)
        
        self.root.after(30, self.update_video)
    
    def update_events(self):
        """更新事件列表"""
        new_event = self.detector.get_latest_event()
        if new_event and new_event['type'] == 'event':
            event_data = new_event['data']
            self.event_tree.insert("", "0", values=(
                event_data['id'],
                event_data['timestamp'],
                event_data['object_class'],
                f"{event_data['confidence']:.2f}",
                event_data['location'],
                event_data['image_path']
            ))
            if len(self.event_tree.get_children()) > 10:
                last_item = self.event_tree.get_children()[-1]
                self.event_tree.delete(last_item)
        
        self.root.after(500, self.update_events)
    
    def on_closing(self):
        """关闭窗口时的处理"""
        self.detector.stop_detection()
        self.root.destroy()

# 主程序入口
if __name__ == "__main__":
    try:
        import torch

    except ImportError as e:
        print(f"缺少必要的库: {e}")
        print("请安装: pip install torch torchvision mysql-connector-python")
        exit(1)
    
    root = Tk()
    app = IntrusionDetectionApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()