from datetime import datetime
from fastapi import APIRouter, Depends, Form, Request, Query
from pydantic_validation_decorator import ValidateFields
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_stream.service.stream_service import StreamService
from module_stream.entity.vo.stream_vo import DeleteStreamModel, StreamModel, StreamPageQueryModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


streamController = APIRouter(prefix='/stream_manage/stream', dependencies=[Depends(LoginService.get_current_user)])

# 算法配置相关的路由器，不需要认证
algorithmController = APIRouter(prefix='/stream_manage/stream')

@streamController.get('/algorithms')
async def get_available_algorithms():
    """
    获取可用的算法列表

    :return: 算法列表
    """
    logger.info('获取算法列表')

    try:
        from module_stream.service.algorithm_config_service import AlgorithmConfigService

        algorithms = await AlgorithmConfigService.get_available_algorithms()

        # 转换为字典格式返回
        algorithm_list = []
        for alg in algorithms:
            algorithm_list.append({
                "id": alg.id,
                "name": alg.name,
                "description": alg.description,
                "version": alg.version,
                "group": alg.group,
                "config_path": alg.config_path,
                "postprocessor_config": alg.postprocessor_config
            })

        return ResponseUtil.success(data=algorithm_list)

    except Exception as e:
        logger.error(f'获取算法列表失败: {e}')
        return ResponseUtil.error(msg='获取算法列表失败')


@streamController.post('/algorithm-config')
async def save_algorithm_config(
    stream_id: int = Form(..., description='视频流ID'),
    algorithm_id: str = Form(..., description='算法ID'),
    algorithm_name: str = Form(default='', description='算法名称'),
    algorithm_version: str = Form(default='', description='算法版本'),
    task_name: str = Form(default='', description='任务名称'),
    user_config: str = Form(..., description='用户配置JSON'),
    remark: str = Form(default='', description='备注'),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    保存算法配置

    :param stream_id: 视频流ID
    :param algorithm_id: 算法ID
    :param algorithm_name: 算法名称
    :param algorithm_version: 算法版本
    :param task_name: 任务名称
    :param user_config: 用户配置JSON字符串
    :param remark: 备注
    :param current_user: 当前用户
    :param query_db: 数据库会话
    :return: 保存结果
    """
    logger.info(f'用户 {current_user.user.user_name} 保存算法配置: stream_id={stream_id}, algorithm_id={algorithm_id}')

    try:
        from module_stream.service.algorithm_config_service import AlgorithmConfigService
        from module_stream.entity.vo.algorithm_config_vo import SaveAlgorithmConfigModel
        from exceptions.exception import ServiceException
        import json

        # 参数验证
        validation_errors = []

        if not stream_id:
            validation_errors.append('视频流ID不能为空')

        if not algorithm_id or algorithm_id.strip() == '':
            validation_errors.append('算法ID不能为空')

        if not user_config or user_config.strip() == '':
            validation_errors.append('用户配置不能为空')

        # 如果有基本参数错误，直接返回
        if validation_errors:
            return ResponseUtil.error(msg='；'.join(validation_errors))

        # 解析用户配置JSON
        try:
            user_config_dict = json.loads(user_config)
        except json.JSONDecodeError as e:
            logger.error(f'用户配置JSON解析失败: {e}')
            return ResponseUtil.error(msg='用户配置格式错误，请检查JSON格式')

        # 验证配置内容
        config_validation_errors = []

        # 检查必要的配置字段
        if not user_config_dict.get('basicParams'):
            config_validation_errors.append('配置缺少必要字段: basicParams')
        else:
            basic_params = user_config_dict['basicParams']

            # 检查告警窗口配置
            if not basic_params.get('alert_window'):
                config_validation_errors.append('配置缺少告警窗口设置')

            # 检查区域配置
            if not basic_params.get('bbox'):
                config_validation_errors.append('配置缺少区域设置')

            # 检查计划配置
            if not basic_params.get('plan'):
                config_validation_errors.append('配置缺少计划设置')

        # 验证模型参数
        if 'model_parameters' in user_config_dict:
            model_params = user_config_dict['model_parameters']
            if 'confidence_threshold' in model_params:
                confidence = model_params['confidence_threshold']
                if not (0 <= confidence <= 1):
                    config_validation_errors.append('置信度阈值必须在0-1之间')

            if 'nms_threshold' in model_params:
                nms = model_params['nms_threshold']
                if not (0 <= nms <= 1):
                    config_validation_errors.append('NMS阈值必须在0-1之间')

            if 'input_size' in model_params:
                input_size = model_params['input_size']
                if input_size <= 0:
                    config_validation_errors.append('输入图像尺寸必须大于0')

            if 'max_detections' in model_params:
                max_det = model_params['max_detections']
                if max_det <= 0:
                    config_validation_errors.append('最大检测数量必须大于0')

        # 验证告警参数
        if 'alert_parameters' in user_config_dict:
            alert_params = user_config_dict['alert_parameters']
            if 'alert_interval' in alert_params:
                interval = alert_params['alert_interval']
                if interval <= 0:
                    config_validation_errors.append('告警间隔必须大于0')

            if 'alert_threshold' in alert_params:
                threshold = alert_params['alert_threshold']
                if threshold <= 0:
                    config_validation_errors.append('告警阈值必须大于0')

        if config_validation_errors:
            return ResponseUtil.error(msg='；'.join(config_validation_errors))

        # 处理空字符串参数，设置默认值
        algorithm_name = algorithm_name.strip() if algorithm_name else algorithm_id.strip()
        algorithm_version = algorithm_version.strip() if algorithm_version else '1.0.0'
        task_name = task_name.strip() if task_name else f'{algorithm_name}检测任务'
        remark = remark.strip() if remark else ''

        # 构建配置模型
        try:
            config_model = SaveAlgorithmConfigModel(
                stream_id=stream_id,
                algorithm_id=algorithm_id.strip(),
                algorithm_name=algorithm_name,
                algorithm_version=algorithm_version,
                task_name=task_name,
                user_config=user_config_dict,
                remark=remark
            )

            # 验证字段
            config_model.validate_fields()

        except ValueError as e:
            logger.error(f'配置模型验证失败: {e}')
            return ResponseUtil.error(msg=str(e))

        # 保存配置
        result = await AlgorithmConfigService.save_algorithm_config(
            query_db, config_model, current_user.user.user_id
        )

        if result:
            return ResponseUtil.success(msg='算法配置保存成功')
        else:
            return ResponseUtil.error(msg='算法配置保存失败')

    except ServiceException as e:
        logger.error(f'业务异常: {e.message}')
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f'保存算法配置失败: {e}')
        return ResponseUtil.error(msg=f'保存失败: {str(e)}')


@streamController.get('/algorithm-config/{stream_id}/{algorithm_id}')
async def get_algorithm_config(
    stream_id: int,
    algorithm_id: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取算法配置

    :param stream_id: 视频流ID
    :param algorithm_id: 算法ID
    :param current_user: 当前用户
    :param query_db: 数据库会话
    :return: 配置信息
    """
    logger.info(f'用户 {current_user.user.user_name} 获取算法配置: stream_id={stream_id}, algorithm_id={algorithm_id}')

    try:
        from module_stream.service.algorithm_config_service import AlgorithmConfigService

        config = await AlgorithmConfigService.get_algorithm_config(query_db, stream_id, algorithm_id)

        if config:
            # 解析用户配置JSON
            import json
            user_config = json.loads(config.user_config) if config.user_config else {}

            result = {
                "config_id": config.config_id,
                "stream_id": config.stream_id,
                "algorithm_id": config.algorithm_id,
                "algorithm_name": config.algorithm_name,
                "algorithm_version": config.algorithm_version,
                "user_config": user_config,
                "status": config.status,
                "remark": config.remark
            }

            return ResponseUtil.success(data=result)
        else:
            return ResponseUtil.success(data=None, msg='未找到配置')

    except Exception as e:
        logger.error(f'获取算法配置失败: {e}')
        return ResponseUtil.error(msg='获取配置失败')

@streamController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:list'))]
)
async def get_stream_manage_stream_list(
    request: Request,
    pageNum: int = Query(1, description='当前页码'),
    pageSize: int = Query(10, description='每页记录数'),
    streamName: Optional[str] = Query(None, description='流名称'),
    rtspUrl: Optional[str] = Query(None, description='RTSP地址'),
    location: Optional[str] = Query(None, description='安装位置'),
    status: Optional[str] = Query(None, description='状态'),
    isRecording: Optional[int] = Query(None, description='是否录制'),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # 调试：打印接收到的参数
    logger.info(f'接收到的搜索参数: pageNum={pageNum}, pageSize={pageSize}, streamName={streamName}, rtspUrl={rtspUrl}, location={location}, status={status}, isRecording={isRecording}')

    # 构建查询参数对象 - 使用驼峰命名创建对象
    stream_page_query = StreamPageQueryModel(
        pageNum=pageNum,
        pageSize=pageSize,
        userId=current_user.user.user_id,  # 添加当前用户ID
        streamName=streamName,
        rtspUrl=rtspUrl,
        location=location,
        status=status,
        isRecording=isRecording
    )

    # 调试：打印查询对象
    logger.info(f'构建的查询对象: {stream_page_query.model_dump()}')
    # 获取分页数据
    stream_page_query_result = await StreamService.get_stream_list_services(query_db, stream_page_query, current_user.user.user_id, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=stream_page_query_result)


@streamController.post('', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:add'))])
@ValidateFields(validate_model='add_stream')
@Log(title='视频流管理', business_type=BusinessType.INSERT)
async def add_stream_manage_stream(
    request: Request,
    add_stream: StreamModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_stream.user_id = current_user.user.user_id  # 设置当前用户ID
    add_stream.create_by = current_user.user.user_name
    add_stream.create_time = datetime.now()
    add_stream.update_by = current_user.user.user_name
    add_stream.update_time = datetime.now()
    add_stream_result = await StreamService.add_stream_services(query_db, add_stream)
    logger.info(add_stream_result.message)

    return ResponseUtil.success(msg=add_stream_result.message)


@streamController.put('', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:edit'))])
@ValidateFields(validate_model='edit_stream')
@Log(title='视频流管理', business_type=BusinessType.UPDATE)
async def edit_stream_manage_stream(
    request: Request,
    edit_stream: StreamModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    edit_stream.update_by = current_user.user.user_name
    edit_stream.update_time = datetime.now()
    edit_stream_result = await StreamService.edit_stream_services(query_db, edit_stream)
    logger.info(edit_stream_result.message)

    return ResponseUtil.success(msg=edit_stream_result.message)


@streamController.delete('/{stream_ids}', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:remove'))])
@Log(title='视频流管理', business_type=BusinessType.DELETE)
async def delete_stream_manage_stream(
    request: Request,
    stream_ids: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    delete_stream = DeleteStreamModel(streamIds=stream_ids)
    delete_stream_result = await StreamService.delete_stream_services(query_db, delete_stream, current_user.user.user_id)
    logger.info(delete_stream_result.message)

    return ResponseUtil.success(msg=delete_stream_result.message)


@streamController.get(
    '/{stream_id}', response_model=StreamModel, dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:query'))]
)
async def query_detail_stream_manage_stream(request: Request, stream_id: int, query_db: AsyncSession = Depends(get_db)):
    stream_detail_result = await StreamService.stream_detail_services(query_db, stream_id)
    logger.info(f'获取stream_id为{stream_id}的信息成功')

    return ResponseUtil.success(data=stream_detail_result)


@streamController.post('/export', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:export'))])
@Log(title='视频流管理', business_type=BusinessType.EXPORT)
async def export_stream_manage_stream_list(
    request: Request,
    stream_name: Optional[str] = Form(None, description='流名称'),
    rtsp_url: Optional[str] = Form(None, description='RTSP地址'),
    location: Optional[str] = Form(None, description='安装位置'),
    status: Optional[str] = Form(None, description='状态'),
    is_recording: Optional[int] = Form(None, description='是否录制'),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # 构建查询参数对象
    stream_page_query = StreamPageQueryModel(
        user_id=current_user.user.user_id,  # 添加当前用户ID
        stream_name=stream_name,
        rtsp_url=rtsp_url,
        location=location,
        status=status,
        is_recording=is_recording
    )
    # 获取全量数据
    stream_query_result = await StreamService.get_stream_list_services(query_db, stream_page_query, current_user.user.user_id, is_page=False)
    stream_export_result = await StreamService.export_stream_list_services(stream_query_result)
    logger.info('导出成功')

    return ResponseUtil.streaming(data=bytes2file_response(stream_export_result))


@streamController.post('/test', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:query'))])
async def test_rtsp_connection(
    request: Request,
    rtsp_url: str = Form(..., description='RTSP地址'),
    timeout: int = Form(5, description='超时时间（秒）'),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    测试RTSP连接

    :param request: 请求对象
    :param rtsp_url: RTSP地址
    :param timeout: 超时时间
    :param current_user: 当前用户
    :return: 测试结果
    """
    logger.info(f'用户 {current_user.user.user_name} 测试RTSP连接: {rtsp_url}')

    test_result = await StreamService.test_rtsp_connection_services(rtsp_url, timeout)

    if test_result['success']:
        logger.info(f'RTSP连接测试成功: {rtsp_url}')
        return ResponseUtil.success(data=test_result['data'], msg=test_result['message'])
    else:
        logger.warning(f'RTSP连接测试失败: {rtsp_url}, 错误: {test_result["message"]}')
        return ResponseUtil.error(msg=test_result['message'])


@streamController.post('/frame', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:stream:query'))])
async def get_rtsp_frame(
    request: Request,
    rtsp_url: str = Form(..., description='RTSP地址'),
    timeout: int = Form(5, description='超时时间（秒）'),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取RTSP视频流单帧

    :param request: 请求对象
    :param rtsp_url: RTSP地址
    :param timeout: 超时时间
    :param current_user: 当前用户
    :return: 视频帧的base64编码
    """
    logger.info(f'用户 {current_user.user.user_name} 获取RTSP视频帧: {rtsp_url}')

    frame_result = await StreamService.get_rtsp_frame_services(rtsp_url, timeout)

    if frame_result['success']:
        logger.info(f'获取RTSP视频帧成功: {rtsp_url}')
        return ResponseUtil.success(data={'frame': frame_result['frame']}, msg=frame_result['message'])
    else:
        logger.warning(f'获取RTSP视频帧失败: {rtsp_url}, 错误: {frame_result["message"]}')
        return ResponseUtil.error(msg=frame_result['message'])





@algorithmController.post('/algorithm-config')
async def save_algorithm_config(
    stream_id: int = Form(..., description='视频流ID'),
    algorithm_id: str = Form(..., description='算法ID'),
    config: str = Form(..., description='算法配置JSON'),
):
    """
    保存算法配置

    :param stream_id: 视频流ID
    :param algorithm_id: 算法ID
    :param config: 配置JSON字符串
    :return: 保存结果
    """
    logger.info(f'保存算法配置: stream_id={stream_id}, algorithm_id={algorithm_id}')

    try:
        # 解析配置JSON
        import json
        config_data = json.loads(config)

        # 验证配置格式
        if 'basicParams' not in config_data:
            return ResponseUtil.error(msg='配置缺少必要字段: basicParams')

        # 这里可以保存到数据库或文件
        # 暂时保存到临时目录用于测试
        import os
        config_dir = os.path.join(os.getcwd(), 'temp_configs')
        os.makedirs(config_dir, exist_ok=True)

        config_file = os.path.join(config_dir, f'stream_{stream_id}_{algorithm_id}.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        logger.info(f'算法配置已保存到: {config_file}')
        return ResponseUtil.success(msg='算法配置保存成功')

    except json.JSONDecodeError:
        return ResponseUtil.error(msg='配置格式错误')
    except Exception as e:
        logger.error(f'保存算法配置失败: {e}')
        return ResponseUtil.error(msg='保存失败')


@algorithmController.post('/add-algorithm')
async def add_new_algorithm(
    algorithm_url: str = Form(..., description='算法包下载地址'),
    algorithm_name: str = Form(..., description='算法名称'),
    version: str = Form(default='KS968', description='算法版本'),
):
    """
    动态添加新算法

    :param algorithm_url: 算法包下载地址
    :param algorithm_name: 算法名称
    :param version: 算法版本
    :return: 添加结果
    """
    logger.info(f'添加新算法: {algorithm_name}, 版本: {version}, 地址: {algorithm_url}')

    try:
        import os
        import requests
        import zipfile
        import tempfile

        # Custom-Algorithm目录路径
        custom_algorithm_path = os.path.join(os.getcwd(), '..', '..', 'Custom-Algorithm-main', 'Custom-Algorithm-main', '02_CustomAlgorithm', '02_Demo', version)

        # 确保目录存在
        os.makedirs(custom_algorithm_path, exist_ok=True)

        # 算法目录路径
        algorithm_dir = os.path.join(custom_algorithm_path, algorithm_name)

        if os.path.exists(algorithm_dir):
            return ResponseUtil.error(msg=f'算法 {algorithm_name} 已存在')

        # 下载算法包
        logger.info(f'开始下载算法包: {algorithm_url}')
        response = requests.get(algorithm_url, timeout=30)
        response.raise_for_status()

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            temp_file.write(response.content)
            temp_zip_path = temp_file.name

        try:
            # 解压算法包
            with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                zip_ref.extractall(algorithm_dir)

            # 验证算法包结构
            required_dirs = ['model', 'postprocessor']
            for req_dir in required_dirs:
                if not os.path.exists(os.path.join(algorithm_dir, req_dir)):
                    # 清理已创建的目录
                    import shutil
                    shutil.rmtree(algorithm_dir)
                    return ResponseUtil.error(msg=f'算法包结构不正确，缺少 {req_dir} 目录')

            # 检查配置文件
            config_file = os.path.join(algorithm_dir, 'postprocessor', f'{algorithm_name}.json')
            if not os.path.exists(config_file):
                return ResponseUtil.error(msg=f'算法包缺少配置文件: {algorithm_name}.json')

            logger.info(f'算法 {algorithm_name} 添加成功')
            return ResponseUtil.success(msg=f'算法 {algorithm_name} 添加成功')

        finally:
            # 清理临时文件
            os.unlink(temp_zip_path)

    except requests.RequestException as e:
        logger.error(f'下载算法包失败: {e}')
        return ResponseUtil.error(msg='下载算法包失败')
    except zipfile.BadZipFile:
        return ResponseUtil.error(msg='算法包格式错误')
    except Exception as e:
        logger.error(f'添加算法失败: {e}')
        return ResponseUtil.error(msg='添加算法失败')


@algorithmController.get('/algorithm-template')
async def get_algorithm_template():
    """
    获取算法配置模板

    :return: 算法配置模板
    """
    template = {
        "basicParams": {
            "alert_window": {
                "type": "interval_threshold_window",
                "interval": 5,
                "length": 5,
                "threshold": 3
            },
            "bbox": {
                "polygons": [],
                "lines": []
            },
            "model_args": {
                "your_model_name": {
                    "conf_thres": 0.65
                }
            },
            "reserved_args": {
                "ch_name": "算法名称",
                "sound_text": "告警语音",
                "strategy": "bottom"
            }
        },
        "renderParams": {
            "alert_window": {
                "interval": {
                    "label": "告警间隔",
                    "unit": "秒",
                    "type": "number",
                    "range": {"min": 0, "step": 1, "max": 99999999}
                },
                "length": {
                    "label": "告警窗口长度",
                    "unit": "次",
                    "type": "number",
                    "range": {"min": 0, "step": 1, "max": 100}
                },
                "threshold": {
                    "label": "告警阈值",
                    "unit": "次",
                    "type": "number",
                    "range": {"min": 0, "step": 1, "max": 100}
                }
            },
            "model_args": {
                "your_model_name": {
                    "conf_thres": {
                        "label": "检测置信度",
                        "unit": "",
                        "type": "number",
                        "range": {"min": 0, "step": 0.01, "max": 1}
                    }
                }
            },
            "reserved_args": {
                "strategy": {
                    "label": "检测策略",
                    "type": "select",
                    "options": [
                        {"label": "顶部", "value": "top"},
                        {"label": "中心", "value": "center"},
                        {"label": "底部", "value": "bottom"},
                        {"label": "左侧", "value": "left"},
                        {"label": "右侧", "value": "right"}
                    ]
                }
            },
            "bbox": {
                "polygons": {
                    "exits": "must",
                    "max": -1,
                    "edge": -1
                }
            }
        }
    }

    return ResponseUtil.success(data=template)
