"""
D:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo\KS968\person_intrusion\postprocessor\postprocessor.py
智驱力算法包后处理器基类
完整实现智驱力标准接口
"""

import json
import time
import math
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional


class Postprocessor(ABC):
    """智驱力后处理器基类"""

    def __init__(self, source_id: int = 0, alg_name: str = ""):
        self.source_id = source_id
        self.alg_name = alg_name

        # 智驱力标准属性
        self.reserved_args = {}
        self.alert_window = {}
        self.model_args = {}
        self.polygons = {}
        self.lines = {}
        self.frame_interval = 1

        # 颜色配置
        self.alert_color = [255, 0, 0]      # 红色
        self.non_alert_color = [0, 255, 0]  # 绿色

        # 告警控制
        self.last_alert_time = 0
        self.alert_interval = 5

        # 策略
        self.strategy = None

    @abstractmethod
    def _process(self, result: Dict, filter_result: Dict) -> bool:
        """
        处理检测结果的抽象方法

        Args:
            result: 检测结果字典，会被修改
            filter_result: 过滤后的结果

        Returns:
            bool: 处理是否成功
        """
        pass

    @abstractmethod
    def _filter(self, model_name: str, model_data: Dict) -> List[Dict]:
        """
        过滤检测结果的抽象方法

        Args:
            model_name: 模型名称
            model_data: 模型数据

        Returns:
            List[Dict]: 过滤后的检测框列表
        """
        pass

    def process(self, model_data: Dict, result: Dict) -> bool:
        """
        智驱力标准处理方法

        Args:
            model_data: 模型数据字典
            result: 结果字典，会被修改

        Returns:
            bool: 处理是否成功
        """
        try:
            # 过滤检测结果
            filter_result = {}
            for model_name, data in model_data.items():
                filter_result[model_name] = self._filter(model_name, data)

            # 调用具体的处理逻辑
            return self._process(result, filter_result)

        except Exception as e:
            print(f"[ERROR] 后处理失败: {e}")
            result['hit'] = False
            return False

    def set_args(self, args: Dict):
        """设置参数"""
        if 'reserved_args' in args:
            self.reserved_args = args['reserved_args']
        if 'alert_window' in args:
            self.alert_window = args['alert_window']
        if 'model_args' in args:
            self.model_args = args['model_args']
        if 'polygons' in args:
            self.polygons = args['polygons']
        if 'lines' in args:
            self.lines = args['lines']
        if 'model_conf' in args:
            self.model_conf = args['model_conf']

    def _gen_polygons(self) -> Dict:
        """生成多边形"""
        return self.polygons if self.polygons else {}

    def _gen_lines(self) -> Dict:
        """生成线段"""
        return self.lines if self.lines else {}

    def _filter_by_conf(self, model_conf: Dict, conf: float) -> bool:
        """根据置信度过滤"""
        conf_thres = model_conf.get('conf_thres', 0.25)
        return conf >= conf_thres

    def _filter_by_label(self, model_conf: Dict, label: int) -> Optional[str]:
        """根据标签过滤"""
        labels = model_conf.get('labels', [])
        if not labels:
            return f"class_{label}"

        if label < len(labels):
            return labels[label]
        return None

    def _scale(self, xyxy: List[int]) -> List[int]:
        """坐标缩放（默认不缩放）"""
        return xyxy

    def _gen_rectangle(self, xyxy: List[int], color: List[int], label: str, conf: float) -> Dict:
        """生成矩形框"""
        return {
            'xyxy': xyxy,
            'color': color,
            'label': label,
            'conf': conf
        }

    def _is_rectangle_in_polygon(self, xyxy: List[int], polygon: List[List[int]], strategy: str = 'center') -> bool:
        """判断矩形是否在多边形内"""
        x1, y1, x2, y2 = xyxy

        if strategy == 'center':
            # 中心点策略
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            return self._point_in_polygon(center_x, center_y, polygon)
        elif strategy == 'overlap':
            # 重叠策略
            return self._rectangle_overlap_polygon(xyxy, polygon)
        elif strategy == 'full':
            # 完全包含策略
            return self._rectangle_fully_in_polygon(xyxy, polygon)
        else:
            # 默认中心点策略
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            return self._point_in_polygon(center_x, center_y, polygon)

    def _point_in_polygon(self, x: int, y: int, polygon: List[List[int]]) -> bool:
        """判断点是否在多边形内（射线法）"""
        n = len(polygon)
        inside = False

        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _rectangle_overlap_polygon(self, xyxy: List[int], polygon: List[List[int]]) -> bool:
        """判断矩形是否与多边形重叠"""
        x1, y1, x2, y2 = xyxy
        corners = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]

        for x, y in corners:
            if self._point_in_polygon(x, y, polygon):
                return True
        return False

    def _rectangle_fully_in_polygon(self, xyxy: List[int], polygon: List[List[int]]) -> bool:
        """判断矩形是否完全在多边形内"""
        x1, y1, x2, y2 = xyxy
        corners = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]

        for x, y in corners:
            if not self._point_in_polygon(x, y, polygon):
                return False
        return True

    def _check_alert_interval(self) -> bool:
        """检查告警间隔"""
        current_time = time.time()
        if current_time - self.last_alert_time >= self.alert_interval:
            self.last_alert_time = current_time
            return True
        return False
