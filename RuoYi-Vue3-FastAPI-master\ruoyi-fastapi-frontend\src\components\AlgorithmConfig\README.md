# 算法配置组件使用说明

## 概述

本目录包含了通用的算法配置组件和验证工具，可以在所有需要算法配置的页面中复用。

## 组件结构

```
AlgorithmConfig/
├── index.vue                    # 原有的完整算法配置组件
├── AlgorithmConfigForm.vue      # 新的通用表单组件
├── PolygonEditor.vue           # 多边形编辑器
├── README.md                   # 使用说明
└── examples/                   # 使用示例
```

## 验证工具

### `utils/algorithmValidation.js`

提供了以下验证功能：

- `validateTaskName(taskName)` - 验证任务名称
- `validateBasicConfig(config)` - 验证基本配置
- `validateModelParameters(modelParams)` - 验证模型参数
- `validateAlertParameters(alertParams)` - 验证告警参数
- `validateGeometryConfig(config, algorithmCapability)` - 验证几何配置
- `validateAlgorithmConfig(config, algorithmCapability)` - 完整配置验证
- `generateDefaultTaskName(algorithmName, algorithmId)` - 生成默认任务名称
- `formatValidationErrors(errors)` - 格式化错误信息

## 使用方式

### 1. 使用完整的算法配置组件

```vue
<template>
  <AlgorithmConfig 
    :stream-id="streamId"
    @config-saved="onConfigSaved"
  />
</template>

<script setup>
import AlgorithmConfig from '@/components/AlgorithmConfig/index.vue'

const streamId = ref(1)

const onConfigSaved = (data) => {
  console.log('配置已保存:', data)
}
</script>
```

### 2. 使用通用表单组件

```vue
<template>
  <AlgorithmConfigForm 
    :stream-id="streamId"
    :initial-data="initialData"
    @submit="onSubmit"
    @reset="onReset"
  >
    <template #algorithm-config="{ algorithm, config, updateConfig }">
      <!-- 自定义算法配置界面 -->
      <div v-if="algorithm.algorithm_id === 'face_detection'">
        <h5>人脸检测配置</h5>
        <el-form-item label="最小人脸尺寸">
          <el-input-number 
            :model-value="config.min_face_size || 20"
            @update:model-value="updateConfig({ ...config, min_face_size: $event })"
            :min="10" 
            :max="200" 
          />
        </el-form-item>
      </div>
      
      <div v-else-if="algorithm.algorithm_id === 'vehicle_detection'">
        <h5>车辆检测配置</h5>
        <el-form-item label="车辆类型">
          <el-select 
            :model-value="config.vehicle_types || []"
            @update:model-value="updateConfig({ ...config, vehicle_types: $event })"
            multiple
          >
            <el-option label="小汽车" value="car" />
            <el-option label="卡车" value="truck" />
            <el-option label="摩托车" value="motorcycle" />
          </el-select>
        </el-form-item>
      </div>
      
      <!-- 通用配置 -->
      <div class="common-config">
        <h5>通用参数</h5>
        <el-form-item label="置信度阈值">
          <el-slider 
            :model-value="config.confidence_threshold || 0.5"
            @update:model-value="updateConfig({ ...config, confidence_threshold: $event })"
            :min="0" 
            :max="1" 
            :step="0.01"
            show-input
          />
        </el-form-item>
      </div>
    </template>
  </AlgorithmConfigForm>
</template>

<script setup>
import AlgorithmConfigForm from '@/components/AlgorithmConfig/AlgorithmConfigForm.vue'
import { saveStandardAlgorithmConfig } from '@/api/stream_manage/algorithm'

const streamId = ref(1)
const initialData = ref({
  taskName: '测试任务',
  algorithmId: 'face_detection'
})

const onSubmit = async (data) => {
  try {
    const response = await saveStandardAlgorithmConfig(data)
    if (response.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

const onReset = () => {
  console.log('表单已重置')
}
</script>
```

### 3. 使用验证工具

```javascript
import { 
  validateTaskName, 
  validateAlgorithmConfig,
  generateDefaultTaskName 
} from '@/utils/algorithmValidation'

// 验证任务名称
const taskNameResult = validateTaskName('我的检测任务')
if (!taskNameResult.isValid) {
  console.error(taskNameResult.error)
}

// 生成默认任务名称
const defaultName = generateDefaultTaskName('人脸检测', 'face_detection')
console.log(defaultName) // 输出: 人脸检测检测任务_0722_1430

// 完整配置验证
const config = {
  streamId: 1,
  selectedAlgorithm: 'face_detection',
  taskName: '人脸检测任务',
  model_params: {
    confidence_threshold: 0.8,
    nms_threshold: 0.5
  }
}

const algorithmCapability = {
  algorithm_id: 'face_detection',
  supports_polygons: true,
  supports_lines: false
}

const validationResult = validateAlgorithmConfig(config, algorithmCapability)
if (!validationResult.isValid) {
  console.error('验证失败:', validationResult.errors)
}
```

## 验证规则

### 任务名称验证
- 不能为空
- 长度在2-50个字符之间
- 不能包含特殊字符 `< > : " / \ | ? *`

### 模型参数验证
- 置信度阈值：0-1之间
- NMS阈值：0-1之间
- 输入尺寸：1-2048之间
- 最大检测数量：1-1000之间

### 告警参数验证
- 告警间隔：1-3600秒之间
- 告警阈值：1-100之间
- 告警消息：不超过200个字符

### 几何配置验证
- 多边形：至少3个点
- 线段：必须有2个点
- 根据算法能力检查是否支持相应的几何类型

## 错误处理

所有验证错误都会以友好的中文消息返回，并且：

1. **前端验证**：在用户输入时进行实时验证
2. **提交验证**：在提交前进行完整验证
3. **后端验证**：后端也会进行验证，并返回详细的错误信息

## 扩展性

### 添加新的算法配置

1. 在 `AlgorithmConfigForm.vue` 的插槽中添加新的算法配置界面
2. 在 `algorithmValidation.js` 中添加特定的验证规则
3. 更新后端的算法能力定义

### 添加新的验证规则

在 `algorithmValidation.js` 中添加新的验证函数，并在相应的组件中调用。

## 注意事项

1. 所有必填字段都有明确的标识（红色星号）
2. 验证错误会实时显示在对应字段下方
3. 提交时会进行完整的验证，确保数据的完整性和正确性
4. 支持自动生成默认任务名称，提升用户体验
