import logging

# 创建一个日志记录器
LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(logging.DEBUG)  # 设置日志级别为 DEBUG

# 创建一个控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)  # 设置控制台日志级别为 DEBUG

# 创建一个日志格式
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# 将控制台处理器添加到日志记录器
LOGGER.addHandler(console_handler)

# 可选：创建一个文件处理器
# file_handler = logging.FileHandler('app.log')
# file_handler.setLevel(logging.DEBUG)
# file_handler.setFormatter(formatter)
# LOGGER.addHandler(file_handler)