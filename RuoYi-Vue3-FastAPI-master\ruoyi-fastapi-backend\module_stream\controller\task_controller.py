from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_stream.service.task_service import TaskService
from module_stream.service.task_execution_service import TaskExecutionService
from module_stream.entity.vo.task_vo import DeleteTaskModel, TaskPageQueryModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


taskController = APIRouter(prefix='/stream_manage/task', dependencies=[Depends(LoginService.get_current_user)])


@taskController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:task:list'))]
)
async def get_task_list(
    request: Request,
    pageNum: int = Query(1, description='当前页码'),
    pageSize: int = Query(10, description='每页记录数'),
    taskName: Optional[str] = Query(None, description='任务名称'),
    streamId: Optional[int] = Query(None, description='视频流ID'),
    algorithmType: Optional[str] = Query(None, description='算法类型'),
    status: Optional[str] = Query(None, description='状态'),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取检测任务分页列表

    :param request: Request对象
    :param pageNum: 当前页码
    :param pageSize: 每页记录数
    :param taskName: 任务名称
    :param streamId: 视频流ID
    :param algorithmType: 算法类型
    :param status: 状态
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 检测任务分页列表
    """
    # 获取全量数据
    logger.info(f"接收到的查询参数 - taskName: {taskName}, status: {status}")
    task_page_query_model = TaskPageQueryModel(
        task_name=taskName,
        stream_id=streamId,
        algorithm_type=algorithmType,
        status=status,
        page_num=pageNum,
        page_size=pageSize
    )
    task_page_query_result = await TaskService.get_task_list_services(
        query_db, task_page_query_model, current_user.user.user_id, is_page=True
    )
    logger.info('获取成功')
    return ResponseUtil.success(model_content=task_page_query_result)





@taskController.delete(
    '/{task_ids}', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:task:remove'))]
)
@Log(title='检测任务', business_type=BusinessType.DELETE)
async def delete_task(
    request: Request,
    task_ids: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除检测任务

    :param request: Request对象
    :param task_ids: 需要删除的任务ID字符串
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 删除检测任务响应
    """
    try:
        # 使用 taskIds 作为参数名，因为 DeleteTaskModel 使用了 to_camel 别名生成器
        delete_task_model = DeleteTaskModel(taskIds=task_ids)
        await TaskService.delete_task_services(query_db, delete_task_model)
        await query_db.commit()
        logger.info('删除成功')
        return ResponseUtil.success(msg='删除成功')
    except Exception as e:
        await query_db.rollback()
        logger.error(f'删除失败: {e}')
        return ResponseUtil.error(msg=str(e))


@taskController.get(
    '/{task_id}', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:task:query'))]
)
async def detail_task(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取检测任务详细信息

    :param request: Request对象
    :param task_id: 任务ID
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 检测任务详细信息响应
    """
    task_detail_result = await TaskService.detail_task_services(query_db, task_id)
    logger.info('获取成功')
    return ResponseUtil.success(data=task_detail_result)


@taskController.post(
    '/start/{task_id}', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:task:operate'))]
)
@Log(title='检测任务', business_type=BusinessType.OTHER)
async def start_task(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    启动检测任务

    :param request: Request对象
    :param task_id: 任务ID
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 启动任务响应
    """
    try:
        # 调用任务执行服务启动任务
        result = await TaskExecutionService.start_task(query_db, task_id, current_user.user.user_id)
        if result:
            logger.info(f'启动任务成功: {task_id}')
            return ResponseUtil.success(msg='任务启动成功')
        else:
            logger.error(f'启动任务失败: {task_id}')
            return ResponseUtil.error(msg='任务启动失败')
    except Exception as e:
        logger.error(f'任务启动失败: {e}')
        return ResponseUtil.error(msg=str(e))


@taskController.post(
    '/stop/{task_id}', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:task:operate'))]
)
@Log(title='检测任务', business_type=BusinessType.OTHER)
async def stop_task(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    停止检测任务

    :param request: Request对象
    :param task_id: 任务ID
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 停止任务响应
    """
    try:
        # 调用任务执行服务停止任务
        result = await TaskExecutionService.stop_task(query_db, task_id, current_user.user.user_id)
        if result:
            logger.info(f'停止任务成功: {task_id}')
            return ResponseUtil.success(msg='任务停止成功')
        else:
            logger.error(f'停止任务失败: {task_id}')
            return ResponseUtil.error(msg='任务停止失败')
    except Exception as e:
        logger.error(f'任务停止失败: {e}')
        return ResponseUtil.error(msg=str(e))


@taskController.post(
    '/pause/{task_id}', dependencies=[Depends(CheckUserInterfaceAuth('stream_manage:task:operate'))]
)
@Log(title='检测任务', business_type=BusinessType.OTHER)
async def pause_task(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db),
):
    """
    暂停检测任务

    :param request: Request对象
    :param task_id: 任务ID
    :param current_user: 当前用户对象
    :param query_db: 数据库会话对象
    :return: 暂停任务响应
    """
    try:
        # 调用任务执行服务暂停任务
        result = await TaskExecutionService.pause_task(query_db, task_id, current_user.user.user_id)
        if result:
            logger.info(f'暂停任务成功: {task_id}')
            return ResponseUtil.success(msg='任务暂停成功')
        else:
            logger.error(f'暂停任务失败: {task_id}')
            return ResponseUtil.error(msg='任务暂停失败')
    except Exception as e:
        logger.error(f'任务暂停失败: {e}')
        return ResponseUtil.error(msg=str(e))
