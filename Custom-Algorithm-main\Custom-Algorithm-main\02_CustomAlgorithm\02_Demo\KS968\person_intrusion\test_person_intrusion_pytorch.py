"""
人员入侵算法PyTorch适配测试
测试智驱力人员入侵检测算法是否能正常使用PyTorch模型
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path
from model.zql_detect import Model
from model.zql_detect import Model
from postprocessor.person_intrusion import Postprocessor

def test_person_intrusion_pytorch():
    """测试人员入侵PyTorch算法"""
    print("🚨 测试人员入侵算法PyTorch适配")
    print("=" * 60)
    
    try:
        # 导入人员入侵模型和后处理器
        
        
        # 创建模型实例
        model = Model(
            acc_id=0,
            name="person_intrusion",
            conf={
                'img_size': 640,
                'conf_thres': 0.25,
                'nms_thres': 0.45
            }
        )
        
        if not model.status:
            print("❌ 人员入侵模型初始化失败")
            return False
        
        print("✅ 人员入侵模型初始化成功")
        print(f"   - 设备: {model.device}")
        print(f"   - 图像尺寸: {model.img_size}")
        print(f"   - 置信度阈值: {model.conf_thres}")
        print(f"   - NMS阈值: {model.nms_thres}")

        # 创建后处理器实例
        print("🔧 初始化后处理器...")
        postprocessor = Postprocessor(
            source_id=0,
            alg_name="person_intrusion"
        )

        # 配置后处理器参数（模拟从数据库传来的配置）
        postprocessor.reserved_args = {
            'strategy': 'center',  # 检测策略：center表示检测目标中心点是否在区域内
            'polygons': {
                'polygon_0': {
                    'polygon': [[160, 120], [480, 120], [480, 360], [160, 360]],  # 中心禁区坐标
                    'color': [255, 0, 0],  # 红色
                    'thickness': 2
                }
            },
            'confidence_threshold': 0.25,  # 置信度阈值
            'alert_interval': 5,  # 告警间隔（秒）
            'alert_message': '检测到人员入侵禁区'  # 告警消息
        }

        print("✅ 后处理器初始化成功")
        
        # 测试随机图像
        print("\n🔍 测试随机图像...")
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        result = model.infer(test_image)
        print(f"📊 随机图像检测结果: {len(result)} 个目标")
        
        # 测试真实图像
        print("\n📸 测试真实图像...")
        test_images = [
            "D:/people.jpg"
        ]
        
        for image_path in test_images:
            if os.path.exists(image_path):
                print(f"\n🔍 测试图像: {os.path.basename(image_path)}")
                
                # 读取图像
                image = cv2.imread(image_path)
                if image is None:
                    print(f"❌ 无法读取图像: {image_path}")
                    continue
                
                print(f"📸 图像尺寸: {image.shape}")
                
                # 转换为RGB格式
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                # 进行推理（使用infer方法获取智驱力格式的结果）
                detection_result = model.infer(image_rgb)
                print(f"📊 检测结果: {len(detection_result)} 个目标")
                print(f"📊 详细结果: {detection_result}")

                # 使用后处理器进行告警判断
                print("⚙️ 进行后处理告警判断...")

                # 准备模型数据字典（智驱力格式）
                model_data = {
                    'model': {  # 模型名称作为键
                        'model_conf': {
                            'conf_thres': 0.25,
                            'labels': ['person']  # 根据配置设置标签
                        },
                        'engine_result': detection_result  # 检测结果列表
                    }
                }

                # 准备结果字典
                alert_result = {}

                # 调用后处理器
                success = postprocessor.process(model_data, alert_result)

                # 分析告警结果
                is_alert = alert_result.get('hit', False)
                alert_message = alert_result.get('message', '')

                print(f"🎯 告警结果:")
                print(f"   - 是否告警: {'🚨 是' if is_alert else '✅ 否'}")
                if alert_message:
                    print(f"   - 告警消息: {alert_message}")

                # 从检测结果中提取目标信息用于显示
                # detection_result 是一个列表，直接使用
                engine_results = detection_result if isinstance(detection_result, list) else []
                print(f"📊 检测到目标: {len(engine_results)} 个")
                
                # COCO类别名称
                coco_classes = [
                    'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
                    'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
                    'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
                    'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
                    'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
                    'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
                    'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
                    'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
                    'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
                    'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
                    'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
                    'toothbrush'
                ]

                # 分析检测结果
                person_detections = []
                intrusion_alerts = []

                # 从检测结果中提取人员信息
                for i, detection in enumerate(engine_results):
                    class_name = coco_classes[detection['label']] if detection['label'] < len(coco_classes) else f"Class_{detection['label']}"

                    print(f"  目标{i+1}: {class_name} 置信度:{detection['conf']:.2f} 位置:{detection['xyxy']}")

                    # 检查是否是人员
                    if class_name == 'person':
                        person_detections.append(detection)

                # 从告警结果中提取入侵信息
                if is_alert and 'data' in alert_result:
                    bbox_data = alert_result['data'].get('bbox', {})
                    rectangles = bbox_data.get('rectangles', [])

                    for rect in rectangles:
                        if rect.get('color') == [255, 0, 0]:  # 红色表示告警
                            x1, y1, x2, y2 = rect['xyxy']
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            intrusion_alerts.append({
                                'person_id': len(intrusion_alerts) + 1,
                                'confidence': rect.get('conf', 0),
                                'position': (center_x, center_y),
                                'bbox': rect['xyxy']
                            })

                print(f"👥 检测到人员: {len(person_detections)} 人")
                print(f"🚨 入侵警报: {len(intrusion_alerts)} 起")

                if intrusion_alerts:
                    print("⚠️ 入侵详情:")
                    for alert in intrusion_alerts:
                        print(f"   人员{alert['person_id']}: 位置({alert['position'][0]}, {alert['position'][1]}) 置信度:{alert['confidence']:.2f}")

                if is_alert:
                    print(f"📢 告警消息: {alert_message}")
                
                # 绘制检测框和入侵区域
                result_image = image.copy()
                
                # 绘制禁区
                img_h, img_w = image.shape[:2]
                forbidden_x1 = int(img_w * 0.25)
                forbidden_x2 = int(img_w * 0.75)
                forbidden_y1 = int(img_h * 0.25)
                forbidden_y2 = int(img_h * 0.75)
                
                # 半透明禁区
                overlay = result_image.copy()
                cv2.rectangle(overlay, (forbidden_x1, forbidden_y1), (forbidden_x2, forbidden_y2), (0, 0, 255), -1)
                cv2.addWeighted(overlay, 0.3, result_image, 0.7, 0, result_image)
                
                # 禁区边框
                cv2.rectangle(result_image, (forbidden_x1, forbidden_y1), (forbidden_x2, forbidden_y2), (0, 0, 255), 3)
                cv2.putText(result_image, "RESTRICTED AREA", (forbidden_x1, forbidden_y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                
                # 绘制检测结果（使用后处理器的结果）
                if 'data' in alert_result and 'bbox' in alert_result['data']:
                    bbox_data = alert_result['data']['bbox']

                    # 绘制多边形区域
                    if 'polygons' in bbox_data:
                        for polygon_name, polygon_data in bbox_data['polygons'].items():
                            points = np.array(polygon_data['polygon'], np.int32)
                            color = tuple(polygon_data.get('color', [0, 255, 0]))
                            # BGR格式
                            bgr_color = (color[2], color[1], color[0])
                            cv2.polylines(result_image, [points], True, bgr_color, 2)

                            # 添加区域标签
                            cv2.putText(result_image, "RESTRICTED AREA",
                                       (points[0][0], points[0][1]-10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, bgr_color, 2)

                    # 绘制检测框
                    if 'rectangles' in bbox_data:
                        for rect in bbox_data['rectangles']:
                            if 'xyxy' in rect:
                                x1, y1, x2, y2 = map(int, rect['xyxy'])
                                color = rect.get('color', [0, 255, 0])
                                # BGR格式
                                bgr_color = (color[2], color[1], color[0])

                                # 绘制检测框
                                cv2.rectangle(result_image, (x1, y1), (x2, y2), bgr_color, 2)

                                # 添加标签
                                label = f"{rect.get('label', 'person')} {rect.get('conf', 0):.2f}"
                                cv2.putText(result_image, label, (x1, y1-10),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, bgr_color, 2)
                else:
                    # 如果没有后处理结果，直接绘制原始检测结果
                    for i, detection in enumerate(engine_results):
                        x1, y1, x2, y2 = map(int, detection['xyxy'])
                        class_name = coco_classes[detection['label']] if detection['label'] < len(coco_classes) else f"Class_{detection['label']}"

                        if class_name == 'person':
                            # 正常人员用绿色
                            color = (0, 255, 0)
                            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
                            label = f"Person {i+1}: {detection['conf']:.2f}"
                            cv2.putText(result_image, label, (x1, y1-10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                
                # 添加状态信息
                status_text = f"Persons: {len(person_detections)} | Intrusions: {len(intrusion_alerts)}"
                status_color = (0, 0, 255) if intrusion_alerts else (0, 255, 0)
                cv2.putText(result_image, status_text, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, status_color, 2)
                
                if intrusion_alerts:
                    cv2.putText(result_image, "ALERT: INTRUSION DETECTED!", (10, 70), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
                
                # 保存结果图像
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                result_path = f"{base_name}_person_intrusion_pytorch_result.jpg"
                cv2.imwrite(result_path, result_image)
                print(f"📸 检测结果已保存为: {result_path}")
                
            else:
                print(f"⚠️ 图像文件不存在: {image_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 人员入侵测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intrusion_sensitivity():
    """测试入侵检测敏感度"""
    print("\n🎚️ 入侵检测敏感度测试")
    print("-" * 40)
    
    try:
        
        
        # 测试不同置信度阈值
        confidence_levels = [0.1, 0.25, 0.5, 0.7, 0.9]
        
        for conf_thres in confidence_levels:
            print(f"\n🔧 测试置信度阈值: {conf_thres}")
            
            model = Model(0, "person_intrusion", {
                'img_size': 640,
                'conf_thres': conf_thres,
                'nms_thres': 0.45
            })
            
            if not model.status:
                print(f"   ❌ 模型初始化失败")
                continue
            
            # 测试图像
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            result = model.infer(test_image)
            
            person_count = sum(1 for det in result if det['label'] == 0)
            
            print(f"   检测到目标: {len(result)} 个")
            print(f"   检测到人员: {person_count} 人")
            print(f"   平均置信度: {np.mean([det['conf'] for det in result]):.2f}" if result else "   平均置信度: N/A")
        
        return True
        
    except Exception as e:
        print(f"❌ 敏感度测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚨 人员入侵算法PyTorch适配完整测试")
    print("=" * 80)
    
    # 功能测试
    function_ok = test_person_intrusion_pytorch()
    
    # 敏感度测试
    sensitivity_ok = test_intrusion_sensitivity()
    
    print("\n" + "=" * 80)
    print("🎯 人员入侵测试结果:")
    print(f"   - 功能测试: {'✅ 通过' if function_ok else '❌ 失败'}")
    print(f"   - 敏感度测试: {'✅ 通过' if sensitivity_ok else '❌ 失败'}")
    
    if function_ok and sensitivity_ok:
        print("\n🎉 人员入侵算法PyTorch适配完全成功！")
        print("✅ 可以准确检测人员入侵")
        print("✅ 支持可调节的检测敏感度")
        print("✅ 提供直观的入侵区域可视化")
        print("✅ 完全兼容智驱力框架")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
