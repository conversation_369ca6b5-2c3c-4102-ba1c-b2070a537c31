#!/usr/bin/env python3
"""
简单检测框绘制测试
直接测试检测框绘制功能
"""

import cv2
import numpy as np
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_stream.service.task_execution_service import TaskExecutionService


def test_direct_drawing():
    """直接测试绘制功能"""
    print("🔍 直接测试检测框绘制")
    
    # 创建白色背景的测试图像
    frame = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 添加标题
    cv2.putText(frame, "Detection Box Test", (250, 50), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
    
    # 手动绘制一些检测框作为对比
    cv2.rectangle(frame, (50, 100), (150, 200), (0, 255, 0), 3)  # 绿色框
    cv2.putText(frame, "Manual Green Box", (50, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    cv2.rectangle(frame, (200, 100), (300, 200), (255, 0, 0), 3)  # 蓝色框
    cv2.putText(frame, "Manual Blue Box", (200, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
    
    # 创建告警结果数据
    alert_result = {
        'hit': True,
        'message': '检测到告警目标',
        'details': {
            'alert_targets': [
                {
                    'box': [400, 150, 500, 250],  # 第一个检测框
                    'score': 0.85,
                    'label': 'car',
                    'alert_type': 'vehicle_counting',
                    'original_color': None  # 使用默认颜色
                },
                {
                    'box': [550, 150, 650, 250],  # 第二个检测框
                    'score': 0.72,
                    'label': 'truck',
                    'alert_type': 'vehicle_counting',
                    'original_color': [255, 165, 0]  # 橙色
                }
            ],
            'configured_areas': [
                {
                    'id': 'area_1',
                    'name': '监控区域',
                    'points': [
                        {'x': 100, 'y': 300},
                        {'x': 700, 'y': 300},
                        {'x': 700, 'y': 500},
                        {'x': 100, 'y': 500}
                    ]
                }
            ]
        }
    }
    
    print(f"✅ 创建测试数据")
    print(f"  - 告警状态: {alert_result['hit']}")
    print(f"  - 告警目标数量: {len(alert_result['details']['alert_targets'])}")
    
    # 调用绘制函数
    try:
        TaskExecutionService._draw_detection_boxes(frame, alert_result)
        print(f"✅ 绘制函数调用成功")
    except Exception as e:
        print(f"❌ 绘制函数调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 保存结果
    cv2.imwrite("test_simple_detection_box.jpg", frame)
    print(f"📸 保存结果: test_simple_detection_box.jpg")
    
    # 检查图像是否有变化（简单的像素检查）
    # 如果绘制成功，图像应该不再是纯白色
    non_white_pixels = np.sum(frame != 255)
    print(f"📊 非白色像素数量: {non_white_pixels}")
    
    if non_white_pixels > 10000:  # 如果有足够的非白色像素
        print(f"✅ 检测到绘制内容")
        return True
    else:
        print(f"❌ 未检测到明显的绘制内容")
        return False


def test_single_target_drawing():
    """测试单个目标绘制"""
    print(f"\n🔍 测试单个目标绘制")
    
    # 创建白色背景
    frame = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # 添加标题
    cv2.putText(frame, "Single Target Test", (150, 50), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
    
    # 测试目标数据
    target = {
        'box': [200, 150, 400, 300],
        'score': 0.89,
        'label': 'car',
        'alert_type': 'vehicle_counting',
        'original_color': None
    }
    
    print(f"✅ 测试目标: {target}")
    
    try:
        # 直接调用单个目标绘制函数
        TaskExecutionService._draw_single_alert_target(frame, target)
        print(f"✅ 单个目标绘制成功")
        
        # 保存结果
        cv2.imwrite("test_single_target.jpg", frame)
        print(f"📸 保存结果: test_single_target.jpg")
        
        return True
        
    except Exception as e:
        print(f"❌ 单个目标绘制失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 简单检测框绘制测试开始")
    
    # 测试1：直接绘制测试
    result1 = test_direct_drawing()
    
    # 测试2：单个目标绘制测试
    result2 = test_single_target_drawing()
    
    # 汇总结果
    print(f"\n{'='*50}")
    print("🏁 测试结果汇总")
    print(f"{'='*50}")
    
    print(f"📊 测试结果:")
    print(f"  直接绘制测试: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"  单个目标绘制: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n✅ 所有测试通过！检测框绘制功能正常")
        print(f"📸 生成的测试图片:")
        print(f"  - test_simple_detection_box.jpg: 完整绘制测试")
        print(f"  - test_single_target.jpg: 单个目标绘制测试")
    else:
        print(f"\n❌ 部分或全部测试失败")
        print(f"🔍 可能的问题:")
        print(f"  1. 绘制函数内部异常")
        print(f"  2. 数据格式不匹配")
        print(f"  3. OpenCV绘制问题")
    
    return result1 and result2


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
