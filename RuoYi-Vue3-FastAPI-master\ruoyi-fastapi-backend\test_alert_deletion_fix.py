#!/usr/bin/env python3
"""
告警记录删除功能修复测试脚本
测试修正后的删除告警记录功能
"""

import asyncio
import cv2
import numpy as np
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from module_alert.service.alert_service import AlertService
from module_alert.dao.alert_dao import AlertDao
from module_alert.entity.do.alert_do import SurveillanceAlert
from module_alert.entity.vo.alert_vo import AlertModel, DeleteAlertModel
from utils.common_util import CamelCaseUtil
from utils.log_util import logger


class AlertDeletionFixTester:
    """告警记录删除功能修复测试器"""
    
    def create_test_screenshot(self, filename):
        """创建测试截图文件"""
        # 创建测试图片
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加内容
        cv2.putText(frame, "Test Alert Screenshot", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), (50, 150), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.rectangle(frame, (200, 200, 300, 300), (0, 0, 255), 3)  # 红色告警框
        
        # 确保目录存在
        file_path = Path("uploads/alerts/test") / filename
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存图片
        cv2.imwrite(str(file_path), frame)
        
        return f"/uploads/alerts/test/{filename}"
    
    def test_screenshot_path_access(self):
        """测试截图路径访问"""
        print("=" * 60)
        print("测试截图路径访问")
        print("=" * 60)
        
        try:
            # 创建模拟的数据库实体对象
            alert_entity = SurveillanceAlert()
            alert_entity.alert_id = 999
            alert_entity.screenshot_path = "/uploads/alerts/test/test_screenshot.jpg"
            
            print(f"✅ 数据库实体对象创建成功")
            print(f"  - alert_id: {alert_entity.alert_id}")
            print(f"  - screenshot_path: {alert_entity.screenshot_path}")
            
            # 测试字段访问
            if hasattr(alert_entity, 'screenshot_path'):
                print(f"  ✅ screenshot_path 字段存在")
            else:
                print(f"  ❌ screenshot_path 字段不存在")
                return False
            
            # 转换为AlertModel
            alert_dict = CamelCaseUtil.transform_result(alert_entity)
            alert_model = AlertModel(**alert_dict)
            
            print(f"✅ 转换为AlertModel成功")
            print(f"  - alert_id: {alert_model.alert_id}")
            
            # 检查AlertModel中的字段
            if hasattr(alert_model, 'image_path'):
                print(f"  ✅ AlertModel有image_path字段: {alert_model.image_path}")
            else:
                print(f"  ⚠️  AlertModel没有image_path字段")
            
            if hasattr(alert_model, 'screenshot_path'):
                print(f"  ⚠️  AlertModel有screenshot_path字段: {getattr(alert_model, 'screenshot_path', None)}")
            else:
                print(f"  ✅ AlertModel没有screenshot_path字段（符合预期）")
            
            return True
            
        except Exception as e:
            print(f"❌ 截图路径访问测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_delete_screenshot_method(self):
        """测试删除截图方法"""
        print("\n" + "=" * 60)
        print("测试删除截图方法")
        print("=" * 60)
        
        try:
            # 创建测试截图文件
            screenshot_filename = "test_delete_screenshot.jpg"
            screenshot_url = self.create_test_screenshot(screenshot_filename)
            
            print(f"✅ 创建测试截图: {screenshot_url}")
            
            # 检查文件是否存在
            file_path = Path("uploads/alerts/test") / screenshot_filename
            if file_path.exists():
                print(f"  ✅ 文件确实存在: {file_path}")
                print(f"  - 文件大小: {file_path.stat().st_size} 字节")
            else:
                print(f"  ❌ 文件不存在: {file_path}")
                return False
            
            # 测试删除方法
            success = await AlertService._delete_alert_screenshot(screenshot_url)
            
            print(f"📊 删除结果: {'成功' if success else '失败'}")
            
            # 检查文件是否被删除
            if file_path.exists():
                print(f"  ❌ 文件仍然存在，删除失败")
                return False
            else:
                print(f"  ✅ 文件已被删除")
                return True
                
        except Exception as e:
            print(f"❌ 删除截图方法测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_different_path_formats(self):
        """测试不同路径格式的删除"""
        print("\n" + "=" * 60)
        print("测试不同路径格式的删除")
        print("=" * 60)
        
        try:
            test_cases = [
                ("URL格式", "/uploads/alerts/test/url_format.jpg"),
                ("相对路径", "alerts/test/relative_path.jpg"),
                ("绝对路径", None)  # 将在运行时设置
            ]
            
            results = []
            
            for case_name, path_format in test_cases:
                print(f"\n🧪 测试 {case_name}: {path_format}")
                
                # 创建测试文件
                filename = f"{case_name.lower().replace(' ', '_')}_test.jpg"
                if case_name == "绝对路径":
                    file_path = Path("uploads/alerts/test") / filename
                    path_format = str(file_path.absolute())
                
                # 创建截图
                if case_name == "URL格式":
                    screenshot_url = self.create_test_screenshot(filename)
                    test_path = screenshot_url
                elif case_name == "相对路径":
                    self.create_test_screenshot(filename)
                    test_path = f"alerts/test/{filename}"
                else:  # 绝对路径
                    self.create_test_screenshot(filename)
                    test_path = path_format
                
                print(f"  - 测试路径: {test_path}")
                
                # 测试删除
                success = await AlertService._delete_alert_screenshot(test_path)
                results.append(success)
                
                print(f"  - 删除结果: {'成功' if success else '失败'}")
            
            success_count = sum(results)
            print(f"\n📊 路径格式测试结果: {success_count}/{len(results)} 种格式删除成功")
            
            return success_count > 0  # 至少有一种格式成功
            
        except Exception as e:
            print(f"❌ 不同路径格式测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_field_mapping(self):
        """测试字段映射"""
        print("\n" + "=" * 60)
        print("测试字段映射")
        print("=" * 60)
        
        try:
            # 创建数据库实体
            alert_entity = SurveillanceAlert()
            alert_entity.alert_id = 123
            alert_entity.task_id = 456
            alert_entity.stream_id = 789
            alert_entity.screenshot_path = "/uploads/alerts/test/mapping_test.jpg"
            alert_entity.alert_type = "area_intrusion"
            alert_entity.alert_message = "测试告警"
            alert_entity.alert_time = datetime.now()
            
            print(f"✅ 数据库实体创建成功")
            print(f"  - screenshot_path: {alert_entity.screenshot_path}")
            
            # 转换为字典
            alert_dict = CamelCaseUtil.transform_result(alert_entity)
            print(f"✅ 转换为字典成功")
            print(f"  - 字典键: {list(alert_dict.keys())}")
            
            # 检查screenshot_path的映射
            if 'screenshot_path' in alert_dict:
                print(f"  ✅ 字典中有screenshot_path: {alert_dict['screenshot_path']}")
            elif 'screenshotPath' in alert_dict:
                print(f"  ✅ 字典中有screenshotPath: {alert_dict['screenshotPath']}")
            elif 'image_path' in alert_dict:
                print(f"  ✅ 字典中有image_path: {alert_dict['image_path']}")
            elif 'imagePath' in alert_dict:
                print(f"  ✅ 字典中有imagePath: {alert_dict['imagePath']}")
            else:
                print(f"  ❌ 字典中没有找到截图路径字段")
                print(f"  - 可用字段: {[k for k in alert_dict.keys() if 'path' in k.lower() or 'image' in k.lower() or 'screenshot' in k.lower()]}")
            
            # 创建AlertModel
            alert_model = AlertModel(**alert_dict)
            print(f"✅ AlertModel创建成功")
            print(f"  - alert_id: {alert_model.alert_id}")
            
            # 检查AlertModel中的路径字段
            path_fields = []
            for field_name in ['image_path', 'screenshot_path', 'imagePath', 'screenshotPath']:
                if hasattr(alert_model, field_name):
                    value = getattr(alert_model, field_name)
                    if value:
                        path_fields.append((field_name, value))
            
            if path_fields:
                print(f"  ✅ AlertModel中的路径字段:")
                for field_name, value in path_fields:
                    print(f"    - {field_name}: {value}")
            else:
                print(f"  ❌ AlertModel中没有找到路径字段")
            
            return len(path_fields) > 0
            
        except Exception as e:
            print(f"❌ 字段映射测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """主测试函数"""
    print("🎯 告警记录删除功能修复测试开始")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = AlertDeletionFixTester()
    
    # 运行测试
    tests = [
        ("截图路径访问", lambda: tester.test_screenshot_path_access()),
        ("删除截图方法", tester.test_delete_screenshot_method),
        ("不同路径格式", tester.test_different_path_formats),
        ("字段映射", lambda: tester.test_field_mapping())
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🏁 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 告警记录删除功能修复成功")
        print("\n📋 修复确认:")
        print("  ✅ 正确访问数据库实体的screenshot_path字段")
        print("  ✅ 删除告警记录时同时删除本地图片")
        print("  ✅ 支持多种路径格式的处理")
        print("  ✅ 字段映射和类型转换正常")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
