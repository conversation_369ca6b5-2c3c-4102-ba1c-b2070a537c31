{"basicParams": {"alert_window": {"type": "interval_threshold_window", "interval": 5, "length": 5, "threshold": 3}, "bbox": {"polygons": [], "lines": []}, "plan": {"1": [[0, 86399]], "2": [[0, 86399]], "3": [[0, 86399]], "4": [[0, 86399]], "5": [[0, 86399]], "6": [[0, 86399]], "7": [[0, 86399]]}, "hazard_level": "", "alg_type": "general", "model_args": {"zql_person": {"conf_thres": 0.65}}, "reserved_args": {"ch_name": "区域入侵", "sound_text": "区域入侵告警", "strategy": "bottom"}}, "renderParams": {"alert_window": {"interval": {"label": "告警间隔", "unit": "秒", "tooltip": "例：设置为5秒，则5秒内连续检测到多次只告警1次", "type": "number", "range": {"min": 0, "step": 1, "max": 99999999}}, "length": {"label": "告警窗口长度", "unit": "次", "tooltip": "告警事件的判断周期，如设置5，则利用最近的5次检测结果判断是否为告警。", "type": "number", "range": {"min": 0, "step": 1, "max": 100}}, "threshold": {"label": "告警阈值", "unit": "次", "tooltip": "告警命中阈值，配合告警窗口使用。如告警窗口长度设置为5，告警阈值设置为3，则5次检测结果中，有3次告警命中，为1次告警事件。", "type": "number", "range": {"min": 0, "step": 1, "max": 100}}}, "reserved_args": {"strategy": {"hide": true, "label": "检测策略", "tooltip": "检测框判断点选择。如选择底部，表示利用检测框底部中点与区域（如入侵区域或离岗区域）关系判断告警事件。", "type": "select", "options": [{"label": "顶部", "value": "top"}, {"label": "中心", "value": "center"}, {"label": "底部", "value": "bottom"}, {"label": "左侧", "value": "left"}, {"label": "右侧", "value": "right"}]}, "sound_text": {"label": "浏览器语音播报", "type": "text", "maxLength": 20}}, "bbox": {"polygons": {"exits": "must", "max": -1, "edge": -1}}, "model_args": {"zql_person": {"conf_thres": {"label": "人体检测置信度", "unit": "", "type": "number", "range": {"min": 0, "step": 0.01, "max": 1}}}}}}