"""
标准化算法配置控制器
提供统一的算法配置管理API
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_stream.service.algorithm_standard_service import AlgorithmStandardService
from module_stream.service.algorithm_config_service import AlgorithmConfigService
from module_stream.entity.vo.algorithm_standard_vo import (
    AlgorithmCapability, AlgorithmConfigRequest, AlgorithmConfigResponse,
    AlgorithmConfig, TaskExecutionRequest, TaskExecutionResponse
)
from utils.response_util import ResponseUtil
from utils.log_util import logger


# 创建路由器
algorithmStandardController = APIRouter(
    prefix='/stream_manage/algorithm/standard',
    dependencies=[Depends(LoginService.get_current_user)]
)


@algorithmStandardController.get('/capabilities', response_model=List[AlgorithmCapability])
async def get_algorithm_capabilities():
    """
    获取所有算法的能力描述
    
    :return: 算法能力列表
    """
    try:
        capabilities = await AlgorithmStandardService.get_algorithm_capabilities()
        logger.info(f"获取算法能力成功，共{len(capabilities)}个算法")
        return ResponseUtil.success(data=capabilities)
    except Exception as e:
        logger.error(f"获取算法能力失败: {e}")
        return ResponseUtil.error(msg=f"获取算法能力失败: {str(e)}")


@algorithmStandardController.post('/config', response_model=AlgorithmConfigResponse)
async def save_algorithm_config(
    request: AlgorithmConfigRequest,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    保存算法配置（标准化接口）

    :param request: 配置请求
    :param current_user: 当前用户
    :param db: 数据库会话
    :return: 配置响应
    """
    try:
        from exceptions.exception import ServiceException

        logger.info(f"用户 {current_user.user.user_name} 保存算法配置: "
                   f"stream_id={request.stream_id}, algorithm_id={request.config.algorithm_id}")

        # 参数验证
        if not request.stream_id:
            return ResponseUtil.error(msg="视频流ID不能为空")

        if not request.config.algorithm_id or request.config.algorithm_id.strip() == '':
            return ResponseUtil.error(msg="算法ID不能为空")

        if not request.task_name or request.task_name.strip() == '':
            return ResponseUtil.error(msg="任务名称不能为空")

        # 处理空字符串参数，设置默认值
        if not request.config.algorithm_name or request.config.algorithm_name.strip() == '':
            request.config.algorithm_name = request.config.algorithm_id

        if not request.config.algorithm_version or request.config.algorithm_version.strip() == '':
            request.config.algorithm_version = '1.0.0'

        # 详细调试信息
        logger.info(f"=== 后端接收配置调试 ===")
        logger.info(f"1. 任务名称: {request.task_name}")
        logger.info(f"2. 视频流ID: {request.stream_id}")
        logger.info(f"3. 完整配置数据: {request.config.model_dump() if hasattr(request.config, 'model_dump') else str(request.config)}")
        logger.info(f"4. 检测区域数量: {len(request.config.detection_areas)}")
        logger.info(f"5. 检测线段数量: {len(request.config.detection_lines) if hasattr(request.config, 'detection_lines') else 0}")
        for i, area in enumerate(request.config.detection_areas):
            logger.info(f"区域{i+1}: {area.name}, 点数: {len(area.points)}, 坐标: {[{'x': p.x, 'y': p.y} for p in area.points]}")
        if hasattr(request.config, 'detection_lines'):
            for i, line in enumerate(request.config.detection_lines):
                logger.info(f"线段{i+1}: {line.name}, 点数: {len(line.points)}, 坐标: {[{'x': p.x, 'y': p.y} for p in line.points]}")
        if hasattr(request.config, 'model_params'):
            logger.info(f"6. 模型参数: {request.config.model_params.model_dump() if hasattr(request.config.model_params, 'model_dump') else str(request.config.model_params)}")
        if hasattr(request.config, 'alert_params'):
            logger.info(f"7. 告警参数: {request.config.alert_params.model_dump() if hasattr(request.config.alert_params, 'model_dump') else str(request.config.alert_params)}")

        response = await AlgorithmStandardService.save_algorithm_config(
            db, request, current_user.user.user_id
        )

        logger.info(f"算法配置保存成功: config_id={response.config_id}, task_id={response.task_id}")
        return ResponseUtil.success(data=response, msg="算法配置保存成功")

    except ServiceException as e:
        logger.error(f"业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"保存算法配置失败: {e}")
        return ResponseUtil.error(msg=f"保存算法配置失败: {str(e)}")


@algorithmStandardController.get('/config/{stream_id}/{algorithm_id}', response_model=AlgorithmConfig)
async def get_algorithm_config(
    stream_id: int,
    algorithm_id: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取算法配置
    
    :param stream_id: 视频流ID
    :param algorithm_id: 算法ID
    :param current_user: 当前用户
    :param db: 数据库会话
    :return: 算法配置
    """
    try:
        config = await AlgorithmStandardService.get_algorithm_config(
            db, stream_id, algorithm_id, current_user.user.user_id
        )
        
        if config:
            logger.info(f"获取算法配置成功: stream_id={stream_id}, algorithm_id={algorithm_id}")
            return ResponseUtil.success(data=config)
        else:
            logger.warning(f"算法配置不存在: stream_id={stream_id}, algorithm_id={algorithm_id}")
            return ResponseUtil.error(msg="算法配置不存在")
            
    except Exception as e:
        logger.error(f"获取算法配置失败: {e}")
        return ResponseUtil.error(msg=f"获取算法配置失败: {str(e)}")


@algorithmStandardController.post('/task/execute', response_model=TaskExecutionResponse)
async def execute_task(
    request: TaskExecutionRequest,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    执行任务操作（启动/停止/暂停/恢复）
    
    :param request: 执行请求
    :param current_user: 当前用户
    :param db: 数据库会话
    :return: 执行响应
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 执行任务操作: "
                   f"task_id={request.task_id}, action={request.action}")
        
        # 这里应该调用任务执行服务
        # 暂时返回模拟响应
        from datetime import datetime
        
        response = TaskExecutionResponse(
            task_id=request.task_id,
            action=request.action,
            status="success",
            message=f"任务{request.action}操作执行成功",
            execution_time=datetime.now().isoformat()
        )
        
        logger.info(f"任务操作执行成功: task_id={request.task_id}, action={request.action}")
        return ResponseUtil.success(data=response, msg="任务操作执行成功")
        
    except Exception as e:
        logger.error(f"执行任务操作失败: {e}")
        return ResponseUtil.error(msg=f"执行任务操作失败: {str(e)}")


@algorithmStandardController.get('/config/validate/{algorithm_id}')
async def validate_algorithm_config(
    algorithm_id: str,
    config: AlgorithmConfig
):
    """
    验证算法配置是否有效
    
    :param algorithm_id: 算法ID
    :param config: 算法配置
    :return: 验证结果
    """
    try:
        # 获取算法能力
        capabilities = await AlgorithmStandardService.get_algorithm_capabilities()
        algorithm_capability = next(
            (cap for cap in capabilities if cap.algorithm_id == algorithm_id), 
            None
        )
        
        if not algorithm_capability:
            return ResponseUtil.error(msg=f"不支持的算法: {algorithm_id}")
        
        # 验证配置
        validation_errors = []
        
        # 检查几何配置
        if config.detection_areas and not algorithm_capability.supports_polygons:
            validation_errors.append("该算法不支持多边形检测区域")
        
        if config.detection_lines and not algorithm_capability.supports_lines:
            validation_errors.append("该算法不支持线段检测")
        
        if config.exclusion_areas and not algorithm_capability.supports_exclusion:
            validation_errors.append("该算法不支持排除区域")
        
        # 检查参数配置
        for param_name in config.custom_params.keys():
            if param_name not in algorithm_capability.configurable_params:
                validation_errors.append(f"不支持的参数: {param_name}")
        
        if validation_errors:
            return ResponseUtil.error(
                msg="配置验证失败",
                data={"errors": validation_errors}
            )
        else:
            return ResponseUtil.success(msg="配置验证通过")
            
    except Exception as e:
        logger.error(f"验证算法配置失败: {e}")
        return ResponseUtil.error(msg=f"验证算法配置失败: {str(e)}")


@algorithmStandardController.get('/config/template/{algorithm_id}')
async def get_algorithm_config_template(algorithm_id: str):
    """
    获取算法配置模板
    
    :param algorithm_id: 算法ID
    :return: 配置模板
    """
    try:
        capabilities = await AlgorithmStandardService.get_algorithm_capabilities()
        algorithm_capability = next(
            (cap for cap in capabilities if cap.algorithm_id == algorithm_id), 
            None
        )
        
        if not algorithm_capability:
            return ResponseUtil.error(msg=f"不支持的算法: {algorithm_id}")
        
        template = {
            "algorithm_info": algorithm_capability.dict(),
            "default_config": algorithm_capability.default_config.dict(),
            "ui_schema": cls._generate_ui_schema(algorithm_capability)
        }
        
        return ResponseUtil.success(data=template, msg="获取配置模板成功")
        
    except Exception as e:
        logger.error(f"获取算法配置模板失败: {e}")
        return ResponseUtil.error(msg=f"获取算法配置模板失败: {str(e)}")


def _generate_ui_schema(capability: AlgorithmCapability) -> dict:
    """
    生成前端UI配置模式
    
    :param capability: 算法能力
    :return: UI配置模式
    """
    ui_schema = {
        "geometry": {
            "show_polygons": capability.supports_polygons,
            "show_lines": capability.supports_lines,
            "show_exclusion": capability.supports_exclusion
        },
        "parameters": []
    }
    
    # 根据可配置参数生成UI配置
    for param in capability.configurable_params:
        if param == "confidence_threshold":
            ui_schema["parameters"].append({
                "name": param,
                "type": "slider",
                "label": "置信度阈值",
                "min": 0.0,
                "max": 1.0,
                "step": 0.01,
                "default": 0.5
            })
        elif param == "alert_interval":
            ui_schema["parameters"].append({
                "name": param,
                "type": "number",
                "label": "告警间隔(秒)",
                "min": 1,
                "max": 3600,
                "default": 5
            })
        # 可以继续添加其他参数类型
    
    return ui_schema


@algorithmStandardController.get('/render-config/{algorithm_id}')
async def get_algorithm_render_config(
    algorithm_id: str,
    platform: str = "KS968"
):
    """
    获取算法的前端渲染配置（从智驱力目录）

    :param algorithm_id: 算法ID
    :param platform: 平台类型，默认KS968
    :return: 前端渲染配置
    """
    try:
        render_config = AlgorithmConfigService.get_algorithm_render_config(algorithm_id, platform)
        if not render_config:
            return ResponseUtil.error(msg=f"未找到算法 {algorithm_id} 的配置信息")

        return ResponseUtil.success(data=render_config)

    except Exception as e:
        logger.error(f"获取算法渲染配置失败: {e}")
        return ResponseUtil.error(msg="获取算法配置失败")


@algorithmStandardController.get('/default-params/{algorithm_id}')
async def get_algorithm_default_params(
    algorithm_id: str,
    platform: str = "KS968"
):
    """
    获取算法的默认参数（从智驱力目录）

    :param algorithm_id: 算法ID
    :param platform: 平台类型，默认KS968
    :return: 默认参数配置
    """
    try:
        default_params = AlgorithmConfigService.get_algorithm_default_params(algorithm_id, platform)
        if not default_params:
            return ResponseUtil.error(msg=f"未找到算法 {algorithm_id} 的默认参数")

        return ResponseUtil.success(data=default_params)

    except Exception as e:
        logger.error(f"获取算法默认参数失败: {e}")
        return ResponseUtil.error(msg="获取算法默认参数失败")


@algorithmStandardController.get('/available-algorithms')
async def get_available_algorithms(platform: str = "KS968"):
    """
    获取可用的算法列表（从智驱力目录）

    :param platform: 平台类型，默认KS968
    :return: 可用算法列表
    """
    try:
        algorithms = AlgorithmConfigService.list_available_algorithms(platform)
        return ResponseUtil.success(data=algorithms)

    except Exception as e:
        logger.error(f"获取可用算法列表失败: {e}")
        return ResponseUtil.error(msg="获取可用算法列表失败")


@algorithmStandardController.get('/algorithm-info/{algorithm_id}')
async def get_algorithm_full_info(
    algorithm_id: str,
    platform: str = "KS968"
):
    """
    获取算法完整信息（包含元数据和配置）

    :param algorithm_id: 算法ID
    :param platform: 平台类型，默认KS968
    :return: 算法完整信息
    """
    try:
        algorithm_info = AlgorithmConfigService.get_algorithm_full_info(algorithm_id, platform)
        if not algorithm_info:
            return ResponseUtil.error(msg=f"未找到算法 {algorithm_id} 的信息")

        return ResponseUtil.success(data=algorithm_info)

    except Exception as e:
        logger.error(f"获取算法完整信息失败: {e}")
        return ResponseUtil.error(msg="获取算法信息失败")


@algorithmStandardController.get('/algorithms-with-info')
async def get_algorithms_with_info(platform: str = "KS968"):
    """
    获取所有算法及其完整信息（包含renderParams）

    :param platform: 平台类型，默认KS968
    :return: 算法信息列表
    """
    try:
        algorithm_ids = AlgorithmConfigService.list_available_algorithms(platform)
        algorithms_info = []

        for algorithm_id in algorithm_ids:
            # 获取基本信息
            algorithm_info = AlgorithmConfigService.get_algorithm_full_info(algorithm_id, platform)
            if algorithm_info:
                # 添加渲染配置
                render_params = AlgorithmConfigService.get_algorithm_render_config(algorithm_id, platform)
                algorithm_info['render_params'] = render_params or {}

                # 添加默认参数
                default_params = AlgorithmConfigService.get_algorithm_default_params(algorithm_id, platform)
                algorithm_info['default_params'] = default_params or {}

                algorithms_info.append(algorithm_info)

        logger.info(f"获取算法信息列表成功，共{len(algorithms_info)}个算法")
        return ResponseUtil.success(data=algorithms_info)

    except Exception as e:
        logger.error(f"获取算法信息列表失败: {e}")
        return ResponseUtil.error(msg="获取算法信息列表失败")
